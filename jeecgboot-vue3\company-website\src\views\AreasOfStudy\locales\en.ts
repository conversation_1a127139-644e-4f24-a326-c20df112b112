export default {
  areasOfStudy: {
    hero: {
      title: 'We have over 2,000 professional editors and translators in more than 400 subject areas',
      description:
        "We have editorial experts in every major academic field, all with advanced degrees from leading US universities, including world-renowned institutions such as Harvard, MIT, Yale, and Stanford. Below are the subspecialties we can cover in each field. If you don't see your subject area on the list, please contact our online customer service or call our pre-sales phone: 400-6291770, or email us at: <EMAIL>.",
      imageAlt: 'Subject Areas Icon',
    },
    disciplines: {
      clinicalMedicine: {
        title: 'Clinical Medicine',
        description:
          'We have editorial experts across all areas of clinical medicine with advanced degrees and extensive professional or research experience in clinical medicine.',
        intro:
          'We have editorial experts across all areas of clinical medicine with advanced degrees and extensive professional or research experience in clinical medicine.',
      },
      lifeSciences: {
        title: 'Life Sciences',
        description:
          'We have editorial experts across all areas of life sciences with advanced degrees and extensive professional or research experience in the field.',
        intro:
          'We have editorial experts across all areas of life sciences with advanced degrees and extensive professional or research experience in the field.',
      },
      physicalSciences: {
        title: 'Physical Sciences',
        description:
          'We have editorial experts across all areas of physical sciences with advanced degrees and extensive professional or research experience in the field.',
        intro:
          'We have editorial experts across all areas of physical sciences with advanced degrees and extensive professional or research experience in the field.',
      },
      mathComputer: {
        title: 'Mathematics and Computer Science',
        description:
          'We have editorial experts across all areas of mathematics and computer science with advanced degrees and extensive professional or research experience in the field.',
        intro:
          'We have editorial experts across all areas of mathematics and computer science with advanced degrees and extensive professional or research experience in the field.',
      },
      engineeringMaterials: {
        title: 'Engineering and Materials Science',
        description:
          'We have editorial experts across all areas of engineering and materials science with advanced degrees and extensive professional or research experience in the field.',
        intro:
          'We have editorial experts across all areas of engineering and materials science with advanced degrees and extensive professional or research experience in the field.',
      },
      humanitiesSocial: {
        title: 'Humanities and Social Sciences',
        description:
          'We have editorial experts across all areas of humanities and social sciences with advanced degrees and extensive professional or research experience in the field.',
        intro:
          'We have editorial experts across all areas of humanities and social sciences with advanced degrees and extensive professional or research experience in the field.',
      },
      businessLaw: {
        title: 'Business and Law',
        description:
          'We have editorial experts across all areas of business and law with advanced degrees and extensive professional or research experience in the field.',
        intro:
          'We have editorial experts across all areas of business and law with advanced degrees and extensive professional or research experience in the field.',
      },
    },
    subspecialties: {
      // Clinical Medicine
      generalMedicine: {
        title: 'General Medicine',
        items: {
          allergy: 'Allergy and Immunologic Diseases',
          criticalCare: 'Critical Care and Emergency Medicine',
          dentistry: 'Dentistry',
          dermatology: 'Dermatology',
          generalPractice: 'General Practice',
          hospitalMedicine: 'Hospital Medicine',
          medicalGenetics: 'Medical Genetics',
          nutrition: 'Nutrition and Dietetics',
          obstetrics: 'Obstetrics and Gynecology',
          ophthalmology: 'Ophthalmology',
          pediatrics: 'Pediatrics',
          physicalMedicine: 'Physical Medicine and Rehabilitation',
          physiology: 'Physiology',
          psychiatry: 'Psychiatry',
          sportsMedicine: 'Sports Medicine and Kinesiology',
        },
      },
      diagnosticSpecialties: {
        title: 'Diagnostic Specialties',
        items: {
          audiology: 'Audiology and Speech-Language Pathology',
          laboratoryDiagnostics: 'Laboratory Diagnostics',
          nuclearMedicine: 'Nuclear Medicine and Medical Imaging',
          pathology: 'Pathology',
          physiology: 'Physiology',
        },
      },
      internalMedicine: {
        title: 'Internal Medicine Specialties',
        items: {
          anesthesiology: 'Anesthesiology and Pain Medicine',
          cardiology: 'Cardiology and Cardiovascular System',
          endocrinology: 'Endocrinology and Metabolism',
          gastroenterology: 'Gastroenterology and Hepatology',
          geriatrics: 'Geriatrics and Gerontology',
          hematology: 'Hematology',
          infectiousDiseases: 'Infectious Diseases',
          maternalFetal: 'Maternal-Fetal Medicine',
          neurology: 'Neurology',
          nuclearMedicine: 'Nuclear Medicine and Medical Imaging',
          oncology: 'Oncology',
          orthopedics: 'Orthopedics',
          otolaryngology: 'Otolaryngology',
          pulmonology: 'Pulmonology',
          rheumatology: 'Rheumatology',
          reproductiveMedicine: 'Sexual and Reproductive Medicine',
          urology: 'Urology and Nephrology',
          vascularMedicine: 'Vascular Medicine',
        },
      },
      publicHealth: {
        title: 'Public Health',
        items: {
          epidemiology: 'Epidemiology',
          tropicalMedicine: 'Tropical Medicine',
          occupationalMedicine: 'Occupational Medicine',
          healthEconomics: 'Health Economics and Outcomes Research',
          preventiveMedicine: 'Preventive Medicine',
          statisticalEpidemiology: 'Statistical Epidemiology',
        },
      },
      surgery: {
        title: 'Surgery',
        items: {
          cardiothoracicSurgery: 'Cardiothoracic Surgery',
          gastrointestinalSurgery: 'Gastrointestinal Surgery',
          generalSurgery: 'General Surgery',
          headNeckSurgery: 'Head and Neck Surgery',
          hepatobiliary: 'Hepatobiliary and Transplant Surgery',
          neurosurgery: 'Neurosurgery',
          orthopedicSurgery: 'Orthopedic Surgery',
          plasticSurgery: 'Plastic and Reconstructive Surgery',
          gynecologicSurgery: 'Gynecologic Surgery',
        },
      },
      interdisciplinaryMedicine: {
        title: 'Interdisciplinary Medicine',
        items: {
          anesthesiology: 'Anesthesiology and Pain Medicine',
          biomedicalEngineering: 'Biomedical Engineering',
          clinicalPharmacology: 'Clinical Pharmacology',
          forensicMedicine: 'Forensic Medicine',
          integrativeMedicine: 'Integrative and Complementary Medicine',
          medicalEthics: 'Medical Ethics',
          medicalInformatics: 'Medical Informatics',
          medicalPhysics: 'Medical Physics',
          nuclearMedicine: 'Nuclear Medicine and Medical Imaging',
          nursing: 'Nursing',
          nutrition: 'Nutrition and Dietetics',
          personalizedMedicine: 'Personalized Medicine',
          translationalMedicine: 'Translational Medicine',
        },
      },

      // Life Sciences
      agriculturalSciences: {
        title: 'Agricultural Sciences',
        items: {
          agriculturalEconomics: 'Agricultural Economics',
          agriculturalEngineering: 'Agricultural Engineering',
          agroecology: 'Agroecology',
          agronomy: 'Agronomy',
          animalScience: 'Animal Science',
          aquaculture: 'Aquaculture',
          foodScience: 'Food Science',
          forestry: 'Forestry',
          horticulture: 'Horticulture',
        },
      },
      biochemistry: {
        title: 'Biochemistry',
        items: {
          analyticalBiochemistry: 'Analytical Biochemistry',
          appliedBiochemistry: 'Applied Biochemistry',
          biochemicalMethods: 'Biochemical Methods',
          biophysics: 'Biophysics',
          generalBiochemistry: 'General Biochemistry',
          structuralBiology: 'Structural Biology',
        },
      },
      cellBiology: {
        title: 'Cell Biology',
        items: {
          cellTissueEngineering: 'Cell and Tissue Engineering',
          cellCommunication: 'Cell Communication',
          cellCycle: 'Cell Cycle',
          cellMigration: 'Cell Migration',
          cellSurvival: 'Cell Survival',
          cellMetabolism: 'Cell Metabolism',
          generalCellBiology: 'General Cell Biology',
          immunology: 'Immunology',
          matrixBiology: 'Matrix Biology',
          stemCells: 'Stem Cells',
        },
      },
      environmentEcology: {
        title: 'Environment and Ecology',
        items: {
          agroecology: 'Agroecology',
          behavioralEcology: 'Behavioral Ecology',
          biogeography: 'Biogeography',
          conservationBiology: 'Conservation Biology',
          ecologicalModeling: 'Ecological Modeling',
          environmentalEngineering: 'Environmental Engineering',
          environmentalPolicy: 'Environmental Policy',
          forestry: 'Forestry',
          marineEcology: 'Marine Ecology',
          populationBiology: 'Population Biology',
          renewableResources: 'Renewable Resources',
          terrestrialEcology: 'Terrestrial Ecology',
          wildlifeBiology: 'Wildlife Biology',
        },
      },
      evolutionDevelopment: {
        title: 'Evolution and Development',
        items: {
          developmentalBiology: 'Developmental Biology',
          evolutionaryBiology: 'Evolutionary Biology',
          evoDevo: 'Evolutionary Developmental Biology',
          stemCells: 'Stem Cells',
        },
      },
      interdisciplinaryBiology: {
        title: 'Interdisciplinary Biology',
        items: {
          mathematicalBiology: 'Mathematical Biology',
          bioinformatics: 'Bioinformatics',
          biomedicalEngineering: 'Biomedical Engineering',
          biophysics: 'Biophysics',
          biopolymers: 'Biopolymers',
          biostatistics: 'Biostatistics',
          biotechnology: 'Biotechnology',
          cancerBiology: 'Cancer Biology',
          chemicalBiology: 'Chemical Biology',
          computationalBiology: 'Computational Biology',
          systemsBiology: 'Systems Biology',
        },
      },
      microbiology: {
        title: 'Microbiology',
        items: {
          appliedMicrobiology: 'Applied Microbiology',
          bacteriology: 'Bacteriology',
          generalMicrobiology: 'General Microbiology',
          mycology: 'Mycology',
          parasitology: 'Parasitology',
          virology: 'Virology',
        },
      },
      molecularBiology: {
        title: 'Molecular Biology',
        items: {
          cellCommunication: 'Cell Communication',
          epigenetics: 'Epigenetics',
          evolutionaryGenetics: 'Evolutionary Genetics',
          medicalGenetics: 'Medical Genetics',
          molecularBiology: 'Molecular Biology',
          molecularEpidemiology: 'Molecular Epidemiology',
          genetics: 'Genetics',
          populationGenetics: 'Population Genetics',
        },
      },
      neuroscience: {
        title: 'Neuroscience',
        items: {
          cellularNeuroscience: 'Cellular Neuroscience',
          cognitiveNeuroscience: 'Cognitive Neuroscience',
          computationalNeuroscience: 'Computational Neuroscience',
          developmentalNeuroscience: 'Developmental Neuroscience',
          diseaseNeurobiology: 'Disease Neurobiology',
        },
      },
      pharmacologyToxicology: {
        title: 'Pharmacology and Toxicology',
        items: {
          clinicalPharmacology: 'Clinical Pharmacology',
          drugDelivery: 'Drug Delivery',
          drugDiscovery: 'Drug Discovery',
          pharmacodynamics: 'Pharmacodynamics',
          pharmacokinetics: 'Pharmacokinetics',
          toxicology: 'Toxicology',
          vaccineDevelopment: 'Vaccine Development',
        },
      },
      zoologyPlantBiology: {
        title: 'Zoology and Plant Biology',
        items: {
          animalBehavior: 'Animal Behavior',
          animalPhysiology: 'Animal Physiology',
          botany: 'Botany',
          entomology: 'Entomology',
          marineBiology: 'Marine Biology',
          plantMolecularBiology: 'Plant Molecular Biology',
          plantPhysiology: 'Plant Physiology',
          taxonomy: 'Taxonomy',
          wildlifeBiology: 'Wildlife Biology',
        },
      },
      veterinaryMedicine: {
        title: 'Veterinary Medicine',
        items: {
          internalMedicine: 'Internal Medicine',
          largeAnimalMedicine: 'Large Animal Medicine',
          smallAnimalMedicine: 'Small Animal Medicine',
          surgery: 'Surgery',
          veterinaryEpidemiology: 'Veterinary Epidemiology',
          zoonoses: 'Zoonoses',
        },
      },

      // Physical Sciences
      chemistry: {
        title: 'Chemistry',
        items: {
          agrochemicals: 'Agrochemicals',
          analyticalChemistry: 'Analytical Chemistry',
          bioinorganicChemistry: 'Bioinorganic Chemistry',
          biochemistry: 'Biochemistry',
          biopolymers: 'Biopolymers',
          catalysis: 'Catalysis',
          cementChemistry: 'Cement Chemistry',
          computationalChemistry: 'Computational Chemistry',
          crystallography: 'Crystallography',
          electrochemistry: 'Electrochemistry',
          environmentalChemistry: 'Environmental Chemistry',
          foodChemistry: 'Food Chemistry',
          inorganicChemistry: 'Inorganic Chemistry',
          massSpectrometry: 'Mass Spectrometry',
          materialChemistry: 'Material Chemistry',
          medicinalChemistry: 'Medicinal Chemistry',
          nanoscience: 'Nanoscience',
          naturalProducts: 'Natural Products',
          nuclearChemistry: 'Nuclear Chemistry',
          organicChemistry: 'Organic Chemistry',
          photonics: 'Photonics',
          physicalChemistry: 'Physical Chemistry',
          polymerScience: 'Polymer Science',
          spectroscopy: 'Spectroscopy',
          surfaceChemistry: 'Surface Chemistry',
        },
      },
      physics: {
        title: 'Physics',
        items: {
          atomicPhysics: 'Atomic Physics',
          biophysics: 'Biophysics',
          computationalPhysics: 'Computational Physics',
          electronicsPhysics: 'Electronics Physics',
          condensedMatter: 'Condensed Matter',
          highEnergyPhysics: 'High Energy Physics',
          magnetism: 'Magnetism',
          nanoscience: 'Nanoscience',
          nuclearPhysics: 'Nuclear Physics',
          optics: 'Optics',
          plasmaPhysics: 'Plasma Physics',
          scattering: 'Scattering',
          softMatter: 'Soft Matter',
          theoreticalPhysics: 'Theoretical Physics',
          thermodynamics: 'Thermodynamics',
        },
      },
      paleontology: {
        title: 'Paleontology',
        items: {
          paleobotany: 'Paleobotany',
          paleoecology: 'Paleoecology',
          paleozoology: 'Paleozoology',
          geology: 'Geology',
        },
      },
      spaceScience: {
        title: 'Space Science',
        items: {
          astrobiology: 'Astrobiology',
          astrochemistry: 'Astrochemistry',
          astronomy: 'Astronomy',
          astrophysics: 'Astrophysics',
          cometResearch: 'Comet Research',
          meteorology: 'Meteorology',
          planetaryScience: 'Planetary Science',
          spaceExploration: 'Space Exploration',
          theoreticalAstrophysics: 'Theoretical Astrophysics',
        },
      },
      earthSciences: {
        title: 'Earth Sciences',
        items: {
          atmosphericScience: 'Atmospheric Science',
          climateAnalysis: 'Climate Analysis',
          climatology: 'Climatology',
          economicGeology: 'Economic Geology',
          geochemistry: 'Geochemistry',
          gis: 'Geographic Information Systems',
          geology: 'Geology',
          geomorphology: 'Geomorphology',
          geophysics: 'Geophysics',
          glaciology: 'Glaciology',
          historicalGeology: 'Historical Geology',
          hydrology: 'Hydrology',
          meteorology: 'Meteorology',
          oceanography: 'Oceanography',
          petroleumGeology: 'Petroleum Geology',
          petrology: 'Petrology',
          physicalGeography: 'Physical Geography',
          planetaryGeology: 'Planetary Geology',
          seismology: 'Seismology',
          volcanology: 'Volcanology',
        },
      },

      // Mathematics and Computer Science
      computerScience: {
        title: 'Computer Science',
        items: {
          artificialIntelligence: 'Artificial Intelligence',
          computerArchitecture: 'Computer Architecture',
          gis: 'Geographic Information Systems',
          graphicsSystems: 'Graphics Systems',
          informationRetrieval: 'Information Retrieval',
          informationTheory: 'Information Theory',
          numericalAnalysis: 'Numerical Analysis',
          robotics: 'Robotics',
          medicalEngineering: 'Medical Engineering',
          systemsNetworks: 'Systems and Networks',
          theoreticalCS: 'Theoretical Computer Science',
        },
      },
      mathematics: {
        title: 'Mathematics',
        items: {
          algebra: 'Algebra',
          analysis: 'Analysis',
          appliedMathematics: 'Applied Mathematics',
          computationalBiology: 'Computational Biology',
          computationalMathematics: 'Computational Mathematics',
          discreteMathematics: 'Discrete Mathematics',
          financialMathematics: 'Financial Mathematics',
          geometry: 'Geometry',
          logic: 'Logic',
          mathematicalPhysics: 'Mathematical Physics',
          numericalAnalysis: 'Numerical Analysis',
          pureMathematics: 'Pure Mathematics',
          topology: 'Topology',
        },
      },
      statistics: {
        title: 'Statistics',
        items: {
          appliedStatistics: 'Applied Statistics',
          biostatistics: 'Biostatistics',
          decisionScience: 'Decision Science',
          operationsResearch: 'Operations Research',
          statisticalEpidemiology: 'Statistical Epidemiology',
          statisticalTheory: 'Statistical Theory',
        },
      },

      // Engineering and Materials Science
      engineering: {
        title: 'Engineering',
        items: {
          acoustics: 'Acoustics',
          aeronautics: 'Aeronautics',
          agriculturalEngineering: 'Agricultural Engineering',
          biomedicalEngineering: 'Biomedical Engineering',
          biotechnology: 'Biotechnology',
          cellTissueEngineering: 'Cell and Tissue Engineering',
          chemicalEngineering: 'Chemical Engineering',
          civilEngineering: 'Civil Engineering',
          computerArchitecture: 'Computer Architecture',
          electricalEngineering: 'Electrical Engineering',
          energyEngineering: 'Energy Engineering',
          environmentalEngineering: 'Environmental Engineering',
          industrialEngineering: 'Industrial Engineering',
          materialEngineering: 'Material Engineering',
          mechanicalEngineering: 'Mechanical Engineering',
          nuclearEngineering: 'Nuclear Engineering',
          oceanEngineering: 'Ocean Engineering',
          petroleumEngineering: 'Petroleum Engineering',
          robotics: 'Robotics',
          softwareEngineering: 'Software Engineering',
          systemsEngineering: 'Systems Engineering',
        },
      },
      materialScience: {
        title: 'Material Science',
        items: {
          biomaterials: 'Biomaterials',
          ceramics: 'Ceramics',
          electronicMaterials: 'Electronic Materials',
          magneticMaterials: 'Magnetic Materials',
          materialChemistry: 'Material Chemistry',
          materialEngineering: 'Material Engineering',
          materialTheory: 'Material Theory',
          metallurgy: 'Metallurgy',
          nanoscience: 'Nanoscience',
          opticalMaterials: 'Optical Materials',
          polymerScience: 'Polymer Science',
        },
      },

      // Humanities and Social Sciences
      education: {
        title: 'Education',
        items: {
          educationalPhilosophy: 'Educational Philosophy',
          educationalPsychology: 'Educational Psychology',
          schoolCounseling: 'School Counseling',
          specialEducation: 'Special Education',
        },
      },
      artsHumanities: {
        title: 'Arts and Humanities',
        items: {
          architecture: 'Architecture',
          artHistory: 'Art History',
          behavioralGeography: 'Behavioral Geography',
          classicalStudies: 'Classical Studies',
          culturalStudies: 'Cultural Studies',
          genderStudies: 'Gender Studies',
          history: 'History',
          linguistics: 'Linguistics',
          literature: 'Literature',
          musicology: 'Musicology',
          philosophy: 'Philosophy',
          religiousStudies: 'Religious Studies',
          theaterStudies: 'Theater Studies',
          womensStudies: "Women's Studies",
        },
      },
      communication: {
        title: 'Communication',
        items: {
          culturalStudies: 'Cultural Studies',
          journalism: 'Journalism',
          mediaStudies: 'Media Studies',
          publicRelations: 'Public Relations',
          publishing: 'Publishing',
          scienceCommunication: 'Science Communication',
          technicalCommunication: 'Technical Communication',
        },
      },
      publicPolicy: {
        title: 'Public Policy',
        items: {
          agriculturalPolicy: 'Agricultural Policy',
          urbanManagement: 'Urban Management',
          environmentalPolicy: 'Environmental Policy',
          healthPolicy: 'Health Policy',
          internationalRelations: 'International Relations',
          otherPublicPolicy: 'Other Public Policy',
          publicAdministration: 'Public Administration',
          socialPolicy: 'Social Policy',
        },
      },
      informationManagement: {
        title: 'Information Management',
        items: {
          informationRetrieval: 'Information Retrieval',
          libraryScience: 'Library Science',
        },
      },
      socialBehavioral: {
        title: 'Social and Behavioral Sciences',
        items: {
          economics: 'Economics',
          anthropology: 'Anthropology',
          archaeology: 'Archaeology',
          behavioralGeography: 'Behavioral Geography',
          behavioralEconomics: 'Behavioral Economics',
          criminology: 'Criminology',
          marketing: 'Marketing',
          nursing: 'Nursing',
          socialWork: 'Social Work',
          sociology: 'Sociology',
          urbanStudies: 'Urban Studies',
        },
      },

      // Business and Law
      business: {
        title: 'Business',
        items: {
          accounting: 'Accounting',
          entrepreneurship: 'Entrepreneurship',
          finance: 'Finance',
          hospitalityTourism: 'Hospitality and Tourism',
          internationalBusiness: 'International Business',
          leadership: 'Leadership',
          management: 'Management',
          marketing: 'Marketing',
          otherBusiness: 'Other Business',
          publicRelations: 'Public Relations',
        },
      },
      law: {
        title: 'Law',
        items: {
          administrativeLaw: 'Administrative Law',
          bankingLaw: 'Banking Law',
          businessLaw: 'Business Law',
          civilRights: 'Civil Rights',
          constitutionalLaw: 'Constitutional Law',
          criminalLaw: 'Criminal Law',
          environmentalLaw: 'Environmental Law',
          familyLaw: 'Family Law',
          healthLaw: 'Health Law',
          immigrationLaw: 'Immigration Law',
          intellectualProperty: 'Intellectual Property',
          internationalLaw: 'International Law',
          taxLaw: 'Tax Law',
        },
      },
    },
    servicesRecommendation: {
      title: 'We provide high-quality paper editing and manuscript preparation services for all disciplines',
      services: {
        editing: {
          title: 'English Editing',
          description:
            "Our standard editing service meets authors' needs for grammar, word choice, and other aspects, ensuring manuscripts meet journal requirements for English language; our premium editing service provides unlimited free re-editing within 3 years, providing the most comprehensive guarantee for successful paper publication.",
          price: 'Price: Starting from ¥331.48',
          buttonText: 'Order Now',
        },
        vipEditing: {
          title: 'Scientific Review Editing',
          description:
            "AJE's Scientific Review Editing is a service built on English editing that provides comprehensive revision suggestions for research scholars in terms of manuscript structure, scientific logic, experimental processes, research details, etc. Using this service can help researchers reduce the risk of manuscript rejection and improve acceptance rates at target journals.",
          price: 'Price: Starting from ¥3,216.68',
          buttonText: 'Order Now',
        },
        scientificEditing: {
          title: 'Scientific Editing',
          description:
            'Scientific Editing is provided by a professional team composed of AJE members who have served as editors of top journals, providing you with in-depth scientific editing services. By purchasing this service, you will receive expert-level in-depth editing and strategic advice from an editorial team that follows top journal quality standards. This service includes premium English editing, developmental editing, quality assurance editing, and a summary report.',
          price: 'Price: Starting from ¥10,746.14',
          buttonText: 'Order Now',
        },
        presubmissionReview: {
          title: 'Presubmission Review',
          description:
            'Understand how experts in your field evaluate your manuscript before submitting it to a journal, and prepare for peer review; also get deeper comments and suggestions beyond language aspects to improve manuscript acceptance rates. Our editorial experts will add in-depth comments to your manuscript, check the overall presentation of research focus, the logic of information in various parts of the manuscript, and provide suggestions.',
          price: '¥2,070.42',
          buttonText: 'Order Now',
        },
        translation: {
          title: 'Academic Translation',
          description:
            'Unlike ordinary translation services, AJE has a senior translation and editing team with research backgrounds in your field, able to combine professional academic translation with high-quality English editing, allowing your manuscript to directly reach international journal publication standards. Purchasing this service includes free re-editing and one free translation of up to 1,500 new words.',
          price: 'Price: Starting from ¥2,095.50',
          buttonText: 'Order Now',
        },
        journalRecommendation: {
          title: 'Journal Recommendation',
          description:
            'By purchasing our journal recommendation service, you will receive a detailed report with three recommended journals based on your research content, scope, impact, and publication goals. The report includes detailed information such as the scope and impact factor of each recommended journal, how well your research matches it, and how the journal meets your submission requirements.',
          price: 'Price: ¥967.15',
          buttonText: 'Order Now',
        },
      },
    },
    cta: {
      title: 'How to Contact Us and Get More Service Information?',
      description:
        "You can contact us through email, website online customer service, pre-sales phone: 400-6291770. We have customer service teams in both the United States and China. During China's working hours, Beijing office customer service will serve you; during non-China working hours, the US customer team will serve you (Note: Please send <NAME_EMAIL> during non-working hours).",
      orderNow: 'Order Now',
      quickQuote: 'Get Quote',
    },
  },
};
