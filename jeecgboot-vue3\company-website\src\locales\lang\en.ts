import { genMessage } from '../helper';
import antdLocale from 'ant-design-vue/es/locale/en_US';
//import momentLocale from 'moment/dist/locale/eu';

const modules = import.meta.glob('./en/**/*.ts', { eager: true });
export default {
  message: {
    ...genMessage(modules as Recordable<Recordable>, 'en'),
    antdLocale,
    // Global error messages
    error: {
      404: 'Page not found',
      500: 'Server error',
      network: 'Network connection failed',
      timeout: 'Request timeout',
    },
    // Researcher page
    researcher: {
      promotion: {
        text: 'Research Sprint Season! Help you overtake in summer! Before August 31st, new customers use code【ACCEPT25】for 15% off standard/premium editing! Returning customers use code【BG25M】for 10% off standard/premium editing! AJE Academic Package, 20% off combo services! Available for both new and returning customers. See【Promotions】page for details.',
      },
      welcome: {
        title: 'Welcome back',
      },
      incompleteOrder: {
        text: 'You have an incomplete order that you can',
        continue: 'continue when convenient',
      },
      orders: {
        title: 'My Orders',
        empty: 'Your search returned no results.',
        submit: 'Submit Manuscript',
        orderId: 'Order ID',
        submitDate: 'Submit Date',
        viewDetails: 'View Details',
        price: 'Price',
        total: '{count} orders in total',
        serviceTypes: {
          standard: 'Standard Editing',
          premium: 'Premium Editing',
          scientific: 'Scientific Review Editing',
          editorial: 'Scientific Editorial',
          translation: 'Academic Translation',
        },
        status: {
          pending: 'Pending',
          processing: 'Processing',
          completed: 'Completed',
          cancelled: 'Cancelled',
        },
        pagination: {
          total: 'Showing {start} to {end} of {total} entries',
        },
      },
      referral: {
        title: 'Referral Rewards',
        description:
          'Simply send your exclusive referral link to friends. When your friends register for an AJE account through your referral link, they automatically receive ¥286.56 off their order (minimum order $100). After your friend registers and completes their first order, you will also receive ¥286.56 credit for your next order (valid for one year). If you have any questions, please contact our customer service team.',
        copy: 'Copy',
        viewReferrals: 'View My Referrals',
      },
      membership: {
        title: 'Become a Member',
        description: 'Once you reach premium membership, enjoy great discounts on every order.',
        level: 'Basic Member',
        points: 'Points Earned',
        viewBenefits: 'View My Benefits',
      },
      teamCode: {
        title: 'Enter Team Discount Code',
        description: 'If you have a group discount code, please enter it here.',
        placeholder: 'Enter team code',
        submit: 'Submit',
      },
      messages: {
        viewOrderDetails: 'View order details feature is under development',
        copySuccess: 'Referral link copied to clipboard',
        copyError: 'Copy failed, please copy manually',
        teamCodeRequired: 'Please enter team discount code',
        teamCodeSubmitted: 'Team discount code submitted successfully',
      },
    },
  },
  dateLocale: null,
  dateLocaleName: 'en',
};
