import { genMessage } from '../helper';
import antdLocale from 'ant-design-vue/es/locale/zh_CN';

const modules = import.meta.glob('./zh-CN/**/*.ts', { eager: true });
export default {
  message: {
    ...genMessage(modules as Recordable<Recordable>, 'zh-CN'),
    antdLocale,
    // 全局错误信息
    error: {
      404: '页面未找到',
      500: '服务器错误',
      network: '网络连接失败',
      timeout: '请求超时',
    },
    // 研究者页面
    researcher: {
      promotion: {
        text: '科研冲刺季！助你暑期弯道超车！8月31日前，新客户首单使用代码【ACCEPT25】，标准/高级润色享85折优惠！老客户复购使用代码【BG25M】，标准/高级润色享9折优惠！AJE学术套餐，组合服务8折优惠！新老客户均可使用。详情见【优惠活动】页面',
      },
      welcome: {
        title: '欢迎回来',
      },
      incompleteOrder: {
        text: '您有一个未完成创建的订单，您可以在方便时',
        continue: '继续下单',
      },
      orders: {
        title: '我的订单',
        empty: '您的搜索没有返回任何结果。',
        submit: '提交文稿',
        orderId: '订单号',
        submitDate: '提交日期',
        viewDetails: '查看详情',
        price: '价格',
        total: '共 {count} 个订单',
        serviceTypes: {
          standard: '标准润色',
          premium: '高级润色',
          scientific: '科学评审编辑',
          editorial: '论文科学编辑',
          translation: '学术论文翻译',
        },
        status: {
          pending: '待处理',
          processing: '处理中',
          completed: '已完成',
          cancelled: '已取消',
        },
        pagination: {
          total: '显示第 {start} 到第 {end} 条记录，共 {total} 条',
        },
      },
      referral: {
        title: '推荐奖励',
        description:
          '您只需将您的推荐专属链接发送给朋友，您的朋友通过您的推荐链接注册AJE账号，即可在下单时(订单金额需100美元以上)自动获得 ¥286.56 的优惠。在您的朋友注册并完成首单之后，您也会获得下一次订单 ¥286.56 的积分优惠（一年有效）。如有任何问题，欢迎咨询我们的客服团队。',
        copy: '复制',
        viewReferrals: '查看我的推荐',
      },
      membership: {
        title: '成为会员',
        description: '到达高级会员后，每笔订单均可享受超值优惠。',
        level: '初级会员',
        points: '获得的积点',
        viewBenefits: '查看我的福利',
      },
      teamCode: {
        title: '请输入团队优惠代码',
        description: '如果您有团体优惠代码，请在这里输入该代码。',
        placeholder: '请输入团队代码',
        submit: '提交',
      },
      messages: {
        viewOrderDetails: '查看订单详情功能正在开发中',
        copySuccess: '推荐链接已复制到剪贴板',
        copyError: '复制失败，请手动复制',
        teamCodeRequired: '请输入团队优惠代码',
        teamCodeSubmitted: '团队优惠代码提交成功',
      },
    },
  },
};
