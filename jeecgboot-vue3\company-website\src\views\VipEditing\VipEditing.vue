<template>
  <div class="vip-editing-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('vipEditing.hero.title') }}</h1>
          <p class="hero-description">{{ t('vipEditing.hero.description') }}</p>
          <div class="hero-actions">
            <a-button type="primary" size="large" @click="handleOrderClick">
              {{ t('vipEditing.hero.orderNow') }}
            </a-button>
            <a-button size="large" @click="handleQuickQuote">
              {{ t('vipEditing.hero.quickQuote') }}
            </a-button>
          </div>
        </div>
        <div class="hero-image">
          <img src="./img/vip-editing-hero.png" :alt="t('vipEditing.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 统计数据区域 -->
    <section class="stats-section">
      <div class="stats-content">
        <div class="section-header">
          <h2>{{ t('vipEditing.stats.title') }}</h2>
        </div>
        <div class="stats-grid">
          <div class="stat-item" v-for="stat in statsData" :key="stat.key">
            <div class="stat-value">{{ t(`vipEditing.stats.items.${stat.key}.value`) }}</div>
            <div class="stat-label">{{ t(`vipEditing.stats.items.${stat.key}.label`) }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 服务内容介绍 -->
    <section class="service-content-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('vipEditing.serviceContent.title') }}</h2>
        </div>
        <div class="content-description">
          <p>{{ t('vipEditing.serviceContent.description') }}</p>
          <div class="content-features">
            <div class="feature-item" v-for="feature in serviceContentFeatures" :key="feature.key">
              <div class="feature-icon">
                <component :is="feature.icon" />
              </div>
              <p>{{ t(`vipEditing.serviceContent.features.${feature.key}`) }}</p>
            </div>
          </div>
        </div>
        <div class="content-image">
          <img src="./img/presubmission-review.png" :alt="t('vipEditing.serviceContent.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 权威认证区域 -->
    <section class="authority-section">
      <div class="authority-content">
        <div class="section-header">
          <h2>{{ t('vipEditing.authority.title') }}</h2>
          <p>{{ t('vipEditing.authority.description') }}</p>
        </div>
        <div class="authority-logos">
          <div class="logo-item" v-for="logo in authorityLogos" :key="logo.name">
            <img :src="logo.src" :alt="logo.name" />
          </div>
        </div>
      </div>
    </section>

    <!-- 服务特点 -->
    <section class="features-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('vipEditing.features.title') }}</h2>
        </div>
        <div class="features-grid">
          <div class="feature-card" v-for="feature in serviceFeatures" :key="feature.key">
            <div class="feature-icon">
              <img :src="feature.icon" :alt="feature.key" />
            </div>
            <h4>{{ t(`vipEditing.features.items.${feature.key}.title`) }}</h4>
            <p>{{ t(`vipEditing.features.items.${feature.key}.description`) }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 编辑团队介绍 -->
    <section class="editor-team-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('vipEditing.editorTeam.title') }}</h2>
        </div>
        <div class="editors-grid">
          <div class="editor-card" v-for="editor in editorTeam" :key="editor.name">
            <div class="editor-avatar">
              <img :src="editor.avatar" :alt="editor.name" />
            </div>
            <div class="editor-info">
              <h4>{{ editor.name }}</h4>
              <p class="editor-title">{{ t(`vipEditing.editorTeam.editors.${editor.key}.title`) }}</p>
              <p class="editor-quote">{{ t(`vipEditing.editorTeam.editors.${editor.key}.quote`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 质量保证 -->
    <section class="quality-guarantee-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('vipEditing.qualityGuarantee.title') }}</h2>
        </div>
        <div class="guarantee-content">
          <div class="guarantee-text">
            <div class="guarantee-features">
              <div class="guarantee-feature" v-for="feature in qualityFeatures" :key="feature.key">
                <div class="feature-icon">
                  <component :is="feature.icon" />
                </div>
                <div class="feature-content">
                  <h4>{{ t(`vipEditing.qualityGuarantee.features.${feature.key}.title`) }}</h4>
                  <p>{{ t(`vipEditing.qualityGuarantee.features.${feature.key}.description`) }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="guarantee-image">
            <img src="./img/quality-guarantee.png" :alt="t('vipEditing.qualityGuarantee.imageAlt')" />
          </div>
        </div>
      </div>
    </section>

    <!-- 相关服务 -->
    <section class="related-services-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('vipEditing.relatedServices.title') }}</h2>
        </div>
        <div class="related-services-grid">
          <div class="service-card" v-for="service in relatedServices" :key="service.key">
            <div class="service-image">
              <img :src="service.image" :alt="service.key" />
            </div>
            <div class="service-content">
              <h4>{{ t(`vipEditing.relatedServices.services.${service.key}.title`) }}</h4>
              <p>{{ t(`vipEditing.relatedServices.services.${service.key}.description`) }}</p>
              <div class="service-price">{{ t(`vipEditing.relatedServices.services.${service.key}.price`) }}</div>
              <a-button type="primary" @click="handleServiceClick(service)">
                {{ t(`vipEditing.relatedServices.services.${service.key}.buttonText`) }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ区域 -->
    <section class="faq-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('vipEditing.faq.title') }}</h2>
          <p>{{ t('vipEditing.faq.subtitle') }}</p>
        </div>
        <a-collapse v-model:activeKey="activeFaqKeys" class="faq-collapse">
          <a-collapse-panel v-for="faq in faqData" :key="faq.key" :header="t(`vipEditing.faq.items.${faq.key}.question`)">
            <div v-html="t(`vipEditing.faq.items.${faq.key}.answer`)"></div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="cta-content">
        <h2>{{ t('vipEditing.cta.title') }}</h2>
        <a-button type="primary" size="large" @click="handleOrderClick">
          {{ t('vipEditing.cta.buttonText') }}
        </a-button>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { CheckOutlined, CheckCircleOutlined, EditOutlined, TeamOutlined, SafetyOutlined, MailOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  statsData,
  serviceContentFeatures,
  authorityLogos,
  serviceFeatures,
  editorTeam,
  qualityFeatures,
  relatedServices,
  faqData
} from './vipEditing.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 响应式数据
const activeFaqKeys = ref(['1'])

// 方法
const handleOrderClick = () => {
  window.open('https://china.aje.com/cn/researcher/submit/get-started', '_blank')
}

const handleQuickQuote = () => {
  router.push('/pricing')
}

const handleServiceClick = (service: any) => {
  window.open(service.link, '_blank')
}

// 生命周期
onMounted(() => {
  console.log('VIP Editing 页面已加载')
})
</script>

<style scoped>
.vip-editing-page {
  min-height: 100vh;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 120px 0 80px;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.hero-actions .ant-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  border-radius: 8px;
}

.hero-image img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 统计数据区域 */
.stats-section {
  padding: 80px 0;
  background: #f8fafc;
}

.stats-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.section-header {
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #4a5568;
  line-height: 1.6;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 48px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.5;
}

/* 服务内容区域 */
.service-content-section {
  padding: 80px 0;
  background: white;
}

.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.content-description {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 60px;
  align-items: center;
  margin-top: 40px;
}

.content-description p {
  font-size: 18px;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 32px;
}

.content-features {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.feature-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  color: #667eea;
  margin-top: 2px;
}

.feature-item p {
  font-size: 16px;
  line-height: 1.6;
  color: #4a5568;
  margin: 0;
}

.content-image img {
  width: 100%;
  height: auto;
  border-radius: 12px;
}

/* 权威认证区域 */
.authority-section {
  padding: 80px 0;
  background: #f8fafc;
}

.authority-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.authority-logos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 40px;
  margin-top: 60px;
  align-items: center;
}

.logo-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
}

.logo-item img {
  max-width: 100%;
  max-height: 60px;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.logo-item:hover img {
  filter: grayscale(0%);
  opacity: 1;
}

/* 服务特点区域 */
.features-section {
  padding: 80px 0;
  background: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: 12px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-card .feature-icon {
  width: 120px;
  height: 120px;
  margin: 0 auto 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-card .feature-icon img {
  width: 100px;
  height: 100px;
}

.feature-card h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.feature-card p {
  font-size: 16px;
  line-height: 1.6;
  color: #4a5568;
}

/* 编辑团队区域 */
.editor-team-section {
  padding: 80px 0;
  background: #f8fafc;
}

.editors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.editor-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.editor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.editor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 24px;
}

.editor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.editor-info h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.editor-title {
  font-size: 14px;
  color: #667eea;
  margin-bottom: 16px;
  font-weight: 500;
}

.editor-quote {
  font-size: 16px;
  line-height: 1.6;
  color: #4a5568;
  font-style: italic;
}

/* 质量保证区域 */
.quality-guarantee-section {
  padding: 80px 0;
  background: white;
}

.guarantee-content {
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  gap: 60px;
  align-items: center;
  margin-top: 60px;
}

.guarantee-features {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.guarantee-feature {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.guarantee-feature .feature-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.guarantee-feature .feature-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.guarantee-feature .feature-content p {
  font-size: 16px;
  line-height: 1.6;
  color: #4a5568;
}

.guarantee-image img {
  width: 100%;
  height: auto;
  border-radius: 12px;
}

/* 相关服务区域 */
.related-services-section {
  padding: 80px 0;
  background: #f8fafc;
}

.related-services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-image {
  height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.service-image img {
  width: 120px;
  height: 120px;
}

.service-content {
  padding: 32px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.service-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.service-content p {
  font-size: 14px;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 16px;
  flex: 1;
}

.service-price {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 20px;
  margin-top: auto;
}

.service-content .ant-btn {
  width: 100%;
  margin-top: auto;
}

/* FAQ区域 */
.faq-section {
  padding: 80px 0;
  background: white;
}

.faq-collapse {
  margin-top: 40px;
  background: transparent;
}

.faq-collapse .ant-collapse-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 16px;
}

.faq-collapse .ant-collapse-header {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  padding: 20px 24px;
}

.faq-collapse .ant-collapse-content-box {
  padding: 0 24px 20px;
  font-size: 14px;
  line-height: 1.6;
  color: #4a5568;
}

/* CTA区域 */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.cta-content h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 32px;
}

.cta-content .ant-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-content h1 {
    font-size: 32px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .content-description {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .authority-logos {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .editors-grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .guarantee-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .related-services-grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-actions .ant-btn {
    width: 200px;
  }
}
</style>
