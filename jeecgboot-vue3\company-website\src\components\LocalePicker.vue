<template>
  <div class="locale-container">
    <a-dropdown placement="bottom">
      <a class="locale-picker" @click.prevent>
        <GlobalOutlined />
        <span class="locale-text">{{ currentLocaleText }}</span>
        <DownOutlined />
      </a>
      <template #overlay>
        <a-menu @click="handleLocaleChange">
          <a-menu-item v-for="locale in locales" :key="locale.key" :class="{ active: locale.key === currentLocale }">
            {{ locale.label }}
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="reset" @click="handleResetCache">
            🔄 重置缓存
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { GlobalOutlined, DownOutlined } from '@ant-design/icons-vue'
import { useLocale } from '@/locales/useLocale'
import { localeList } from '@/settings/localeSetting'

const { getLocale, changeLocale } = useLocale()

const locales = localeList.map(item => ({
  key: item.event as string,
  label: item.text
}))

const currentLocale = computed(() => getLocale.value)

const currentLocaleText = computed(() => {
  const current = localeList.find(item => item.event === currentLocale.value)
  return current?.text || '简体中文'
})

const handleLocaleChange = async ({ key }: { key: string }) => {
  if (key === 'reset') {
    handleResetCache()
    return
  }

  // 切换语言，不刷新页面
  await changeLocale(key as any)
}

const handleResetCache = () => {
  // 清除所有可能的语言缓存
  localStorage.removeItem('LOCALE__');
  localStorage.removeItem('__APP__LOCALE__');
  localStorage.removeItem('COMMON__LOCAL__KEY__');
  sessionStorage.clear();

  console.log('缓存已清除')

  // 重置为默认语言
  changeLocale('zh-CN' as any)
}
</script>

<style scoped>
.locale-picker {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #333;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.locale-picker:hover {
  background-color: #f5f5f5;
  color: #1890ff;
}

.locale-text {
  margin: 0 4px;
  font-size: 14px;
}

:deep(.ant-menu-item.active) {
  color: #1890ff;
  background-color: #e6f7ff;
}
</style>