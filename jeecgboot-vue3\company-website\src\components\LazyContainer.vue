<template>
  <transition-group 
    class="h-full w-full" 
    v-bind="$attrs" 
    ref="elRef" 
    :name="transitionName" 
    :tag="tag" 
    mode="out-in"
  >
    <div key="component" v-if="isInit">
      <slot :loading="loading"></slot>
    </div>
    <div key="skeleton" v-else>
      <slot name="skeleton" v-if="$slots.skeleton"></slot>
      <a-skeleton v-else />
    </div>
  </transition-group>
</template>

<script lang="ts">
import type { PropType } from 'vue';
import { defineComponent, reactive, onMounted, ref, toRef, toRefs } from 'vue';
import { Skeleton } from 'ant-design-vue';
import { useTimeoutFn } from '@/hooks/useTimeout';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

interface State {
  isInit: boolean;
  loading: boolean;
  intersectionObserverInstance: IntersectionObserver | null;
}

const props = {
  /**
   * 等待时间，如果指定了时间，无论是否可见，都会在指定时间后自动加载
   */
  timeout: { type: Number },
  /**
   * 视口的边距，用于提前加载
   */
  threshold: { type: String, default: '0px' },
  /**
   * 滚动方向
   */
  direction: { type: String as PropType<'vertical' | 'horizontal'>, default: 'vertical' },
  /**
   * 最大等待时间
   */
  maxWaitingTime: { type: Number, default: 80 },
  /**
   * 过渡名称
   */
  transitionName: { type: String, default: 'lazy-container' },
  /**
   * 容器标签
   */
  tag: { type: String, default: 'div' },
  /**
   * 视口元素
   */
  viewport: { type: (typeof window !== 'undefined' ? window.HTMLElement : Object) as PropType<HTMLElement> },
};

export default defineComponent({
  name: 'LazyContainer',
  components: { 'a-skeleton': Skeleton },
  inheritAttrs: false,
  props,
  emits: ['init'],
  setup(props, { emit }) {
    const elRef = ref();
    const state = reactive<State>({
      isInit: false,
      loading: false,
      intersectionObserverInstance: null,
    });

    onMounted(() => {
      immediateInit();
      initIntersectionObserver();
    });

    // 如果设置了延迟时间，将立即执行
    function immediateInit() {
      const { timeout } = props;
      timeout &&
        useTimeoutFn(() => {
          init();
        }, timeout);
    }

    function init() {
      state.loading = true;

      useTimeoutFn(() => {
        if (state.isInit) return;
        state.isInit = true;
        emit('init');
      }, props.maxWaitingTime || 80);
    }

    function initIntersectionObserver() {
      const { timeout, direction, threshold } = props;
      if (timeout) return;
      
      // 根据滚动方向构造视口边距，用于提前加载
      let rootMargin = '0px';
      switch (direction) {
        case 'vertical':
          rootMargin = `${threshold} 0px`;
          break;
        case 'horizontal':
          rootMargin = `0px ${threshold}`;
          break;
      }

      try {
        const { stop, observer } = useIntersectionObserver({
          rootMargin,
          target: toRef(elRef.value, '$el'),
          onIntersect: (entries: any[]) => {
            const isIntersecting = entries[0].isIntersecting || entries[0].intersectionRatio;
            if (isIntersecting) {
              init();
              if (observer) {
                stop();
              }
            }
          },
          root: toRef(props, 'viewport'),
        });
      } catch (e) {
        init();
      }
    }
    
    return {
      elRef,
      ...toRefs(state),
    };
  },
});
</script>

<style lang="less" scoped>
.lazy-container-enter-active,
.lazy-container-leave-active {
  transition: opacity 0.3s ease;
}

.lazy-container-enter-from,
.lazy-container-leave-to {
  opacity: 0;
}
</style>
