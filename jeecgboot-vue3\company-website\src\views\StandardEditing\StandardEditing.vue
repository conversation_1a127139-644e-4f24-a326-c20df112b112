<template>
  <div class="standard-editing-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('standardEditing.hero.title') }}</h1>
          <p class="hero-description">{{ t('standardEditing.hero.description') }}</p>
          <div class="hero-actions">
            <a-button type="primary" size="large" @click="handleOrderClick">
              {{ t('standardEditing.hero.orderNow') }}
            </a-button>
            <a-button size="large" @click="handleQuickQuote">
              {{ t('standardEditing.hero.quickQuote') }}
            </a-button>
          </div>
        </div>
        <div class="hero-image">
          <img src="./img/hero-editing.jpg" :alt="t('standardEditing.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 服务介绍 -->
    <section class="service-intro-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('standardEditing.serviceIntro.title') }}</h2>
          <p>{{ t('standardEditing.serviceIntro.subtitle') }}</p>
        </div>
        <div class="intro-content">
          <div class="intro-image">
            <img src="./img/service-intro.jpg" :alt="t('standardEditing.serviceIntro.imageAlt')" />
          </div>
          <div class="intro-features">
            <div class="feature-item" v-for="feature in serviceFeatures" :key="feature.key">
              <div class="feature-icon">
                <component :is="feature.icon" />
              </div>
              <div class="feature-content">
                <h4>{{ t(`standardEditing.serviceIntro.features.${feature.key}.title`) }}</h4>
                <p>{{ t(`standardEditing.serviceIntro.features.${feature.key}.description`) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 价格计算器 -->
    <section class="price-calculator-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('standardEditing.priceCalculator.title') }}</h2>
          <p>{{ t('standardEditing.priceCalculator.subtitle') }}</p>
        </div>
        <div class="calculator-content">
          <div class="word-count-input">
            <label>{{ t('standardEditing.priceCalculator.wordCountLabel') }}</label>
            <a-input-number v-model:value="wordCount" :min="1" :max="40000" :step="100" size="large"
              style="width: 200px" />
            <span class="word-unit">{{ t('standardEditing.priceCalculator.wordUnit') }}</span>
          </div>
          <div class="service-tabs">
            <a-tabs v-model:activeKey="activeServiceTab" centered>
              <a-tab-pane key="editing" :tab="t('standardEditing.priceCalculator.editingTab')">
                <div class="pricing-cards">
                  <div class="pricing-card standard-card">
                    <div class="card-header">
                      <h3>{{ t('standardEditing.services.standard.title') }}</h3>
                      <div class="price">{{ standardPrice }}</div>
                      <div class="delivery-time">
                        {{ t('standardEditing.services.standard.deliveryTime', { days: standardDeliveryDays }) }}
                      </div>
                    </div>
                    <div class="card-content">
                      <ul class="feature-list">
                        <li v-for="feature in standardFeatures" :key="feature">
                          <CheckOutlined class="check-icon" />
                          {{ t(`standardEditing.services.standard.features.${feature}`) }}
                        </li>
                      </ul>
                      <a-button type="primary" block size="large" @click="handleOrderClick('standard')">
                        {{ t('standardEditing.services.orderNow') }}
                      </a-button>
                    </div>
                  </div>

                  <div class="pricing-card premium-card">
                    <div class="card-header">
                      <h3>{{ t('standardEditing.services.premium.title') }}</h3>
                      <div class="price">{{ premiumPrice }}</div>
                      <div class="delivery-time">
                        {{ t('standardEditing.services.premium.deliveryTime', { days: premiumDeliveryDays }) }}
                      </div>
                    </div>
                    <div class="card-content">
                      <ul class="feature-list">
                        <li v-for="feature in premiumFeatures" :key="feature">
                          <CheckOutlined class="check-icon" />
                          {{ t(`standardEditing.services.premium.features.${feature}`) }}
                        </li>
                      </ul>
                      <a-button type="primary" block size="large" @click="handleOrderClick('premium')">
                        {{ t('standardEditing.services.orderNow') }}
                      </a-button>
                    </div>
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
          <div class="calculator-note">
            <p>{{ t('standardEditing.priceCalculator.note') }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 样稿下载 -->
    <section class="sample-downloads-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('standardEditing.sampleDownloads.title') }}</h2>
        </div>
        <div class="sample-tabs">
          <a-tabs v-model:activeKey="activeSampleTab" centered>
            <a-tab-pane key="standard" :tab="t('standardEditing.sampleDownloads.standardTab')">
              <div class="sample-content">
                <h4>{{ t('standardEditing.sampleDownloads.standard.title') }}</h4>
                <p>{{ t('standardEditing.sampleDownloads.standard.description') }}</p>
                <a-button type="primary" @click="downloadSample('standard')">
                  {{ t('standardEditing.sampleDownloads.standard.downloadButton') }}
                </a-button>
              </div>
            </a-tab-pane>
            <a-tab-pane key="premium" :tab="t('standardEditing.sampleDownloads.premiumTab')">
              <div class="sample-content">
                <h4>{{ t('standardEditing.sampleDownloads.premium.title') }}</h4>
                <p>{{ t('standardEditing.sampleDownloads.premium.description') }}</p>
                <a-button type="primary" @click="downloadSample('premium')">
                  {{ t('standardEditing.sampleDownloads.premium.downloadButton') }}
                </a-button>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </section>

    <!-- 科学评审编辑 -->
    <section class="scientific-review-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('standardEditing.scientificReview.title') }}</h2>
          <p>{{ t('standardEditing.scientificReview.subtitle') }}</p>
        </div>
        <div class="scientific-review-content">
          <div class="review-image">
            <img src="./img/scientific-review.jpg" :alt="t('standardEditing.scientificReview.title')" />
          </div>
          <div class="review-features">
            <div class="review-feature" v-for="feature in scientificReviewFeatures" :key="feature.key">
              <div class="feature-icon">
                <CheckCircleOutlined />
              </div>
              <div class="feature-content">
                <h4>{{ t(feature.title) }}</h4>
                <p>{{ t(feature.description) }}</p>
              </div>
            </div>
            <div class="review-action">
              <a-button type="primary" @click="handleLearnMore('scientific-review')">
                {{ t('standardEditing.scientificReview.learnMore') }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 论文科学编辑 -->
    <section class="scientific-editing-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('standardEditing.scientificEditing.title') }}</h2>
          <p>{{ t('standardEditing.scientificEditing.subtitle') }}</p>
        </div>
        <div class="scientific-editing-features">
          <div class="editing-feature" v-for="feature in scientificEditingFeatures" :key="feature.key">
            <h4>{{ t(feature.title) }}</h4>
            <p>{{ t(feature.description) }}</p>
          </div>
        </div>
        <div class="scientific-editing-action">
          <a-button type="primary" size="large" @click="handleLearnMore('scientific-editing')">
            {{ t('standardEditing.scientificEditing.learnMore') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 质量保证 -->
    <section class="quality-guarantee-section">
      <div class="services-content">
        <div class="guarantee-content">
          <div class="guarantee-text">
            <h2>{{ t('standardEditing.qualityGuarantee.title') }}</h2>
            <p>{{ t('standardEditing.qualityGuarantee.subtitle') }}</p>
            <div class="guarantee-features">
              <div class="guarantee-feature" v-for="feature in qualityGuaranteeFeatures" :key="feature.key">
                <h4>{{ t(feature.title) }}</h4>
                <p>{{ t(feature.description) }}</p>
              </div>
            </div>
            <a-button type="primary" @click="handleLearnMore('guarantee')">
              {{ t('standardEditing.qualityGuarantee.learnMore') }}
            </a-button>
          </div>
          <div class="guarantee-image">
            <img src="./img/d464bb5e.jpg" :alt="t('standardEditing.qualityGuarantee.title')" />
          </div>
        </div>
      </div>
    </section>

    <!-- 相关服务 -->
    <section class="related-services-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('standardEditing.relatedServices.title') }}</h2>
        </div>
        <div class="related-services-grid">
          <div class="service-card" v-for="service in relatedServices" :key="service.key">
            <div class="service-image">
              <img :src="service.image" :alt="t(service.title)" />
            </div>
            <div class="service-content">
              <h4>{{ t(service.title) }}</h4>
              <p>{{ t(service.description) }}</p>
              <div class="service-price">{{ t(service.price) }}</div>
              <a-button type="primary" @click="handleOrderClick(service.key)">
                {{ t('standardEditing.services.orderNow') }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 常见问题 -->
    <section class="faq-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('standardEditing.faq.title') }}</h2>
          <p>{{ t('standardEditing.faq.subtitle') }}</p>
        </div>
        <div class="faq-content">
          <a-collapse v-model:activeKey="activeFaqKeys" ghost>
            <a-collapse-panel v-for="faq in faqData" :key="faq.key" :header="t(faq.question)">
              <p v-html="t(faq.answer)"></p>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { CheckOutlined, CheckCircleOutlined, EditOutlined, TeamOutlined, SafetyOutlined, MailOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  serviceFeatures,
  standardFeatures,
  premiumFeatures,
  scientificReviewFeatures,
  scientificEditingFeatures,
  qualityGuaranteeFeatures,
  relatedServices,
  faqData
} from './standardEditing.data'

const { t } = useI18n()
const router = useRouter()

// 响应式数据
const wordCount = ref(400)
const activeServiceTab = ref('editing')
const activeSampleTab = ref('standard')
const activeFaqKeys = ref(['1'])

// 价格计算的计算属性
const standardPrice = computed(() => {
  const basePrice = 0.83 // 每字基础价格
  const currency = t('standardEditing.priceCalculator.currency')
  return `${currency}${(wordCount.value * basePrice).toFixed(2)}`
})

const premiumPrice = computed(() => {
  const basePrice = 4.25 // 每字基础价格
  const currency = t('standardEditing.priceCalculator.currency')
  return `${currency}${(wordCount.value * basePrice).toFixed(2)}`
})

const standardDeliveryDays = computed(() => {
  if (wordCount.value <= 3000) return 2
  if (wordCount.value <= 8000) return 3
  return 4
})

const premiumDeliveryDays = computed(() => {
  if (wordCount.value <= 3000) return 2
  if (wordCount.value <= 8000) return 3
  return 4
})

// 方法
const handleOrderClick = (serviceType?: string) => {
  // 跳转到订单页面或打开外部链接
  window.open('https://china.aje.com/cn/researcher/submit/get-started', '_blank')
}

const handleQuickQuote = () => {
  router.push('/pricing')
}

const handleLearnMore = (serviceType: string) => {
  const routes = {
    'scientific-review': '/services/vip-editing',
    'scientific-editing': '/services/scientific-editing',
    'guarantee': '/guarantee'
  }

  const route = routes[serviceType as keyof typeof routes]
  if (route) {
    router.push(route)
  }
}

const downloadSample = (type: string) => {
  const urls = {
    standard: 'https://aje-cms-production.cdn.prismic.io/aje-cms-production/af3fcf66-5d0f-4c2f-b139-752c0f6bbfe1_AJE-Sample-Standard-Editing.docx',
    premium: 'https://aje-cms-production.cdn.prismic.io/aje-cms-production/0dfe9bb7-38e9-4219-a2c8-eea64d0cada9_AJE-Sample-Premium-Editing.docx'
  }

  const link = document.createElement('a')
  link.href = urls[type as keyof typeof urls]
  link.download = `AJE-Sample-${type}-Editing.docx`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

onMounted(() => {
  console.log('StandardEditing page loaded')
})
</script>

<style scoped>
/* 横幅区域 */
.hero-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  color: white;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 30px;
  color: rgba(255, 255, 255, 0.9);
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.hero-image {
  flex: 1;
  text-align: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

/* 服务介绍 */
.service-intro-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.intro-content {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-top: 60px;
}

.intro-image {
  flex: 1;
}

.intro-image img {
  width: 100%;
  height: auto;
  border-radius: 12px;
}

.intro-features {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
}

.feature-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.feature-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

/* 通用样式 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #666;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 价格计算器 */
.price-calculator-section {
  padding: 80px 0;
  background: white;
}

.calculator-content {
  margin-top: 60px;
}

.word-count-input {
  text-align: center;
  margin-bottom: 40px;
}

.word-count-input label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.word-unit {
  margin-left: 12px;
  color: #666;
  font-size: 16px;
}

.pricing-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 30px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-header h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
  color: white;
}

.price {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.delivery-time {
  font-size: 14px;
  opacity: 0.9;
}

.card-content {
  padding: 30px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0 0 30px 0;
}

.feature-list li {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.check-icon {
  color: #52c41a;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

/* 样稿下载 */
.sample-downloads-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.sample-content {
  text-align: center;
  padding: 40px;
}

.sample-content h4 {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}

.sample-content p {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 科学评审 */
.scientific-review-section {
  padding: 80px 0;
  background: white;
}

.scientific-review-content {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-top: 60px;
}

.review-image {
  flex: 1;
}

.review-image img {
  width: 100%;
  height: auto;
  border-radius: 12px;
}

.review-features {
  flex: 1;
}

.review-feature {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 30px;
}

.review-feature .feature-icon {
  width: 50px;
  height: 50px;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.review-action {
  margin-top: 40px;
}

/* 科学编辑 */
.scientific-editing-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.scientific-editing-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-top: 60px;
}

.editing-feature {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.editing-feature h4 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.editing-feature p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.scientific-editing-action {
  text-align: center;
  margin-top: 40px;
}

/* 质量保证 */
.quality-guarantee-section {
  padding: 80px 0;
  background: white;
}

.guarantee-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.guarantee-text {
  flex: 1;
}

.guarantee-text h2 {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 16px;
}

.guarantee-text>p {
  font-size: 18px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.guarantee-features {
  margin-bottom: 30px;
}

.guarantee-feature {
  margin-bottom: 24px;
}

.guarantee-feature h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.guarantee-feature p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.guarantee-image {
  flex: 1;
  text-align: center;
}

.guarantee-image img {
  max-width: 100%;
  height: auto;
}

/* 相关服务 */
.related-services-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.related-services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 60px;
}

.related-services-grid .service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 480px;
}

.related-services-grid .service-card:hover {
  transform: translateY(-5px);
}

.related-services-grid .service-image {
  height: 200px;
  overflow: hidden;
}

.related-services-grid .service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-services-grid .service-content {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.related-services-grid .service-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.related-services-grid .service-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
  font-size: 14px;
  flex: 1;
}

.service-price {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 16px;
}

/* 常见问题 */
.faq-section {
  padding: 80px 0;
  background: white;
}

.faq-content {
  margin-top: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {

  .hero-container,
  .intro-content,
  .scientific-review-content,
  .guarantee-content {
    flex-direction: column;
    gap: 40px;
  }

  .pricing-cards {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .scientific-editing-features {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .related-services-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-content h1 {
    font-size: 32px;
  }

  .section-header h2 {
    font-size: 28px;
  }

  .calculator-note {
    margin-top: 15px;
  }

  .calculator-note p {
    font-size: 12px;
  }
}

/* 价格计算器注释样式 */
.calculator-note {
  margin-top: 20px;
  text-align: center;
}

.calculator-note p {
  color: #666;
  font-size: 14px;
  margin: 0;
}
</style>
