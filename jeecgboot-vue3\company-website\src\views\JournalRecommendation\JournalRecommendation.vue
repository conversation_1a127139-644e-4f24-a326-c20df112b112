<template>
  <div class="journal-recommendation-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('journalRecommendation.hero.title') }}</h1>
          <p class="hero-description">{{ t('journalRecommendation.hero.description') }}</p>
          <div class="hero-actions">
            <a-button type="primary" size="large" @click="handleOrderClick">
              {{ t('journalRecommendation.hero.orderNow') }}
            </a-button>
          </div>
        </div>
        <div class="hero-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/ac74602c-5931-43a4-8403-90fdeb5a5f69_2018_02_15_aje_event-8284-Copy1+%281%29.jpg?auto=compress,format&rect=0,218,4200,2363&w=60"
            :alt="t('journalRecommendation.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 服务特色介绍 -->
    <section class="service-features-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('journalRecommendation.serviceFeatures.title') }}</h2>
          <p>{{ t('journalRecommendation.serviceFeatures.description') }}</p>
        </div>
        <div class="features-layout">
          <div class="features-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/9b5c07f5-1031-4a50-afe0-b29be2e4d6e3_2020_01_31_aje_corporate-0914.jpg?auto=compress,format&rect=642,242,932,932&w=60"
              :alt="t('journalRecommendation.serviceFeatures.imageAlt')" />
          </div>
          <div class="features-list">
            <div class="feature-item" v-for="feature in serviceFeatures" :key="feature.key">
              <div class="feature-icon">
                <component :is="feature.icon" />
              </div>
              <div class="feature-content">
                <h4>{{ t(`journalRecommendation.serviceFeatures.features.${feature.key}.title`) }}</h4>
                <p>{{ t(`journalRecommendation.serviceFeatures.features.${feature.key}.description`) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 期刊专家工作内容 -->
    <section class="expert-work-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('journalRecommendation.expertWork.title') }}</h2>
        </div>
        <div class="work-content">
          <div class="work-item" v-for="work in expertWork" :key="work.key">
            <div class="work-icon">
              <component :is="work.icon" />
            </div>
            <div class="work-info">
              <h4>{{ t(`journalRecommendation.expertWork.items.${work.key}.title`) }}</h4>
              <p>{{ t(`journalRecommendation.expertWork.items.${work.key}.description`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 注意事项 -->
    <section class="notice-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('journalRecommendation.notice.title') }}</h2>
        </div>
        <div class="notice-list">
          <div class="notice-item" v-for="(notice, index) in noticeItems" :key="notice">
            <div class="notice-number">{{ index + 1 }}</div>
            <p>{{ t(`journalRecommendation.notice.items.${notice}`) }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 期刊推荐报告示例 -->
    <section class="sample-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('journalRecommendation.sample.title') }}</h2>
        </div>
        <div class="sample-content">
          <div class="sample-info">
            <h3>{{ t('journalRecommendation.sample.subtitle') }}</h3>
            <p>{{ t('journalRecommendation.sample.description') }}</p>
            <a-button type="primary" @click="downloadSample">
              {{ t('journalRecommendation.sample.download') }}
            </a-button>
          </div>
          <div class="sample-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/2a7474a6-cbc4-456c-a477-318256af34b7_journal-recommendation-sample-image.png?auto=compress,format&w=60"
              :alt="t('journalRecommendation.sample.imageAlt')" />
          </div>
        </div>
      </div>
    </section>

    <!-- 需要提供的信息 -->
    <section class="requirements-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('journalRecommendation.requirements.title') }}</h2>
          <p>{{ t('journalRecommendation.requirements.description') }}</p>
        </div>
        <div class="requirements-layout">
          <div class="requirements-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/366b3417-50c3-40e1-9419-5c4a3fb3216c_life+sciences+stackable+journal+covers+vertical+square.png?auto=compress,format&w=60"
              :alt="t('journalRecommendation.requirements.imageAlt')" />
          </div>
          <div class="requirements-list">
            <div class="requirement-item" v-for="requirement in requirements" :key="requirement.key">
              <div class="requirement-icon">
                <component :is="requirement.icon" />
              </div>
              <div class="requirement-content">
                <h4>{{ t(`journalRecommendation.requirements.items.${requirement.key}.title`) }}</h4>
                <p>{{ t(`journalRecommendation.requirements.items.${requirement.key}.description`) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 客户评价 -->
    <section class="testimonials-section">
      <div class="services-content">
        <div class="testimonials-grid">
          <div class="testimonial-card" v-for="testimonial in testimonials" :key="testimonial.key">
            <div class="testimonial-content">
              <p class="testimonial-text">{{ t(`journalRecommendation.testimonials.${testimonial.key}.text`) }}</p>
              <div class="testimonial-author">
                <div class="author-info">
                  <p class="author-name">{{ t(`journalRecommendation.testimonials.${testimonial.key}.name`) }}</p>
                  <p class="author-org">{{ t(`journalRecommendation.testimonials.${testimonial.key}.organization`) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 相关服务 -->
    <section class="related-services-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('journalRecommendation.relatedServices.title') }}</h2>
        </div>
        <div class="related-services-grid">
          <div class="service-card" v-for="service in relatedServices" :key="service.key">
            <div class="service-image">
              <img :src="service.image" :alt="service.key" style="width: 100%; height: auto;" />
            </div>
            <div class="service-content">
              <h4>{{ t(`journalRecommendation.relatedServices.services.${service.key}.title`) }}</h4>
              <p>{{ t(`journalRecommendation.relatedServices.services.${service.key}.description`) }}</p>
              <div class="service-price">{{ t(`journalRecommendation.relatedServices.services.${service.key}.price`) }}
              </div>
              <a-button type="primary" @click="handleServiceClick(service)">
                {{ t(`journalRecommendation.relatedServices.services.${service.key}.buttonText`) }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ区域 -->
    <section class="faq-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('journalRecommendation.faq.title') }}</h2>
          <p>{{ t('journalRecommendation.faq.subtitle') }}</p>
        </div>
        <a-collapse v-model:activeKey="activeFaqKeys" class="faq-collapse">
          <a-collapse-panel v-for="faq in faqData" :key="faq.key"
            :header="t(`journalRecommendation.faq.items.${faq.key}.question`)">
            <div v-html="t(`journalRecommendation.faq.items.${faq.key}.answer`)"></div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="cta-content">
        <div class="cta-actions">
          <a-button type="primary" size="large" @click="handleOrderClick">
            {{ t('journalRecommendation.cta.orderNow') }}
          </a-button>
          <a-button size="large" @click="handleMoreServices">
            {{ t('journalRecommendation.cta.moreServices') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { BookOutlined, SearchOutlined, FileTextOutlined, BarChartOutlined, NumberOutlined, GlobalOutlined, ExclamationCircleOutlined, FilterOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  serviceFeatures,
  expertWork,
  noticeItems,
  requirements,
  testimonials,
  relatedServices,
  faqData
} from './journalRecommendation.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 响应式数据
const activeFaqKeys = ref(['1'])

// 方法
const handleOrderClick = () => {
  window.open('https://china.aje.com/cn/researcher/submit/get-started', '_blank')
}

const handleServiceClick = (service: any) => {
  window.open(service.link, '_blank')
}

const downloadSample = () => {
  window.open('https://aje-cms-production.cdn.prismic.io/aje-cms-production/7c17a2d8-061a-4c7e-b468-6414bac4aa89_journal-recommendation-sample.pdf', '_blank')
}

const handleMoreServices = () => {
  window.open('https://www.aje.cn/services', '_blank')
}

// 生命周期
onMounted(() => {
  console.log('JournalRecommendation 页面已加载')
})
</script>

<style scoped>
.journal-recommendation-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.hero-image {
  flex-shrink: 0;
}

.hero-image img {
  width: 200px;
  height: 120px;
  border-radius: 12px;
  object-fit: cover;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 通用区域样式 */
.service-features-section,
.expert-work-section,
.notice-section,
.sample-section,
.requirements-section,
.testimonials-section,
.related-services-section,
.faq-section {
  padding: 80px 0;
}

.service-features-section {
  background: white;
}

.expert-work-section {
  background: #f8fafc;
}

.notice-section {
  background: white;
}

.sample-section {
  background: #f8fafc;
}

.requirements-section {
  background: white;
}

.testimonials-section {
  background: #f8fafc;
}

.related-services-section {
  background: white;
}

.faq-section {
  background: #f8fafc;
}

/* 区域标题 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 服务特色区域 */
.features-layout {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.features-image {
  flex-shrink: 0;
}

.features-image img {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

.features-list {
  flex: 1;
}

.feature-item {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
}

.feature-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.feature-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.feature-content p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

/* 期刊专家工作区域 */
.work-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.work-item {
  display: flex;
  gap: 20px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.work-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.work-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.work-info p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

/* 注意事项区域 */
.notice-list {
  max-width: 800px;
  margin: 0 auto;
}

.notice-item {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
}

.notice-number {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.notice-item p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

/* 样本示例区域 */
.sample-content {
  display: flex;
  gap: 60px;
  align-items: center;
}

.sample-info {
  flex: 1;
}

.sample-info h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.sample-info p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 24px;
}

.sample-image {
  flex-shrink: 0;
}

.sample-image img {
  width: 200px;
  height: 250px;
  border-radius: 12px;
  object-fit: cover;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 需要提供信息区域 */
.requirements-layout {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.requirements-image {
  flex-shrink: 0;
}

.requirements-image img {
  width: 200px;
  height: 250px;
  border-radius: 12px;
  object-fit: cover;
}

.requirements-list {
  flex: 1;
}

.requirement-item {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
}

.requirement-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.requirement-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.requirement-content p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

/* 客户评价区域 */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 32px;
}

.testimonial-card {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-text {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 20px;
  font-style: italic;
}

.testimonial-author {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.author-org {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.4;
}

/* 相关服务区域 */
.related-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.service-image {
  height: 300px;
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
}

.service-image img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.service-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.service-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.service-content p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 16px;
  flex: 1;
}

.service-price {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 16px;
}

.service-content .ant-btn {
  width: 100%;
  margin-top: auto;
}

/* FAQ区域 */
.faq-collapse {
  background: transparent;
}

.faq-collapse :deep(.ant-collapse-item) {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.faq-collapse :deep(.ant-collapse-header) {
  padding: 20px 24px;
  font-weight: 600;
  color: #1a202c;
  border-radius: 12px;
}

.faq-collapse :deep(.ant-collapse-content-box) {
  padding: 0 24px 20px;
  color: #4a5568;
  line-height: 1.6;
}

/* CTA区域 */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80px 0;
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .features-layout,
  .sample-content,
  .requirements-layout {
    flex-direction: column;
    gap: 40px;
  }

  .features-image img,
  .sample-image img,
  .requirements-image img {
    width: 150px;
    height: 150px;
    margin: 0 auto;
  }

  .work-content {
    grid-template-columns: 1fr;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .related-services-grid {
    grid-template-columns: 1fr;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-actions .ant-btn {
    width: 200px;
  }
}
</style>
