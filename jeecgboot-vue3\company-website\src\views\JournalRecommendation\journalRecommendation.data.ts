import {
  BookOutlined,
  SearchOutlined,
  FileTextOutlined,
  BarChartOutlined,
  NumberOutlined,
  GlobalOutlined,
  ExclamationCircleOutlined,
  FilterOutlined,
} from '@ant-design/icons-vue';

// 服务特色
export const serviceFeatures = [
  { key: 'selection', icon: BookOutlined },
  { key: 'customization', icon: SearchOutlined },
  { key: 'report', icon: FileTextOutlined },
];

// 期刊专家工作内容
export const expertWork = [
  { key: 'matching', icon: SearchOutlined },
  { key: 'indexing', icon: BarChartOutlined },
  { key: 'access', icon: GlobalOutlined },
];

// 注意事项
export const noticeItems = ['peerReview', 'evaluation', 'guarantee', 'timeline', 'information'];

// 需要提供的信息
export const requirements = [
  { key: 'impactFactor', icon: NumberOutlined },
  { key: 'field', icon: BookOutlined },
  { key: 'indexing', icon: BarChartOutlined },
  { key: 'exclusion', icon: ExclamationCircleOutlined },
];

// 客户评价
export const testimonials = [{ key: 'neiff' }, { key: 'gui' }];

// 相关服务
export const relatedServices = [
  {
    key: 'formatting',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAvPdc1huKlrb_manuscript-formatting.png?auto=format,compress&w=1080',
    link: 'https://www.aje.cn/services/formatting',
  },
  {
    key: 'figures',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/66ce856e-1f94-4f32-a0e7-8c8126fb6aa3_figure-formatting-128.png?auto=compress,format&w=1080',
    link: 'https://www.aje.cn/services/figures',
  },
  {
    key: 'presubmission',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/fc927461-7932-461b-9983-cf1441cc454e_presubmission+review+illustration.png?auto=compress,format&w=1080',
    link: 'https://www.aje.cn/services/presubmission-review',
  },
];

// FAQ数据
export const faqData = [{ key: 'guarantee' }, { key: 'evaluation' }, { key: 'contact' }];
