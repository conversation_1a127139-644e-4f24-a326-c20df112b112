<template>
  <div class="pricing">
    <AppHeader />

    <section v-if="!bannerClosed" class="promo-banner">
      <div class="banner-content">
        <div class="banner-text">
          <p>{{ t('pricing.banner.text') }}</p>
        </div>
        <div class="banner-actions">
          <a-button type="primary" @click="handlePromoClick">
            {{ t('pricing.banner.buttonText') }}
          </a-button>
          <a-button type="text" @click="closeBanner" class="close-btn">
            <CloseOutlined />
          </a-button>
        </div>
      </div>
    </section>

    <section class="quote-section">
      <div class="quote-container">
        <div class="quote-header">
          <h1>{{ t('pricing.quote.title') }}</h1>
          <p>{{ t('pricing.quote.description') }}</p>
        </div>
        <div class="quote-form">
          <div class="word-count-section">
            <div class="slider-container">
              <a-slider v-model:value="wordCount" :min="100" :max="50000" :step="100" @change="handleWordCountChange"
                class="word-slider" />
            </div>
            <div class="word-input-container">
              <div class="input-wrapper">
                <a-input-number v-model:value="wordCount" :min="100" :max="50000" :step="100"
                  @change="handleWordCountChange" class="word-input" :controls="false" />
                <span class="word-label">{{ t('pricing.quote.wordCount') }}</span>
              </div>
            </div>
          </div>
          <div class="service-tabs">
            <div class="tabs-container">
              <a href="#语言润色及科学编辑" class="tab-item" @click="handleTabClick('editing')">
                {{ t('pricing.tabs.editing') }}
              </a>
              <a href="#学术论文翻译" class="tab-item" @click="handleTabClick('translation')">
                {{ t('pricing.tabs.translation') }}
              </a>
              <a href="#期刊选择" class="tab-item" @click="handleTabClick('journal')">
                {{ t('pricing.tabs.journal') }}
              </a>
              <a href="#图表和排版、推广服务" class="tab-item" @click="handleTabClick('formatting')">
                {{ t('pricing.tabs.formatting') }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="语言润色及科学编辑" class="editing-services-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('pricing.editingServices.title') }}</h2>
          <p>{{ t('pricing.editingServices.description') }}</p>
        </div>
        <div class="services-grid-horizontal">
          <!-- Standard Editing -->
          <div class="service-card-grid">
            <div class="service-header-grid">
              <div class="service-badge">{{ t('pricing.editingServices.standard.badge') }}</div>
              <div class="service-icon">
                <ThunderboltOutlined />
              </div>
            </div>
            <div class="service-info-grid">
              <h3>{{ t('pricing.editingServices.standard.title') }}</h3>
              <div class="service-price">{{ t('pricing.editingServices.standard.price') }}</div>
              <div class="service-type">{{ t('pricing.editingServices.standard.type') }}</div>
            </div>
            <div class="service-divider-grid"></div>
            <div class="service-content-grid">
              <p class="service-summary">{{ t('pricing.editingServices.standard.summary') }}</p>
              <div class="detail-section-grid">
                <h4>{{ t('pricing.editingServices.recommendedFor') }}</h4>
                <p>{{ t('pricing.editingServices.standard.recommendedFor') }}</p>
              </div>
              <div class="detail-section-grid">
                <h4>{{ t('pricing.editingServices.features') }}</h4>
                <ul>
                  <li v-for="(feature, index) in standardFeatures" :key="index">{{ feature }}</li>
                </ul>
              </div>
              <div class="service-divider-grid"></div>
            </div>
            <div class="service-actions-grid">
              <a-button type="primary" @click="handleOrderClick('standard')">
                {{ t('pricing.editingServices.orderNow') }}
              </a-button>
            </div>
          </div>

          <!-- Premium Editing -->
          <div class="service-card-grid">
            <div class="service-header-grid">
              <div class="service-badge">{{ t('pricing.editingServices.premium.badge') }}</div>
              <div class="service-icon">
                <CrownOutlined />
              </div>
            </div>
            <div class="service-info-grid">
              <h3>{{ t('pricing.editingServices.premium.title') }}</h3>
              <div class="service-price">{{ t('pricing.editingServices.premium.price') }}</div>
              <div class="service-type">{{ t('pricing.editingServices.premium.type') }}</div>
            </div>
            <div class="service-divider-grid"></div>
            <div class="service-content-grid">
              <p class="service-summary">{{ t('pricing.editingServices.premium.summary') }}</p>
              <div class="detail-section-grid">
                <h4>{{ t('pricing.editingServices.recommendedFor') }}</h4>
                <p>{{ t('pricing.editingServices.premium.recommendedFor') }}</p>
              </div>
              <div class="detail-section-grid">
                <h4>{{ t('pricing.editingServices.features') }}</h4>
                <ul>
                  <li v-for="(feature, index) in premiumFeatures" :key="index">{{ feature }}</li>
                </ul>
              </div>
              <div class="service-divider-grid"></div>
            </div>
            <div class="service-actions-grid">
              <a-button type="primary" @click="handleOrderClick('premium')">
                {{ t('pricing.editingServices.orderNow') }}
              </a-button>
            </div>
          </div>

          <!-- Scientific Review -->
          <div class="service-card-grid">
            <div class="service-info-grid">
              <h3>{{ t('pricing.editingServices.scientific.title') }}</h3>
              <div class="service-price">{{ t('pricing.editingServices.scientific.price') }}</div>
              <div class="service-type">{{ t('pricing.editingServices.scientific.type') }}</div>
            </div>
            <div class="service-divider-grid"></div>
            <div class="service-content-grid">
              <p class="service-summary">{{ t('pricing.editingServices.scientific.summary') }}</p>
              <div class="detail-section-grid">
                <h4>{{ t('pricing.editingServices.recommendedFor') }}</h4>
                <p>{{ t('pricing.editingServices.scientific.recommendedFor') }}</p>
              </div>
              <div class="detail-section-grid">
                <h4>{{ t('pricing.editingServices.features') }}</h4>
                <ul>
                  <li v-for="(feature, index) in scientificFeatures" :key="index">{{ feature }}</li>
                </ul>
              </div>
              <div class="service-divider-grid"></div>
            </div>
            <div class="service-actions-grid">
              <a-button type="primary" @click="handleOrderClick('scientific')">
                {{ t('pricing.editingServices.orderNow') }}
              </a-button>
            </div>
          </div>

          <!-- Advanced Editing -->
          <div class="service-card-grid">
            <div class="service-info-grid">
              <h3>{{ t('pricing.editingServices.advanced.title') }}</h3>
              <div class="service-price">{{ t('pricing.editingServices.advanced.price') }}</div>
              <div class="service-type">{{ t('pricing.editingServices.advanced.type') }}</div>
            </div>
            <div class="service-divider-grid"></div>
            <div class="service-content-grid">
              <p class="service-summary">{{ t('pricing.editingServices.advanced.summary') }}</p>
              <div class="detail-section-grid">
                <h4>{{ t('pricing.editingServices.recommendedFor') }}</h4>
                <p>{{ t('pricing.editingServices.advanced.recommendedFor') }}</p>
              </div>
              <div class="detail-section-grid">
                <h4>{{ t('pricing.editingServices.features') }}</h4>
                <ul>
                  <li v-for="(feature, index) in advancedFeatures" :key="index">{{ feature }}</li>
                </ul>
              </div>
              <div class="rush-option-grid">
                <div class="service-divider-grid"></div>
                <p><strong>{{ t('pricing.editingServices.advanced.rushOption') }}</strong></p>
              </div>
            </div>
            <div class="service-actions-grid">
              <a-button type="primary" @click="handleOrderClick('advanced')">
                {{ t('pricing.editingServices.orderNow') }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Academic Translation -->
    <section id="学术论文翻译" class="translation-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('pricing.translation.title') }}</h2>
          <p>{{ t('pricing.translation.description') }}</p>
        </div>
        <div class="translation-service">
          <div class="service-card translation-card">
            <div class="service-header">
              <div class="service-info">
                <h4>{{ t('pricing.translation.service.title') }}</h4>
                <p>{{ t('pricing.translation.service.subtitle') }}</p>
              </div>
            </div>
            <div class="service-content">
              <div class="service-details">
                <h4>{{ t('pricing.translation.service.featuresTitle') }}</h4>
                <div class="service-price">{{ t('pricing.translation.service.price') }}</div>
                <h6>{{ t('pricing.translation.service.expertiseTitle') }}</h6>
                <ul>
                  <li v-for="feature in translationServiceData.features" :key="feature">{{ t(`pricing.${feature}`)
                  }}</li>
                </ul>
              </div>
              <div class="service-actions">
                <a-button type="primary" @click="handleOrderClick('translation')">
                  {{ t('pricing.editingServices.orderNow') }}
                </a-button>
                <a-button @click="handleServiceDetails('translation')">
                  {{ t('pricing.translation.service.details') }}
                </a-button>
              </div>
              <div class="rush-option">
                <p>{{ t('pricing.translation.service.rushOption') }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Journal Selection -->
    <section id="期刊选择" class="journal-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('pricing.journal.title') }}</h2>
          <p>{{ t('pricing.journal.description') }}</p>
        </div>
        <div class="journal-service">
          <div class="service-card journal-card">
            <div class="service-header">
              <div class="service-info">
                <h4>{{ t('pricing.journal.service.title') }}</h4>
                <p>{{ t('pricing.journal.service.subtitle') }}</p>
              </div>
            </div>
            <div class="service-content">
              <div class="service-details">
                <h4>{{ t('pricing.journal.service.processTitle') }}</h4>
                <div class="service-price">{{ t('pricing.journal.service.price') }}</div>
                <h6>{{ t('pricing.journal.service.benefitsTitle') }}</h6>
                <ul>
                  <li v-for="benefit in journalServiceData.benefits" :key="benefit">{{ t(`pricing.${benefit}`) }}</li>
                </ul>
              </div>
              <div class="service-actions">
                <a-button type="primary" @click="handleOrderClick('journal')">
                  {{ t('pricing.editingServices.orderNow') }}
                </a-button>
                <a-button @click="handleServiceDetails('journal')">
                  {{ t('pricing.translation.service.details') }}
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Formatting Services -->
    <section id="图表和排版、推广服务" class="formatting-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('pricing.formatting.title') }}</h2>
        </div>
        <div class="formatting-services-grid">
          <!-- 文稿格式排版 -->
          <div class="formatting-card">
            <a href="/services/formatting" class="service-link">
              <div class="service-image">
                <img src="./img/ZiBGAvPdc1huKlrb_manuscript-formatting.png"
                  :alt="t('pricing.formatting.services.formatting.title')" />
              </div>
              <div class="service-content">
                <h4>{{ t('pricing.formatting.services.formatting.title') }}</h4>
                <p>{{ t('pricing.formatting.services.formatting.description') }}</p>
              </div>
            </a>
            <div class="service-footer">
              <div class="service-price">{{ t('pricing.formatting.services.formatting.price') }}</div>
              <a-button type="primary" class="order-btn">
                {{ t('pricing.editingServices.orderNow') }}
              </a-button>
            </div>
          </div>

          <!-- 表格排版 -->
          <div class="formatting-card">
            <a href="/services/figures" class="service-link">
              <div class="service-image">
                <img src="./img/66ce856e-1f94-4f32-a0e7-8c8126fb6aa3_figure-formatting.png"
                  :alt="t('pricing.formatting.services.figures.title')" />
              </div>
              <div class="service-content">
                <h4>{{ t('pricing.formatting.services.figures.title') }}</h4>
                <p>{{ t('pricing.formatting.services.figures.description') }}</p>
              </div>
            </a>
            <div class="service-footer">
              <div class="service-price-note">{{ t('pricing.formatting.services.figures.price') }}</div>
              <a-button type="primary" class="order-btn">
                {{ t('pricing.editingServices.orderNow') }}
              </a-button>
            </div>
          </div>

          <!-- 视频摘要制作 -->
          <div class="formatting-card">
            <a href="/services/research-promotion" class="service-link">
              <div class="service-image">
                <img src="./img/ZiBF_fPdc1huKlrT_formatting-services-group.png"
                  :alt="t('pricing.formatting.services.video.title')" />
              </div>
              <div class="service-content">
                <h4>{{ t('pricing.formatting.services.video.title') }}</h4>
                <p>{{ t('pricing.formatting.services.video.description') }}</p>
              </div>
            </a>
            <div class="service-footer">
              <div class="service-price">{{ t('pricing.formatting.services.video.price') }}</div>
              <a-button type="primary" class="order-btn">
                {{ t('pricing.editingServices.orderNow') }}
              </a-button>
            </div>
          </div>

          <!-- 图表摘要 -->
          <div class="formatting-card">
            <a href="/services/research-promotion" class="service-link">
              <div class="service-image">
                <img src="./img/3407a1aa-58bf-4321-96bf-139c0f302973_custom-Illustration.png"
                  :alt="t('pricing.formatting.services.graphical.title')" />
              </div>
              <div class="service-content">
                <h4>{{ t('pricing.formatting.services.graphical.title') }}</h4>
                <p>{{ t('pricing.formatting.services.graphical.description') }}</p>
              </div>
            </a>
            <div class="service-footer">
              <div class="service-price">{{ t('pricing.formatting.services.graphical.price') }}</div>
              <a-button type="primary" class="order-btn">
                {{ t('pricing.editingServices.orderNow') }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Payment Methods -->
    <section class="payment-section">
      <div class="payment-container">
        <div class="section-header">
          <h2>{{ t('pricing.payment.title') }}</h2>
          <p>{{ t('pricing.payment.description') }}</p>
        </div>
        <div class="payment-content-wrapper">
          <div class="payment-methods">
            <div class="payment-card">
              <div class="payment-icon">
                <svg aria-hidden="true" focusable="false" data-prefix="fal" data-icon="globe"
                  class="svg-inline--fa fa-globe fa-2xl" role="img" xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512">
                  <path fill="currentColor"
                    d="M256 480c16.7 0 40.4-14.4 61.9-57.3c9.9-19.8 18.2-43.7 24.1-70.7l-172 0c5.9 27 14.2 50.9 24.1 70.7C215.6 465.6 239.3 480 256 480zM164.3 320l183.4 0c2.8-20.2 4.3-41.7 4.3-64s-1.5-43.8-4.3-64l-183.4 0c-2.8 20.2-4.3 41.7-4.3 64s1.5 43.8 4.3 64zM170 160l172 0c-5.9-27-14.2-50.9-24.1-70.7C296.4 46.4 272.7 32 256 32s-40.4 14.4-61.9 57.3C184.2 109.1 175.9 133 170 160zm210 32c2.6 20.5 4 41.9 4 64s-1.4 43.5-4 64l90.8 0c6-20.3 9.3-41.8 9.3-64s-3.2-43.7-9.3-64L380 192zm78.5-32c-25.9-54.5-73.1-96.9-130.9-116.3c21 28.3 37.6 68.8 47.2 116.3l83.8 0zm-321.1 0c9.6-47.6 26.2-88 47.2-116.3C126.7 63.1 79.4 105.5 53.6 160l83.7 0zm-96 32c-6 20.3-9.3 41.8-9.3 64s3.2 43.7 9.3 64l90.8 0c-2.6-20.5-4-41.9-4-64s1.4-43.5 4-64l-90.8 0zM327.5 468.3c57.8-19.5 105-61.8 130.9-116.3l-83.8 0c-9.6 47.6-26.2 88-47.2 116.3zm-143 0c-21-28.3-37.5-68.8-47.2-116.3l-83.7 0c25.9 54.5 73.1 96.9 130.9 116.3zM256 512A256 256 0 1 1 256 0a256 256 0 1 1 0 512z">
                  </path>
                </svg>
              </div>
              <div class="payment-content">
                <h4>{{ t('pricing.payment.methods.quick.title') }}</h4>
                <p>{{ t('pricing.payment.methods.quick.description') }}</p>
              </div>
            </div>
            <div class="payment-card">
              <div class="payment-icon">
                <svg aria-hidden="true" focusable="false" data-prefix="fal" data-icon="paper-plane"
                  class="svg-inline--fa fa-paper-plane fa-2xl" role="img" xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512">
                  <path fill="currentColor"
                    d="M511.6 36.9c1.9-12.1-3.4-24.3-13.5-31.2s-23.3-7.5-34-1.4l-448 256C5.5 266.3-.7 277.8 .1 290s8.4 22.9 19.6 27.6L160 376l0 93c0 23.8 19.3 43 43 43c13.1 0 25.4-5.9 33.6-16.1l52.8-66 .1 0 114.2 47.6c9.1 3.8 19.4 3.2 28-1.6s14.5-13.3 16-23l64-416zm-253 380.2l-47 58.8c-2.1 2.6-5.3 4.1-8.6 4.1c-6.1 0-11-4.9-11-11l0-79.7 66.6 27.8zm43.1-16.7l-96.6-40.3L474.1 70.5 416 448 301.8 400.4zM450.5 48.8L173.6 347 32 288 450.5 48.8z">
                  </path>
                </svg>
              </div>
              <div class="payment-content">
                <h4>{{ t('pricing.payment.methods.bank.title') }}</h4>
                <p>{{ t('pricing.payment.methods.bank.description') }}</p>
              </div>
            </div>
            <div class="payment-card">
              <div class="payment-icon">
                <svg aria-hidden="true" focusable="false" data-prefix="fal" data-icon="cart-shopping"
                  class="svg-inline--fa fa-cart-shopping fa-2xl" role="img" xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 576 512">
                  <path fill="currentColor"
                    d="M16 0C7.2 0 0 7.2 0 16s7.2 16 16 16l37.9 0c7.6 0 14.2 5.3 15.7 12.8l58.9 288c6.1 29.8 32.3 51.2 62.7 51.2L496 384c8.8 0 16-7.2 16-16s-7.2-16-16-16l-304.8 0c-15.2 0-28.3-10.7-31.4-25.6L152 288l314.6 0c29.4 0 55-20 62.1-48.5L570.6 71.8c5-20.2-10.2-39.8-31-39.8L99.1 32C92.5 13 74.4 0 53.9 0L16 0zm90.1 64l433.4 0L497.6 231.8C494 246 481.2 256 466.5 256l-321.1 0L106.1 64zM168 456a24 24 0 1 1 48 0 24 24 0 1 1 -48 0zm80 0a56 56 0 1 0 -112 0 56 56 0 1 0 112 0zm200-24a24 24 0 1 1 0 48 24 24 0 1 1 0-48zm0 80a56 56 0 1 0 0-112 56 56 0 1 0 0 112z">
                  </path>
                </svg>
              </div>
              <div class="payment-content">
                <h4>{{ t('pricing.payment.methods.prepaid.title') }}</h4>
                <p>{{ t('pricing.payment.methods.prepaid.description') }}</p>
              </div>
            </div>
          </div>
          <div class="payment-image">
            <img src="./img/6d3160ae-951e-4727-9748-6ca70496c705_aje+website.jpg" alt="支付方式" />
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ -->
    <section class="faq-section">
      <div class="services-content">
        <div class="section-header">
          <h3>{{ t('pricing.faq.title') }}</h3>
          <p>{{ t('pricing.faq.description') }}</p>
        </div>
        <div class="faq-list">
          <a-collapse v-model:activeKey="activeFaqKeys" ghost>
            <a-collapse-panel v-for="faq in faqData" :key="faq.id" :header="t(`pricing.${faq.question}`)">
              <div v-html="t(`pricing.${faq.answer}`)"></div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
    </section>

    <!-- Contact Information -->
    <section class="contact-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('pricing.contact.title') }}</h2>
          <p>{{ t('pricing.contact.description') }}</p>
        </div>
        <div class="contact-actions">
          <a-button type="primary" size="large" @click="handleOrderClick('contact')">
            {{ t('pricing.contact.buttons.primary') }}
          </a-button>
          <a-button size="large" @click="handleServiceDetails('all')">
            {{ t('pricing.contact.buttons.secondary') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import zhCN from './locales/zh-CN'
import en from './locales/en'
import { CloseOutlined, ThunderboltOutlined, CrownOutlined, ExperimentOutlined, RocketOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  translationServiceData,
  journalServiceData,
  faqData,
  bannerData,
  contactData
} from './pricing.data'
const { t, locale } = useI18n()

const bannerClosed = ref(false)
const wordCount = ref(400)
const activeServiceTab = ref('editing')
const activeFaqKeys = ref(['1'])

const iconComponents = {
  ThunderboltOutlined,
  CrownOutlined,
  ExperimentOutlined,
  RocketOutlined
}

// 计算属性用于获取特点列表 - 直接从翻译文件中获取数组
const standardFeatures = computed(() => {
  const messages = locale.value === 'zh-CN' ? zhCN : en
  return messages.pricing.editingServices.standard.features
})

const premiumFeatures = computed(() => {
  const messages = locale.value === 'zh-CN' ? zhCN : en
  return messages.pricing.editingServices.premium.features
})

const scientificFeatures = computed(() => {
  const messages = locale.value === 'zh-CN' ? zhCN : en
  return messages.pricing.editingServices.scientific.features
})

const advancedFeatures = computed(() => {
  const messages = locale.value === 'zh-CN' ? zhCN : en
  return messages.pricing.editingServices.advanced.features
})


const closeBanner = () => {
  bannerClosed.value = true
}

const handlePromoClick = () => {
  window.open(bannerData.buttonLink, '_blank')
}

const handleWordCountChange = (value: number) => {
  wordCount.value = value
}

const handleTabClick = (tabKey: string) => {
  // 标签页点击处理，锚点跳转由浏览器自动处理
  console.log('Tab clicked:', tabKey)
}

const handleOrderClick = (service: any) => {
  console.log('Order service:', service)
  window.open(contactData.buttons.primary.link, '_blank')
}

const handleServiceDetails = (service: string) => {
  console.log('Service details:', service)
  window.open(contactData.buttons.secondary.link, '_blank')
}

onMounted(() => {
  console.log('Pricing page loaded')
})
</script>

<style scoped>
.pricing {
  min-height: 100vh;
  background: #f8f9fa;
}

.promo-banner {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  padding: 16px 0;
}

.banner-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.banner-text p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.banner-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.close-btn {
  color: white !important;
}

.quote-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.quote-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

.quote-header {
  text-align: center;
  margin-bottom: 60px;
}

.quote-header h1 {
  font-size: 48px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 16px;
}

.quote-header p {
  font-size: 18px;
  color: #6c757d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.quote-form {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h1 {
  font-size: 48px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #4a5568;
  max-width: 800px;
  margin: 0 auto;
}

.quote-calculator {
  background: #f7fafc;
  border-radius: 12px;
  padding: 40px;
}

.word-count-section {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 40px;
}

.slider-container {
  flex: 1;
}

.word-slider {
  width: 100%;
}

.word-input-container {
  flex-shrink: 0;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 8px 12px;
}

.word-input {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  width: 80px;
  text-align: center;
}

.word-input .ant-input-number-input {
  background: transparent;
  border: none;
  text-align: center;
}

.word-label {
  font-weight: 500;
  color: #4a5568;
  white-space: nowrap;
}

.service-tabs {
  margin-top: 20px;
}

.tabs-container {
  display: flex;
  justify-content: center;
  gap: 8px;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 16px;
  border-radius: 6px;
  background: transparent;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  border: 1px solid transparent;
}

.tab-item:hover {
  background: #fff;
  color: #ff6b35;
  border-color: #ff6b35;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.15);
  text-decoration: none;
}

.tab-item.active {
  background: #ff6b35;
  color: #fff;
  border-color: #ff6b35;
}



.editing-services-section {
  padding: 80px 0;
  background: white;
}

.services-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.services-grid-horizontal {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-top: 60px;
}

@media (max-width: 1200px) {
  .services-grid-horizontal {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .services-grid-horizontal {
    grid-template-columns: 1fr;
  }
}

.service-card-grid {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.service-card-grid:hover {
  border-color: #ff6b35;
  box-shadow: 0 4px 20px rgba(255, 107, 53, 0.1);
}

.service-header-grid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.service-badge {
  background: #ff6b35;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.service-icon {
  font-size: 20px;
  color: #ff6b35;
}

.service-info-grid {
  margin-bottom: 16px;
  text-align: center;
}

.service-info-grid h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.service-price {
  font-size: 18px;
  font-weight: 700;
  color: #ff6b35;
  margin-bottom: 4px;
}

.service-type {
  font-size: 12px;
  color: #718096;
}

.service-divider-grid {
  height: 1px;
  background: #e2e8f0;
  margin: 16px 0;
}

.service-content-grid {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-summary {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 16px;
}

.detail-section-grid {
  margin-bottom: 16px;
}

.detail-section-grid h4 {
  font-size: 12px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.detail-section-grid p {
  font-size: 12px;
  color: #4a5568;
  line-height: 1.4;
  margin: 0 0 8px 0;
}

.detail-section-grid ul {
  margin: 0;
  padding-left: 16px;
}

.detail-section-grid li {
  font-size: 11px;
  color: #4a5568;
  line-height: 1.4;
  margin-bottom: 3px;
}

.rush-option-grid {
  margin-top: auto;
}

.rush-option-grid p {
  font-size: 11px;
  color: #4a5568;
  margin: 0;
}

.service-actions-grid {
  margin-top: auto;
  padding-top: 16px;
}

.service-actions-grid .ant-btn {
  width: 100%;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
}

.service-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 0 24px;
}

.service-content {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-summary {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 24px;
}

.service-details {
  flex: 1;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.detail-section p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
  margin: 0;
}

.detail-section ul {
  margin: 0;
  padding-left: 16px;
}

.detail-section li {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 4px;
}

.rush-option {
  margin-top: auto;
}

.rush-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 16px 0;
}

.rush-option p {
  font-size: 14px;
  color: #4a5568;
  margin: 0;
}

.service-actions {
  padding: 24px;
  border-top: 1px solid #e2e8f0;
}

.service-actions .ant-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

/* Journal Selection */
.journal-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.journal-service {
  margin-top: 60px;
}

.journal-card {
  max-width: 800px;
  margin: 0 auto;
}

.journal-card .service-header {
  padding: 32px 32px 0 32px;
}

.journal-card .service-info h4 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.journal-card .service-info p {
  font-size: 16px;
  color: #4a5568;
  margin: 0;
}

/* Formatting Services */
.formatting-section {
  padding: 80px 0;
  background: white;
}

.formatting-services-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-top: 60px;
}

.formatting-card {
  background: white;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.formatting-card:hover {
  border-color: #ff6b35;
  box-shadow: 0 8px 30px rgba(255, 107, 53, 0.15);
}

.formatting-card .service-link {
  display: flex;
  flex-direction: column;
  text-decoration: none;
  color: inherit;
  flex: 1;
}

.formatting-card .service-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.formatting-card .service-content {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.formatting-card .service-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.formatting-card .service-content p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
  flex: 1;
}

.formatting-card .service-footer {
  padding: 0 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.formatting-card .service-price {
  font-size: 16px;
  font-weight: 600;
  color: #ff6b35;
}

.formatting-card .service-price-note {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.4;
  flex: 1;
  margin-right: 16px;
}

.formatting-card .service-price-note .highlight {
  color: #ff6b35;
  font-weight: 600;
}

.formatting-card .order-btn {
  background: #4285f4;
  border-color: #4285f4;
  flex-shrink: 0;
}

.formatting-card .order-btn:hover {
  background: #3367d6;
  border-color: #3367d6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .formatting-services-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* Payment Methods */
.payment-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.payment-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.payment-content-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 50px;
  margin-top: 50px;
}

.payment-methods {
  flex: 1.3;
  max-width: 520px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.payment-card {
  background: white;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  transition: none;
}

.payment-card:hover {
  transform: none;
  box-shadow: none;
}

.payment-icon {
  width: 40px;
  height: 40px;
  background: none;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #1890ff;
  font-size: 40px;
}

.payment-icon svg {
  width: 40px;
  height: 40px;
  color: #1890ff;
}

.payment-content h4 {
  margin: 0 0 6px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
}

.payment-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
  font-size: 14px;
}

.payment-image {
  flex: 0.8;
  text-align: center;
  max-width: 450px;
}

.payment-image img {
  width: 100%;
  max-width: 450px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

@media (max-width: 768px) {
  .payment-content-wrapper {
    flex-direction: column;
    gap: 40px;
  }
}

/* FAQ */
.faq-section {
  padding: 80px 0;
  background: white;
}

.faq-list {
  margin-top: 60px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Translation Service */
.translation-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.translation-service {
  margin-top: 60px;
}

.translation-card {
  max-width: 800px;
  margin: 0 auto;
}

.translation-card .service-header {
  padding: 32px 32px 0 32px;
}

.translation-card .service-info h4 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.translation-card .service-info p {
  font-size: 16px;
  color: #4a5568;
  margin: 0;
}

.contact-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.contact-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 40px;
}

@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .section-header h1 {
    font-size: 36px;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .quote-calculator {
    padding: 24px;
  }

  .contact-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
