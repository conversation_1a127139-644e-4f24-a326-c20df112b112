<template>
  <div class="personal-info">
    <div class="section-header">
      <h3>{{ t('accountSettings.personalInfo.title') }}</h3>
      <p>{{ t('accountSettings.personalInfo.subtitle') }}</p>
    </div>

    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <a-row :gutter="24">
        <!-- 头像上传 -->
        <a-col :span="24">
          <div class="avatar-section">
            <a-avatar :size="80" :src="formData.avatar">
              {{ formData.firstName?.charAt(0) }}{{ formData.lastName?.charAt(0) }}
            </a-avatar>
            <div class="avatar-actions">
              <a-upload
                :show-upload-list="false"
                :before-upload="beforeUpload"
                accept="image/*"
              >
                <a-button type="primary" ghost>
                  {{ t('accountSettings.personalInfo.buttons.uploadAvatar') }}
                </a-button>
              </a-upload>
            </div>
          </div>
        </a-col>

        <!-- 姓名 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="firstName" :label="t('accountSettings.personalInfo.form.firstName.label')">
            <a-input
              v-model:value="formData.firstName"
              :placeholder="t('accountSettings.personalInfo.form.firstName.placeholder')"
            />
          </a-form-item>
        </a-col>

        <a-col :xs="24" :sm="12">
          <a-form-item name="lastName" :label="t('accountSettings.personalInfo.form.lastName.label')">
            <a-input
              v-model:value="formData.lastName"
              :placeholder="t('accountSettings.personalInfo.form.lastName.placeholder')"
            />
          </a-form-item>
        </a-col>

        <!-- 邮箱 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="email" :label="t('accountSettings.personalInfo.form.email.label')">
            <a-input
              v-model:value="formData.email"
              :placeholder="t('accountSettings.personalInfo.form.email.placeholder')"
              disabled
            />
            <div class="verification-status">
              <span v-if="formData.emailVerified" class="verified">
                <CheckCircleOutlined />
                {{ t('accountSettings.personalInfo.verification.emailVerified') }}
              </span>
              <span v-else class="not-verified">
                <ExclamationCircleOutlined />
                {{ t('accountSettings.personalInfo.verification.emailNotVerified') }}
                <a-button type="link" size="small" @click="sendVerificationEmail">
                  {{ t('accountSettings.personalInfo.verification.sendVerification') }}
                </a-button>
              </span>
            </div>
          </a-form-item>
        </a-col>

        <!-- 手机号 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="phone" :label="t('accountSettings.personalInfo.form.phone.label')">
            <a-input
              v-model:value="formData.phone"
              :placeholder="t('accountSettings.personalInfo.form.phone.placeholder')"
            />
            <div class="verification-status">
              <span v-if="formData.phoneVerified" class="verified">
                <CheckCircleOutlined />
                {{ t('accountSettings.personalInfo.verification.phoneVerified') }}
              </span>
              <span v-else class="not-verified">
                <ExclamationCircleOutlined />
                {{ t('accountSettings.personalInfo.verification.phoneNotVerified') }}
                <a-button type="link" size="small" @click="verifyPhone">
                  {{ t('accountSettings.personalInfo.verification.verifyPhone') }}
                </a-button>
              </span>
            </div>
          </a-form-item>
        </a-col>

        <!-- 所属机构 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="organization" :label="t('accountSettings.personalInfo.form.organization.label')">
            <a-input
              v-model:value="formData.organization"
              :placeholder="t('accountSettings.personalInfo.form.organization.placeholder')"
            />
          </a-form-item>
        </a-col>

        <!-- 国家/地区 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="country" :label="t('accountSettings.personalInfo.form.country.label')">
            <a-select
              v-model:value="formData.country"
              :placeholder="t('accountSettings.personalInfo.form.country.placeholder')"
              show-search
              :filter-option="filterCountry"
            >
              <a-select-option v-for="country in countries" :key="country.code" :value="country.name">
                {{ country.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 职位/头衔 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="title" :label="t('accountSettings.personalInfo.form.title.label')">
            <a-input
              v-model:value="formData.title"
              :placeholder="t('accountSettings.personalInfo.form.title.placeholder')"
            />
          </a-form-item>
        </a-col>

        <!-- 研究领域 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="researchField" :label="t('accountSettings.personalInfo.form.researchField.label')">
            <a-input
              v-model:value="formData.researchField"
              :placeholder="t('accountSettings.personalInfo.form.researchField.placeholder')"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <a-button type="primary" html-type="submit" :loading="saving">
          {{ t('accountSettings.personalInfo.buttons.save') }}
        </a-button>
        <a-button @click="resetForm">
          {{ t('accountSettings.personalInfo.buttons.cancel') }}
        </a-button>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useI18n } from '@/locales/useI18n';
import { useUserStore } from '@/store/modules/user';
import type { UserInfo } from '@/store/modules/user';

const { t } = useI18n();
const userStore = useUserStore();

// 响应式数据
const formRef = ref();
const saving = ref(false);

// 表单数据
const formData = reactive<Partial<UserInfo>>({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  organization: '',
  country: '',
  title: '',
  researchField: '',
  avatar: '',
  emailVerified: false,
  phoneVerified: false,
});

// 表单验证规则
const rules = {
  firstName: [
    { required: true, message: t('accountSettings.personalInfo.form.firstName.required') },
  ],
  lastName: [
    { required: true, message: t('accountSettings.personalInfo.form.lastName.required') },
  ],
  email: [
    { required: true, message: t('accountSettings.personalInfo.form.email.required') },
    { type: 'email', message: t('accountSettings.personalInfo.form.email.invalid') },
  ],
};

// 国家列表（简化版）
const countries = [
  { code: 'CN', name: 'China' },
  { code: 'US', name: 'United States' },
  { code: 'UK', name: 'United Kingdom' },
  { code: 'JP', name: 'Japan' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
  { code: 'CA', name: 'Canada' },
  { code: 'AU', name: 'Australia' },
  // 可以添加更多国家
];

// 方法
const loadUserData = () => {
  const userInfo = userStore.getUserInfo;
  if (userInfo) {
    Object.assign(formData, userInfo);
  }
};

const handleSubmit = async () => {
  try {
    saving.value = true;
    
    // 更新用户信息
    userStore.updateUserInfo(formData as UserInfo);
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success(t('accountSettings.personalInfo.messages.saveSuccess'));
  } catch (error) {
    message.error(t('accountSettings.personalInfo.messages.saveError'));
  } finally {
    saving.value = false;
  }
};

const resetForm = () => {
  loadUserData();
  formRef.value?.clearValidate();
};

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('只能上传图片文件！');
    return false;
  }
  
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB！');
    return false;
  }
  
  // 模拟上传
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.avatar = e.target?.result as string;
  };
  reader.readAsDataURL(file);
  
  return false; // 阻止自动上传
};

const sendVerificationEmail = async () => {
  try {
    await userStore.sendVerificationEmail();
    message.success(t('accountSettings.personalInfo.messages.verificationSent'));
  } catch (error) {
    message.error('发送验证邮件失败');
  }
};

const verifyPhone = () => {
  // 这里可以打开手机验证对话框
  message.info('手机验证功能开发中...');
};

const filterCountry = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase());
};

// 生命周期
onMounted(() => {
  loadUserData();
});
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';

.personal-info {
  .section-header {
    margin-bottom: @padding-xl;
    
    h3 {
      margin: 0 0 @padding-xs;
      font-size: 20px;
      font-weight: 600;
      color: @gray-10;
    }
    
    p {
      margin: 0;
      color: @gray-7;
    }
  }
  
  .avatar-section {
    display: flex;
    align-items: center;
    gap: @padding-lg;
    margin-bottom: @padding-lg;
    padding: @padding-lg;
    background: @gray-2;
    border-radius: @border-radius-base;
    
    .avatar-actions {
      display: flex;
      flex-direction: column;
      gap: @padding-xs;
    }
  }
  
  .verification-status {
    margin-top: @padding-xs;
    font-size: @font-size-sm;
    
    .verified {
      color: @success-color;
      
      .anticon {
        margin-right: 4px;
      }
    }
    
    .not-verified {
      color: @warning-color;
      
      .anticon {
        margin-right: 4px;
      }
    }
  }
  
  .form-actions {
    display: flex;
    gap: @padding-md;
    margin-top: @padding-xl;
    padding-top: @padding-lg;
    border-top: 1px solid @border-color-base;
  }
}

@media (max-width: @screen-sm) {
  .personal-info {
    .avatar-section {
      flex-direction: column;
      text-align: center;
    }
    
    .form-actions {
      flex-direction: column;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}
</style>
