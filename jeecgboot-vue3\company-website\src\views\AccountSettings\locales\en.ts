export default {
  accountSettings: {
    title: 'Account Settings',
    tabs: {
      personalInfo: 'Personal Info',
      security: 'Security',
      preferences: 'Preferences',
      orderHistory: 'Order History',
    },
    
    // Personal Information
    personalInfo: {
      title: 'Personal Information',
      subtitle: 'Manage your profile and contact information',
      form: {
        firstName: {
          label: 'First Name',
          placeholder: 'Enter your first name',
          required: 'Please enter your first name',
        },
        lastName: {
          label: 'Last Name',
          placeholder: 'Enter your last name',
          required: 'Please enter your last name',
        },
        email: {
          label: 'Email Address',
          placeholder: 'Enter your email address',
          required: 'Please enter your email address',
          invalid: 'Please enter a valid email address',
        },
        phone: {
          label: 'Phone Number',
          placeholder: 'Enter your phone number',
        },
        organization: {
          label: 'Organization',
          placeholder: 'Enter your organization',
        },
        country: {
          label: 'Country/Region',
          placeholder: 'Select your country/region',
        },
        title: {
          label: 'Title/Position',
          placeholder: 'Enter your title or academic position',
        },
        researchField: {
          label: 'Research Field',
          placeholder: 'Enter your primary research field',
        },
      },
      verification: {
        emailVerified: 'Email Verified',
        emailNotVerified: 'Email Not Verified',
        phoneVerified: 'Phone Verified',
        phoneNotVerified: 'Phone Not Verified',
        sendVerification: 'Send Verification Email',
        verifyPhone: 'Verify Phone',
      },
      buttons: {
        save: 'Save Changes',
        cancel: 'Cancel',
        uploading: 'Uploading...',
        uploadAvatar: 'Upload Avatar',
      },
      messages: {
        saveSuccess: 'Personal information saved successfully',
        saveError: 'Failed to save, please try again',
        verificationSent: 'Verification email sent',
      },
    },
    
    // Account Security
    security: {
      title: 'Account Security',
      subtitle: 'Manage your password and security settings',
      changePassword: {
        title: 'Change Password',
        currentPassword: {
          label: 'Current Password',
          placeholder: 'Enter your current password',
          required: 'Please enter your current password',
        },
        newPassword: {
          label: 'New Password',
          placeholder: 'Enter your new password',
          required: 'Please enter your new password',
          minLength: 'Password must be at least 8 characters',
        },
        confirmPassword: {
          label: 'Confirm New Password',
          placeholder: 'Confirm your new password',
          required: 'Please confirm your new password',
          mismatch: 'Passwords do not match',
        },
        button: 'Update Password',
        success: 'Password updated successfully',
        error: 'Failed to update password',
      },
      twoFactor: {
        title: 'Two-Factor Authentication',
        description: 'Add an extra layer of security to your account',
        enabled: 'Enabled',
        disabled: 'Disabled',
        enable: 'Enable',
        disable: 'Disable',
        success: 'Two-factor authentication settings updated',
      },
      loginHistory: {
        title: 'Login History',
        lastLogin: 'Last Login',
        device: 'Device',
        location: 'Location',
        noHistory: 'No login history available',
      },
    },
    
    // Preferences
    preferences: {
      title: 'Preferences',
      subtitle: 'Customize your experience',
      language: {
        label: 'Language',
        description: 'Choose your interface language',
      },
      timezone: {
        label: 'Timezone',
        description: 'Set your timezone',
      },
      notifications: {
        title: 'Notification Settings',
        email: {
          label: 'Email Notifications',
          description: 'Receive important email notifications',
        },
        sms: {
          label: 'SMS Notifications',
          description: 'Receive important SMS notifications',
        },
        marketing: {
          label: 'Marketing Emails',
          description: 'Receive product updates and promotional offers',
        },
      },
      display: {
        title: 'Display Settings',
        theme: {
          label: 'Theme',
          light: 'Light',
          dark: 'Dark',
          auto: 'System',
        },
        currency: {
          label: 'Currency',
          description: 'Currency for price display',
        },
        dateFormat: {
          label: 'Date Format',
          description: 'Date display format',
        },
      },
      buttons: {
        save: 'Save Settings',
        reset: 'Reset to Default',
      },
      messages: {
        saveSuccess: 'Preferences saved successfully',
        resetSuccess: 'Reset to default settings',
      },
    },
    
    // Order History
    orderHistory: {
      title: 'Order History',
      subtitle: 'View all your order records',
      filters: {
        status: 'Status',
        dateRange: 'Date Range',
        service: 'Service Type',
        all: 'All',
      },
      table: {
        orderId: 'Order ID',
        service: 'Service',
        status: 'Status',
        amount: 'Amount',
        date: 'Date',
        actions: 'Actions',
      },
      status: {
        pending: 'Pending',
        processing: 'Processing',
        completed: 'Completed',
        cancelled: 'Cancelled',
        refunded: 'Refunded',
      },
      actions: {
        view: 'View Details',
        download: 'Download Files',
        reorder: 'Reorder',
      },
      empty: 'No order records found',
      pagination: {
        total: 'Total {total} records',
        pageSize: 'Items per page',
      },
    },
    
    // Common
    common: {
      loading: 'Loading...',
      saving: 'Saving...',
      saved: 'Saved',
      error: 'Operation failed',
      confirm: 'Confirm',
      cancel: 'Cancel',
      edit: 'Edit',
      delete: 'Delete',
      required: 'This field is required',
    },
  },
};
