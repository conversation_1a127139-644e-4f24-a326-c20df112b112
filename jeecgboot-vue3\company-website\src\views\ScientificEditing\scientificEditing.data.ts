import { CheckOutlined, EditOutlined, TeamOutlined, SafetyOutlined } from '@ant-design/icons-vue';

// 服务介绍特点
export const serviceIntroFeatures = [
  { key: 'assessment', icon: TeamOutlined },
  { key: 'strategic', icon: EditOutlined },
  { key: 'report', icon: CheckOutlined },
  { key: 'support', icon: SafetyOutlined },
];

// 深度编辑包含内容
export const deepEditingItems = ['strategic', 'suggestions', 'language', 'report'];

// 论文科学编辑流程
export const processItems = ['matching', 'meeting', 'editing', 'review', 'secondReview', 'delivery'];

// 定制化详细报告内容
export const reportItems = ['issues', 'evaluation', 'suggestions', 'documents'];

// 服务优势
export const serviceAdvantages = ['highlight', 'guidance', 'feedback'];

// 编辑团队优势
export const editorTeamAdvantages = ['degree', 'training', 'knowledge'];

// 服务涵盖的检查方面
export const checkAspects = ['argument', 'structure', 'methodology', 'background', 'figures', 'details', 'materials'];

// 编辑团队
export const editorTeam = [
  {
    key: 'keri',
    name: '<PERSON><PERSON>',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face&auto=format',
  },
  {
    key: 'courey',
    name: 'Courey Averett',
    avatar: 'https://www.aje.cn/externalimages/aje-cms-production/dd651479-9020-4e44-ac44-2e3254b65bb9_Keri_Tabb_cropped.jpg?auto=compress,format',
  },
  {
    key: 'jen',
    name: 'Jen Kirchhoff',
    avatar:
      'https://www.aje.cn/externalimages/aje-cms-production/911e6e62-73f8-423c-8261-843bf00b963e_Courey_Averett_cropped.jpg?auto=compress,format',
  },
  {
    key: 'kermani',
    name: 'Z Kermani',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face&auto=format',
  },
  {
    key: 'alyson',
    name: 'Alyson Mack',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face&auto=format',
  },
  {
    key: 'bart',
    name: 'Bart Phillips',
    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=80&h=80&fit=crop&crop=face&auto=format',
  },
];

// 客户评价
export const testimonials = [{ key: 'testimonial1' }, { key: 'testimonial2' }];

// 相关服务
export const relatedServices = [
  {
    key: 'formatting',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAvPdc1huKlrb_manuscript-formatting.png?auto=format,compress&w=60',
    link: 'https://www.aje.cn/services/formatting',
  },
  {
    key: 'figures',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/66ce856e-1f94-4f32-a0e7-8c8126fb6aa3_figure-formatting-128.png?auto=compress,format&w=60',
    link: 'https://www.aje.cn/services/figures',
  },
  {
    key: 'journal',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAfPdc1huKlra_journal-recommendation.png?auto=format,compress&w=60',
    link: 'https://www.aje.cn/services/journal-recommendation',
  },
];

// FAQ数据
export const faqData = [{ key: 'suitable' }, { key: 'guarantee' }, { key: 'difference' }, { key: 'publications' }, { key: 'contact' }];
