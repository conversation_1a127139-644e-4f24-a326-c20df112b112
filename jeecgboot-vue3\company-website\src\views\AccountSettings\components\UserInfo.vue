<template>
  <div class="user-info">
    <div class="section-header">
      <h3>{{ t('accountSettings.userInfo.title') }}</h3>
      <p>{{ t('accountSettings.userInfo.subtitle') }}</p>
    </div>

    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <a-row :gutter="24">
        <!-- 姓名 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="firstName" :label="t('accountSettings.userInfo.form.firstName.label')">
            <a-input
              v-model:value="formData.firstName"
              :placeholder="t('accountSettings.userInfo.form.firstName.placeholder')"
            />
          </a-form-item>
        </a-col>

        <a-col :xs="24" :sm="12">
          <a-form-item name="lastName" :label="t('accountSettings.userInfo.form.lastName.label')">
            <a-input
              v-model:value="formData.lastName"
              :placeholder="t('accountSettings.userInfo.form.lastName.placeholder')"
            />
          </a-form-item>
        </a-col>

        <!-- 邮箱 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="email" :label="t('accountSettings.userInfo.form.email.label')">
            <a-input
              v-model:value="formData.email"
              :placeholder="t('accountSettings.userInfo.form.email.placeholder')"
              disabled
            />
          </a-form-item>
        </a-col>

        <!-- 手机号 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="phone" :label="t('accountSettings.userInfo.form.phone.label')">
            <a-input
              v-model:value="formData.phone"
              :placeholder="t('accountSettings.userInfo.form.phone.placeholder')"
            />
          </a-form-item>
        </a-col>

        <!-- 国家/地区 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="country" :label="t('accountSettings.userInfo.form.country.label')">
            <a-select
              v-model:value="formData.country"
              :placeholder="t('accountSettings.userInfo.form.country.placeholder')"
              show-search
              :filter-option="filterCountry"
            >
              <a-select-option v-for="country in countries" :key="country.code" :value="country.name">
                {{ country.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 首选语言 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="language" :label="t('accountSettings.userInfo.form.language.label')">
            <a-select
              v-model:value="formData.language"
              :placeholder="t('accountSettings.userInfo.form.language.placeholder')"
            >
              <a-select-option value="zh-CN">中文（简体）</a-select-option>
              <a-select-option value="en">English</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <a-button type="primary" html-type="submit" :loading="saving">
          {{ t('accountSettings.userInfo.buttons.save') }}
        </a-button>
        <a-button @click="resetForm">
          {{ t('accountSettings.userInfo.buttons.cancel') }}
        </a-button>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from '@/locales/useI18n';
import { useUserStore } from '@/store/modules/user';
import type { UserInfo } from '@/store/modules/user';

const { t } = useI18n();
const userStore = useUserStore();

// 响应式数据
const formRef = ref();
const saving = ref(false);

// 表单数据
const formData = reactive<Partial<UserInfo>>({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  country: '',
  language: 'zh-CN',
});

// 表单验证规则
const rules = {
  firstName: [
    { required: true, message: t('accountSettings.userInfo.form.firstName.required') },
  ],
  lastName: [
    { required: true, message: t('accountSettings.userInfo.form.lastName.required') },
  ],
  email: [
    { required: true, message: t('accountSettings.userInfo.form.email.required') },
    { type: 'email', message: t('accountSettings.userInfo.form.email.invalid') },
  ],
};

// 国家列表
const countries = [
  { code: 'CN', name: 'China' },
  { code: 'US', name: 'United States' },
  { code: 'UK', name: 'United Kingdom' },
  { code: 'JP', name: 'Japan' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
  { code: 'CA', name: 'Canada' },
  { code: 'AU', name: 'Australia' },
  { code: 'KR', name: 'South Korea' },
  { code: 'SG', name: 'Singapore' },
];

// 方法
const loadUserData = () => {
  const userInfo = userStore.getUserInfo;
  if (userInfo) {
    Object.assign(formData, {
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      email: userInfo.email,
      phone: userInfo.phone,
      country: userInfo.country,
      language: userInfo.language,
    });
  }
};

const handleSubmit = async () => {
  try {
    saving.value = true;
    
    // 更新用户信息
    userStore.updateUserInfo(formData as UserInfo);
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success(t('accountSettings.userInfo.messages.saveSuccess'));
  } catch (error) {
    message.error(t('accountSettings.userInfo.messages.saveError'));
  } finally {
    saving.value = false;
  }
};

const resetForm = () => {
  loadUserData();
  formRef.value?.clearValidate();
};

const filterCountry = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase());
};

// 生命周期
onMounted(() => {
  loadUserData();
});
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';

.user-info {
  .section-header {
    margin-bottom: @padding-xl;
    
    h3 {
      margin: 0 0 @padding-xs;
      font-size: 20px;
      font-weight: 600;
      color: @gray-10;
    }
    
    p {
      margin: 0;
      color: @gray-7;
    }
  }
  
  .form-actions {
    display: flex;
    gap: @padding-md;
    margin-top: @padding-xl;
    padding-top: @padding-lg;
    border-top: 1px solid @border-color-base;
  }
}

@media (max-width: @screen-sm) {
  .user-info {
    .form-actions {
      flex-direction: column;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}
</style>
