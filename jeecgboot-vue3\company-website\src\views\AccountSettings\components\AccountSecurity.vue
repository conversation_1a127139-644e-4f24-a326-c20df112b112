<template>
  <div class="account-security">
    <div class="section-header">
      <h3>{{ t('accountSettings.security.title') }}</h3>
      <p>{{ t('accountSettings.security.subtitle') }}</p>
    </div>

    <!-- 修改密码 -->
    <div class="security-section">
      <h4>{{ t('accountSettings.security.changePassword.title') }}</h4>
      <a-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        layout="vertical"
        @finish="handlePasswordChange"
      >
        <a-row :gutter="24">
          <a-col :xs="24" :md="12">
            <a-form-item 
              name="currentPassword" 
              :label="t('accountSettings.security.changePassword.currentPassword.label')"
            >
              <a-input-password
                v-model:value="passwordForm.currentPassword"
                :placeholder="t('accountSettings.security.changePassword.currentPassword.placeholder')"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :xs="24" :md="12">
            <a-form-item 
              name="newPassword" 
              :label="t('accountSettings.security.changePassword.newPassword.label')"
            >
              <a-input-password
                v-model:value="passwordForm.newPassword"
                :placeholder="t('accountSettings.security.changePassword.newPassword.placeholder')"
              />
            </a-form-item>
          </a-col>
          
          <a-col :xs="24" :md="12">
            <a-form-item 
              name="confirmPassword" 
              :label="t('accountSettings.security.changePassword.confirmPassword.label')"
            >
              <a-input-password
                v-model:value="passwordForm.confirmPassword"
                :placeholder="t('accountSettings.security.changePassword.confirmPassword.placeholder')"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="passwordLoading">
            {{ t('accountSettings.security.changePassword.button') }}
          </a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 双因素认证 -->
    <div class="security-section">
      <h4>{{ t('accountSettings.security.twoFactor.title') }}</h4>
      <p>{{ t('accountSettings.security.twoFactor.description') }}</p>
      
      <div class="two-factor-setting">
        <div class="setting-info">
          <span class="status" :class="{ enabled: userInfo?.twoFactorEnabled }">
            {{ userInfo?.twoFactorEnabled 
              ? t('accountSettings.security.twoFactor.enabled') 
              : t('accountSettings.security.twoFactor.disabled') 
            }}
          </span>
        </div>
        <a-button 
          :type="userInfo?.twoFactorEnabled ? 'default' : 'primary'"
          @click="toggleTwoFactor"
          :loading="twoFactorLoading"
        >
          {{ userInfo?.twoFactorEnabled 
            ? t('accountSettings.security.twoFactor.disable') 
            : t('accountSettings.security.twoFactor.enable') 
          }}
        </a-button>
      </div>
    </div>

    <!-- 登录历史 -->
    <div class="security-section">
      <h4>{{ t('accountSettings.security.loginHistory.title') }}</h4>
      
      <div class="login-history">
        <div class="history-item">
          <div class="history-info">
            <div class="history-time">
              {{ t('accountSettings.security.loginHistory.lastLogin') }}: 
              {{ formatDate(userInfo?.lastLoginAt) }}
            </div>
            <div class="history-details">
              <span class="device">
                {{ t('accountSettings.security.loginHistory.device') }}: Chrome on Windows
              </span>
              <span class="location">
                {{ t('accountSettings.security.loginHistory.location') }}: Beijing, China
              </span>
            </div>
          </div>
          <div class="history-status">
            <a-tag color="green">当前会话</a-tag>
          </div>
        </div>
        
        <!-- 可以添加更多历史记录 -->
        <div class="history-item">
          <div class="history-info">
            <div class="history-time">2024-01-15 14:30:25</div>
            <div class="history-details">
              <span class="device">Safari on iPhone</span>
              <span class="location">Shanghai, China</span>
            </div>
          </div>
          <div class="history-status">
            <a-tag>已结束</a-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 账户删除 -->
    <div class="security-section danger-section">
      <h4>危险操作</h4>
      <p>以下操作不可逆，请谨慎操作</p>
      
      <a-button danger @click="showDeleteConfirm">
        删除账户
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useI18n } from '@/locales/useI18n';
import { useUserStore } from '@/store/modules/user';
import dayjs from 'dayjs';

const { t } = useI18n();
const userStore = useUserStore();

// 响应式数据
const passwordFormRef = ref();
const passwordLoading = ref(false);
const twoFactorLoading = ref(false);

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});

// 计算属性
const userInfo = computed(() => userStore.getUserInfo);

// 密码验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: t('accountSettings.security.changePassword.currentPassword.required') },
  ],
  newPassword: [
    { required: true, message: t('accountSettings.security.changePassword.newPassword.required') },
    { min: 8, message: t('accountSettings.security.changePassword.newPassword.minLength') },
  ],
  confirmPassword: [
    { required: true, message: t('accountSettings.security.changePassword.confirmPassword.required') },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== passwordForm.newPassword) {
          return Promise.reject(t('accountSettings.security.changePassword.confirmPassword.mismatch'));
        }
        return Promise.resolve();
      },
    },
  ],
};

// 方法
const handlePasswordChange = async () => {
  try {
    passwordLoading.value = true;
    
    await userStore.updatePassword(passwordForm.currentPassword, passwordForm.newPassword);
    
    message.success(t('accountSettings.security.changePassword.success'));
    
    // 重置表单
    passwordForm.currentPassword = '';
    passwordForm.newPassword = '';
    passwordForm.confirmPassword = '';
    passwordFormRef.value?.resetFields();
    
  } catch (error) {
    message.error(t('accountSettings.security.changePassword.error'));
  } finally {
    passwordLoading.value = false;
  }
};

const toggleTwoFactor = async () => {
  try {
    twoFactorLoading.value = true;
    
    const newStatus = !userInfo.value?.twoFactorEnabled;
    await userStore.toggleTwoFactor(newStatus);
    
    message.success(t('accountSettings.security.twoFactor.success'));
    
  } catch (error) {
    message.error('操作失败，请重试');
  } finally {
    twoFactorLoading.value = false;
  }
};

const formatDate = (dateString?: string) => {
  if (!dateString) return '-';
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
};

const showDeleteConfirm = () => {
  Modal.confirm({
    title: '确认删除账户',
    content: '删除账户后，您的所有数据将被永久删除且无法恢复。确定要继续吗？',
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      message.info('账户删除功能开发中...');
    },
  });
};
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';

.account-security {
  .section-header {
    margin-bottom: @padding-xl;
    
    h3 {
      margin: 0 0 @padding-xs;
      font-size: 20px;
      font-weight: 600;
      color: @gray-10;
    }
    
    p {
      margin: 0;
      color: @gray-7;
    }
  }
  
  .security-section {
    margin-bottom: @padding-xl;
    padding: @padding-lg;
    background: @gray-2;
    border-radius: @border-radius-base;
    
    h4 {
      margin: 0 0 @padding-md;
      font-size: @font-size-lg;
      font-weight: 500;
      color: @gray-10;
    }
    
    p {
      margin: 0 0 @padding-md;
      color: @gray-7;
    }
    
    &.danger-section {
      border: 1px solid @error-color;
      background: #fff2f0;
      
      h4 {
        color: @error-color;
      }
    }
  }
  
  .two-factor-setting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .setting-info {
      .status {
        padding: 4px 12px;
        border-radius: @border-radius-sm;
        font-size: @font-size-sm;
        background: @gray-4;
        color: @gray-8;
        
        &.enabled {
          background: #f6ffed;
          color: @success-color;
        }
      }
    }
  }
  
  .login-history {
    .history-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: @padding-md;
      margin-bottom: @padding-sm;
      background: white;
      border-radius: @border-radius-sm;
      border: 1px solid @border-color-base;
      
      .history-info {
        .history-time {
          font-weight: 500;
          color: @gray-10;
          margin-bottom: 4px;
        }
        
        .history-details {
          display: flex;
          gap: @padding-md;
          font-size: @font-size-sm;
          color: @gray-7;
        }
      }
    }
  }
}

@media (max-width: @screen-sm) {
  .account-security {
    .two-factor-setting {
      flex-direction: column;
      gap: @padding-md;
      align-items: flex-start;
    }
    
    .login-history {
      .history-item {
        flex-direction: column;
        gap: @padding-sm;
        align-items: flex-start;
        
        .history-details {
          flex-direction: column;
          gap: 4px;
        }
      }
    }
  }
}
</style>
