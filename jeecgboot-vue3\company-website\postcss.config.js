module.exports = {
  plugins: {
    // 处理@import语句
    'postcss-import': {},

    // 支持嵌套语法（与Less兼容）
    'postcss-nested': {},

    // 自动添加浏览器前缀
    autoprefixer: {
      overrideBrowserslist: ['> 1%', 'last 2 versions', 'not dead', 'not ie <= 11', 'Chrome >= 60', 'Firefox >= 60', 'Safari >= 12', 'Edge >= 79'],
      grid: true,
      flexbox: 'no-2009',
    },

    // 生产环境CSS优化
    ...(process.env.NODE_ENV === 'production'
      ? {
          cssnano: {
            preset: [
              'default',
              {
                // 保留重要的注释
                discardComments: {
                  removeAll: false,
                },
                // 不合并相同的规则（避免与UnoCSS冲突）
                mergeRules: false,
                // 保持CSS变量
                reduceIdents: false,
                // 不压缩字体权重值
                minifyFontValues: false,
              },
            ],
          },
        }
      : {}),
  },
};
