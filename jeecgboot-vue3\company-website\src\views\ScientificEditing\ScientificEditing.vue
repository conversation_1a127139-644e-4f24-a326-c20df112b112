<template>
  <div class="scientific-editing-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('scientificEditing.hero.title') }}</h1>
          <p class="hero-description">{{ t('scientificEditing.hero.description') }}</p>
          <div class="hero-actions">
            <a-button type="primary" size="large" @click="handleOrderClick">
              {{ t('scientificEditing.hero.orderNow') }}
            </a-button>
            <a-button size="large" @click="handleQuickQuote">
              {{ t('scientificEditing.hero.quickQuote') }}
            </a-button>
          </div>
        </div>
        <div class="hero-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/2ff95af9-af02-46a1-bffa-2b446df899b8_scientific-editing-128.png?auto=format,compress&w=60"
            :alt="t('scientificEditing.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 服务介绍和特色 -->
    <section class="service-intro-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('scientificEditing.serviceIntro.title') }}</h2>
          <p>{{ t('scientificEditing.serviceIntro.description') }}</p>
        </div>
        <div class="intro-content">
          <div class="intro-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/a0479334-3d9c-46b4-8c39-32c7a139f4de_2020_01_31_aje_corporate-0411.jpg?auto=compress,format&rect=342,0,1365,1365&w=60"
              :alt="t('scientificEditing.serviceIntro.imageAlt')" />
          </div>
          <div class="intro-features">
            <div class="feature-item" v-for="feature in serviceIntroFeatures" :key="feature.key">
              <div class="feature-icon">
                <component :is="feature.icon" />
              </div>
              <div class="feature-content">
                <h4>{{ t(`scientificEditing.serviceIntro.features.${feature.key}.title`) }}</h4>
                <p>{{ t(`scientificEditing.serviceIntro.features.${feature.key}.description`) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 行业领先的科学编辑服务 -->
    <section class="leading-service-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('scientificEditing.leadingService.title') }}</h2>
          <p>{{ t('scientificEditing.leadingService.description') }}</p>
        </div>
        <div class="service-details">
          <div class="detail-section">
            <h3>{{ t('scientificEditing.leadingService.deepEditing.title') }}</h3>
            <ol class="detail-list">
              <li v-for="item in deepEditingItems" :key="item">
                {{ t(`scientificEditing.leadingService.deepEditing.items.${item}`) }}
              </li>
            </ol>
          </div>
          <div class="detail-section">
            <h3>{{ t('scientificEditing.leadingService.process.title') }}</h3>
            <ol class="detail-list">
              <li v-for="item in processItems" :key="item">
                {{ t(`scientificEditing.leadingService.process.items.${item}`) }}
              </li>
            </ol>
          </div>
          <div class="detail-section">
            <h3>{{ t('scientificEditing.leadingService.report.title') }}</h3>
            <ol class="detail-list">
              <li v-for="item in reportItems" :key="item">
                {{ t(`scientificEditing.leadingService.report.items.${item}`) }}
              </li>
            </ol>
          </div>
        </div>
      </div>
    </section>

    <!-- 样本示例 -->
    <section class="sample-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('scientificEditing.sample.title') }}</h2>
          <p>{{ t('scientificEditing.sample.description') }}</p>
        </div>
        <div class="sample-content">
          <div class="sample-grid">
            <div class="sample-item">
              <div class="sample-image">
                <img
                  src="https://www.aje.cn/externalimages/aje-cms-production/Z2L5_pbqstJ98pVX_scientific-editing-example-1.png?auto=format,compress?auto=compress,format&w=60"
                  :alt="t('scientificEditing.sample.manuscript.title')" />
              </div>
              <div class="sample-info">
                <h4>{{ t('scientificEditing.sample.manuscript.title') }}</h4>
                <a-button type="primary" @click="downloadSample('manuscript')">
                  {{ t('scientificEditing.sample.manuscript.download') }}
                </a-button>
              </div>
            </div>
            <div class="sample-item">
              <div class="sample-image">
                <img
                  src="https://www.aje.cn/externalimages/aje-cms-production/62c18f7f-919e-41e4-a1c4-7863679498ca_2020_01_31_aje_corporate-0439+%282%29.jpg?auto=compress,format&rect=342,0,1365,1365&w=60"
                  :alt="t('scientificEditing.sample.report.title')" />
              </div>
              <div class="sample-info">
                <h4>{{ t('scientificEditing.sample.report.title') }}</h4>
                <a-button type="primary" @click="downloadSample('report')">
                  {{ t('scientificEditing.sample.report.download') }}
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心优势 -->
    <section class="advantages-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('scientificEditing.advantages.title') }}</h2>
          <p>{{ t('scientificEditing.advantages.description') }}</p>
        </div>
        <div class="advantages-content">
          <div class="advantages-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/9662cec3-e2f5-4189-9466-bed2930fbdf5_2020_01_31_aje_corporate-0453+%283%29.jpg?auto=compress,format&rect=81,0,1365,1365&w=60"
              :alt="t('scientificEditing.advantages.imageAlt')" />
          </div>
          <div class="advantages-details">
            <div class="advantage-section">
              <h3>{{ t('scientificEditing.advantages.serviceAdvantages.title') }}</h3>
              <ol class="advantage-list">
                <li v-for="item in serviceAdvantages" :key="item">
                  {{ t(`scientificEditing.advantages.serviceAdvantages.items.${item}`) }}
                </li>
              </ol>
            </div>
            <div class="advantage-section">
              <h3>{{ t('scientificEditing.advantages.editorTeam.title') }}</h3>
              <ol class="advantage-list">
                <li v-for="item in editorTeamAdvantages" :key="item">
                  {{ t(`scientificEditing.advantages.editorTeam.items.${item}`) }}
                </li>
              </ol>
            </div>
            <div class="advantage-section">
              <h3>{{ t('scientificEditing.advantages.checkAspects.title') }}</h3>
              <ol class="advantage-list">
                <li v-for="item in checkAspects" :key="item">
                  {{ t(`scientificEditing.advantages.checkAspects.items.${item}`) }}
                </li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 编辑团队 -->
    <section class="editor-team-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('scientificEditing.editorTeam.title') }}</h2>
        </div>
        <div class="editors-grid">
          <div class="editor-card" v-for="editor in editorTeam" :key="editor.key">
            <div class="editor-avatar">
              <img :src="editor.avatar" :alt="editor.name" />
            </div>
            <div class="editor-info">
              <h4>{{ editor.name }}</h4>
              <p class="editor-title">{{ t(`scientificEditing.editorTeam.editors.${editor.key}.title`) }}</p>
              <p class="editor-degree">{{ t(`scientificEditing.editorTeam.editors.${editor.key}.degree`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 客户评价 -->
    <section class="testimonials-section">
      <div class="services-content">
        <div class="testimonials-grid">
          <div class="testimonial-card" v-for="testimonial in testimonials" :key="testimonial.key">
            <div class="testimonial-content">
              <p class="testimonial-text">{{ t(`scientificEditing.testimonials.${testimonial.key}.text`) }}</p>
              <div class="testimonial-author">
                <p class="author-title">{{ t(`scientificEditing.testimonials.${testimonial.key}.title`) }}</p>
                <p class="author-org">{{ t(`scientificEditing.testimonials.${testimonial.key}.organization`) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 相关服务 -->
    <section class="related-services-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('scientificEditing.relatedServices.title') }}</h2>
        </div>
        <div class="related-services-grid">
          <div class="service-card" v-for="service in relatedServices" :key="service.key">
            <div class="service-image">
              <img :src="service.image" :alt="service.key" />
            </div>
            <div class="service-content">
              <h4>{{ t(`scientificEditing.relatedServices.services.${service.key}.title`) }}</h4>
              <p>{{ t(`scientificEditing.relatedServices.services.${service.key}.description`) }}</p>
              <div class="service-price">{{ t(`scientificEditing.relatedServices.services.${service.key}.price`) }}</div>
              <a-button type="primary" @click="handleServiceClick(service)">
                {{ t(`scientificEditing.relatedServices.services.${service.key}.buttonText`) }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ区域 -->
    <section class="faq-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('scientificEditing.faq.title') }}</h2>
          <p>{{ t('scientificEditing.faq.subtitle') }}</p>
        </div>
        <a-collapse v-model:activeKey="activeFaqKeys" class="faq-collapse">
          <a-collapse-panel v-for="faq in faqData" :key="faq.key"
            :header="t(`scientificEditing.faq.items.${faq.key}.question`)">
            <div v-html="t(`scientificEditing.faq.items.${faq.key}.answer`)"></div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="cta-content">
        <div class="cta-actions">
          <a-button type="primary" size="large" @click="handleOrderClick">
            {{ t('scientificEditing.cta.orderNow') }}
          </a-button>
          <a-button size="large" @click="handleQuickQuote">
            {{ t('scientificEditing.cta.quickQuote') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { CheckOutlined, EditOutlined, TeamOutlined, SafetyOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  serviceIntroFeatures,
  deepEditingItems,
  processItems,
  reportItems,
  serviceAdvantages,
  editorTeamAdvantages,
  checkAspects,
  editorTeam,
  testimonials,
  relatedServices,
  faqData
} from './scientificEditing.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 响应式数据
const activeFaqKeys = ref(['1'])

// 方法
const handleOrderClick = () => {
  window.open('https://china.aje.com/cn/researcher/submit/get-started', '_blank')
}

const handleQuickQuote = () => {
  router.push('/pricing')
}

const handleServiceClick = (service: any) => {
  window.open(service.link, '_blank')
}

const downloadSample = (type: string) => {
  if (type === 'manuscript') {
    window.open('https://aje-cms-production.cdn.prismic.io/aje-cms-production/7150bc97-4304-42bc-86a8-78e4954aea92_NRES_Scientific_Editing_edited_manuscript_illustration.docx', '_blank')
  } else if (type === 'report') {
    window.open('https://aje-cms-production.cdn.prismic.io/aje-cms-production/Z2L6JpbqstJ98pVa_Scientific_Editing_Sample_Report_for_Website_UPDATED2023-1.pdf', '_blank')
  }
}

// 生命周期
onMounted(() => {
  console.log('Scientific Editing 页面已加载')
})
</script>

<style scoped>
.scientific-editing-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.hero-image {
  flex-shrink: 0;
}

.hero-image img {
  width: 120px;
  height: 120px;
  object-fit: contain;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 通用区域样式 */
.service-intro-section,
.leading-service-section,
.sample-section,
.advantages-section,
.editor-team-section,
.testimonials-section,
.related-services-section,
.faq-section {
  padding: 80px 0;
}

.service-intro-section {
  background: white;
}

.leading-service-section {
  background: #f8fafc;
}

.sample-section {
  background: white;
}

.advantages-section {
  background: #f8fafc;
}

.editor-team-section {
  background: white;
}

.testimonials-section {
  background: #f8fafc;
}

.related-services-section {
  background: white;
}

.faq-section {
  background: #f8fafc;
}

/* 区域标题 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 服务介绍区域 */
.intro-content {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.intro-image {
  flex-shrink: 0;
}

.intro-image img {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

.intro-features {
  flex: 1;
}

.feature-item {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
}

.feature-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.feature-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.feature-content p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

/* 服务详情 */
.service-details {
  display: grid;
  gap: 40px;
}

.detail-section h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 20px;
}

.detail-list {
  list-style: none;
  padding: 0;
  counter-reset: item;
}

.detail-list li {
  counter-increment: item;
  margin-bottom: 16px;
  padding-left: 40px;
  position: relative;
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

.detail-list li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

/* 样本示例区域 */
.sample-content {
  max-width: 800px;
  margin: 0 auto;
}

.sample-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.sample-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.sample-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.sample-image {
  height: 200px;
  overflow: hidden;
}

.sample-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sample-info {
  padding: 24px;
  text-align: center;
}

.sample-info h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

/* 优势区域 */
.advantages-content {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.advantages-image {
  flex-shrink: 0;
}

.advantages-image img {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

.advantages-details {
  flex: 1;
}

.advantage-section {
  margin-bottom: 40px;
}

.advantage-section:last-child {
  margin-bottom: 0;
}

.advantage-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.advantage-list {
  list-style: none;
  padding: 0;
  counter-reset: item;
}

.advantage-list li {
  counter-increment: item;
  margin-bottom: 12px;
  padding-left: 32px;
  position: relative;
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

.advantage-list li::before {
  content: counter(item);
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
}

/* 编辑团队区域 */
.editors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.editor-card {
  background: white;
  border-radius: 12px;
  padding: 32px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.editor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.editor-avatar {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  border-radius: 50%;
  overflow: hidden;
  background: #f7fafc;
}

.editor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.editor-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.editor-title {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 4px;
}

.editor-degree {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.4;
}

/* 客户评价区域 */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
}

.testimonial-card {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-text {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 20px;
  font-style: italic;
}

.testimonial-author {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.author-title {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.author-org {
  font-size: 14px;
  color: #4a5568;
}

/* 相关服务区域 */
.related-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.service-image {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
}

.service-image img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.service-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.service-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.service-content p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 16px;
  flex: 1;
}

.service-price {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 16px;
}

.service-content .ant-btn {
  width: 100%;
  margin-top: auto;
}

/* FAQ区域 */
.faq-collapse {
  background: transparent;
}

.faq-collapse :deep(.ant-collapse-item) {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.faq-collapse :deep(.ant-collapse-header) {
  padding: 20px 24px;
  font-weight: 600;
  color: #1a202c;
  border-radius: 12px;
}

.faq-collapse :deep(.ant-collapse-content-box) {
  padding: 0 24px 20px;
  color: #4a5568;
  line-height: 1.6;
}

/* CTA区域 */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80px 0;
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .intro-content,
  .advantages-content {
    flex-direction: column;
    gap: 40px;
  }

  .intro-image img,
  .advantages-image img {
    width: 150px;
    height: 150px;
    margin: 0 auto;
  }

  .sample-grid {
    grid-template-columns: 1fr;
  }

  .editors-grid {
    grid-template-columns: 1fr;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .related-services-grid {
    grid-template-columns: 1fr;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-actions .ant-btn {
    width: 200px;
  }
}
</style>
