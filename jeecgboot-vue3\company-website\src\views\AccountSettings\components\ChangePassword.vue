<template>
  <div class="change-password">
    <div class="section-header">
      <h3>{{ t('accountSettings.changePassword.title') }}</h3>
      <p>{{ t('accountSettings.changePassword.subtitle') }}</p>
    </div>

    <div class="password-content">
      <!-- 修改密码表单 -->
      <div class="password-form">
        <a-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          layout="vertical"
          @finish="handlePasswordChange"
        >
          <a-row :gutter="24">
            <a-col :xs="24" :md="16">
              <a-form-item 
                name="currentPassword" 
                :label="t('accountSettings.changePassword.form.currentPassword.label')"
              >
                <a-input-password
                  v-model:value="passwordForm.currentPassword"
                  :placeholder="t('accountSettings.changePassword.form.currentPassword.placeholder')"
                />
              </a-form-item>
              
              <a-form-item 
                name="newPassword" 
                :label="t('accountSettings.changePassword.form.newPassword.label')"
              >
                <a-input-password
                  v-model:value="passwordForm.newPassword"
                  :placeholder="t('accountSettings.changePassword.form.newPassword.placeholder')"
                />
              </a-form-item>
              
              <a-form-item 
                name="confirmPassword" 
                :label="t('accountSettings.changePassword.form.confirmPassword.label')"
              >
                <a-input-password
                  v-model:value="passwordForm.confirmPassword"
                  :placeholder="t('accountSettings.changePassword.form.confirmPassword.placeholder')"
                />
              </a-form-item>
              
              <div class="form-actions">
                <a-button type="primary" html-type="submit" :loading="passwordLoading">
                  {{ t('accountSettings.changePassword.buttons.update') }}
                </a-button>
                <a-button @click="resetPasswordForm">
                  {{ t('accountSettings.changePassword.buttons.cancel') }}
                </a-button>
              </div>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 密码安全提示 -->
      <div class="password-tips">
        <h4>{{ t('accountSettings.changePassword.tips.title') }}</h4>
        <ul>
          <li v-for="tip in t('accountSettings.changePassword.tips.items')" :key="tip">
            {{ tip }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from '@/locales/useI18n';
import { useUserStore } from '@/store/modules/user';

const { t } = useI18n();
const userStore = useUserStore();

// 响应式数据
const passwordFormRef = ref();
const passwordLoading = ref(false);

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});

// 密码验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: t('accountSettings.changePassword.form.currentPassword.required') },
  ],
  newPassword: [
    { required: true, message: t('accountSettings.changePassword.form.newPassword.required') },
    { min: 8, message: t('accountSettings.changePassword.form.newPassword.minLength') },
  ],
  confirmPassword: [
    { required: true, message: t('accountSettings.changePassword.form.confirmPassword.required') },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== passwordForm.newPassword) {
          return Promise.reject(t('accountSettings.changePassword.form.confirmPassword.mismatch'));
        }
        return Promise.resolve();
      },
    },
  ],
};

// 方法
const handlePasswordChange = async () => {
  try {
    passwordLoading.value = true;
    
    await userStore.updatePassword(passwordForm.currentPassword, passwordForm.newPassword);
    
    message.success(t('accountSettings.changePassword.messages.success'));
    
    // 重置表单
    resetPasswordForm();
    
  } catch (error) {
    message.error(t('accountSettings.changePassword.messages.error'));
  } finally {
    passwordLoading.value = false;
  }
};

const resetPasswordForm = () => {
  passwordForm.currentPassword = '';
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
  passwordFormRef.value?.resetFields();
};
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';

.change-password {
  .section-header {
    margin-bottom: @padding-xl;
    
    h3 {
      margin: 0 0 @padding-xs;
      font-size: 20px;
      font-weight: 600;
      color: @gray-10;
    }
    
    p {
      margin: 0;
      color: @gray-7;
    }
  }
  
  .password-content {
    display: flex;
    gap: @padding-xl;
    
    .password-form {
      flex: 1;
      max-width: 600px;
    }
    
    .password-tips {
      flex: 0 0 300px;
      padding: @padding-lg;
      background: @gray-2;
      border-radius: @border-radius-base;
      
      h4 {
        margin: 0 0 @padding-md;
        font-size: @font-size-lg;
        font-weight: 500;
        color: @gray-10;
      }
      
      ul {
        margin: 0;
        padding-left: @padding-lg;
        
        li {
          margin-bottom: @padding-xs;
          color: @gray-7;
          font-size: @font-size-sm;
          line-height: 1.5;
        }
      }
    }
  }
  
  .form-actions {
    display: flex;
    gap: @padding-md;
    margin-top: @padding-lg;
  }
}

@media (max-width: @screen-lg) {
  .change-password {
    .password-content {
      flex-direction: column;
      
      .password-tips {
        flex: none;
        max-width: none;
      }
    }
  }
}

@media (max-width: @screen-sm) {
  .change-password {
    .form-actions {
      flex-direction: column;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}
</style>
