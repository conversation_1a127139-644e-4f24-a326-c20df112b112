<template>
  <div class="api-example">
    <h2>API 使用示例</h2>
    
    <div class="example-section">
      <h3>1. 联系页面 API</h3>
      <a-button @click="testContactApi" :loading="loading.contact">
        测试联系 API
      </a-button>
      <pre v-if="results.contact">{{ results.contact }}</pre>
    </div>

    <div class="example-section">
      <h3>2. 首页 API</h3>
      <a-button @click="testHomeApi" :loading="loading.home">
        测试首页 API
      </a-button>
      <pre v-if="results.home">{{ results.home }}</pre>
    </div>

    <div class="example-section">
      <h3>3. 产品页面 API</h3>
      <a-button @click="testProductsApi" :loading="loading.products">
        测试产品 API
      </a-button>
      <pre v-if="results.products">{{ results.products }}</pre>
    </div>

    <div class="example-section">
      <h3>4. 直接使用 defHttp</h3>
      <a-button @click="testDefHttp" :loading="loading.defHttp">
        测试 defHttp
      </a-button>
      <pre v-if="results.defHttp">{{ results.defHttp }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';

// 导入各页面的 API
import { getContactInfo, submitContactForm } from '@/views/Contact/contact.api';
import { getCompanyStats, getFeaturedProducts } from '@/views/Home/home.api';
import { getProductList, getProductCategories } from '@/views/Products/products.api';
import { defHttp } from '@/utils/request';

const loading = reactive({
  contact: false,
  home: false,
  products: false,
  defHttp: false,
});

const results = reactive({
  contact: '',
  home: '',
  products: '',
  defHttp: '',
});

// 测试联系页面 API
const testContactApi = async () => {
  loading.contact = true;
  try {
    console.log('🧪 测试联系页面 API...');
    
    // 测试获取联系信息
    const contactInfo = await getContactInfo();
    
    results.contact = JSON.stringify({
      message: '联系 API 测试完成',
      contactInfo: contactInfo || '使用默认数据',
      timestamp: new Date().toISOString()
    }, null, 2);
    
    message.success('联系 API 测试成功');
  } catch (error) {
    results.contact = JSON.stringify({
      error: '请求失败 (这是正常的，因为后端接口可能不存在)',
      details: error.message,
      timestamp: new Date().toISOString()
    }, null, 2);
    
    message.info('API 调用正常，后端接口未配置');
  } finally {
    loading.contact = false;
  }
};

// 测试首页 API
const testHomeApi = async () => {
  loading.home = true;
  try {
    console.log('🧪 测试首页 API...');
    
    const stats = await getCompanyStats();
    const products = await getFeaturedProducts({ limit: 3 });
    
    results.home = JSON.stringify({
      message: '首页 API 测试完成',
      stats: stats || '使用默认数据',
      products: products || '使用默认数据',
      timestamp: new Date().toISOString()
    }, null, 2);
    
    message.success('首页 API 测试成功');
  } catch (error) {
    results.home = JSON.stringify({
      error: '请求失败 (这是正常的，因为后端接口可能不存在)',
      details: error.message,
      timestamp: new Date().toISOString()
    }, null, 2);
    
    message.info('API 调用正常，后端接口未配置');
  } finally {
    loading.home = false;
  }
};

// 测试产品页面 API
const testProductsApi = async () => {
  loading.products = true;
  try {
    console.log('🧪 测试产品页面 API...');
    
    const productList = await getProductList({ page: 1, size: 5 });
    const categories = await getProductCategories();
    
    results.products = JSON.stringify({
      message: '产品 API 测试完成',
      productList: productList || '使用默认数据',
      categories: categories || '使用默认数据',
      timestamp: new Date().toISOString()
    }, null, 2);
    
    message.success('产品 API 测试成功');
  } catch (error) {
    results.products = JSON.stringify({
      error: '请求失败 (这是正常的，因为后端接口可能不存在)',
      details: error.message,
      timestamp: new Date().toISOString()
    }, null, 2);
    
    message.info('API 调用正常，后端接口未配置');
  } finally {
    loading.products = false;
  }
};

// 测试 defHttp
const testDefHttp = async () => {
  loading.defHttp = true;
  try {
    console.log('🧪 测试 defHttp...');
    
    // 测试 GET 请求
    const getResult = await defHttp.get({ 
      url: '/test/get', 
      params: { test: 'get-request' } 
    });
    
    // 测试 POST 请求
    const postResult = await defHttp.post({ 
      url: '/test/post', 
      params: { test: 'post-request', data: 'test-data' } 
    });
    
    results.defHttp = JSON.stringify({
      message: 'defHttp 测试完成',
      getResult: getResult || '请求已发送',
      postResult: postResult || '请求已发送',
      timestamp: new Date().toISOString()
    }, null, 2);
    
    message.success('defHttp 测试成功');
  } catch (error) {
    results.defHttp = JSON.stringify({
      error: '请求失败 (这是正常的，因为后端接口可能不存在)',
      details: error.message,
      note: 'defHttp 方法调用正常，类型检查通过',
      timestamp: new Date().toISOString()
    }, null, 2);
    
    message.info('defHttp 调用正常，后端接口未配置');
  } finally {
    loading.defHttp = false;
  }
};
</script>

<style scoped>
.api-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.example-section h3 {
  margin-top: 0;
  color: #1890ff;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  margin-top: 10px;
  font-size: 12px;
}
</style>
