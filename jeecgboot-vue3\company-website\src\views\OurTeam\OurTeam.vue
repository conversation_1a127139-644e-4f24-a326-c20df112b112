<template>
  <div class="our-team-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('ourTeam.hero.title') }}</h1>
          <p class="hero-description">{{ t('ourTeam.hero.description') }}</p>
        </div>
      </div>
    </section>

    <!-- 资深编辑团队 -->
    <section class="senior-editors-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('ourTeam.seniorEditors.title') }}</h2>
        </div>
        <div class="team-grid">
          <div class="team-member-card" v-for="editor in seniorEditors" :key="editor.key">
            <div class="member-avatar">
              <img :src="editor.avatar" :alt="editor.name" />
            </div>
            <div class="member-info">
              <h4>{{ editor.name }}</h4>
              <p class="member-title">{{ t(`ourTeam.seniorEditors.members.${editor.key}.title`) }}</p>
              <p class="member-education">{{ t(`ourTeam.seniorEditors.members.${editor.key}.education`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 客服管理团队 -->
    <section class="customer-service-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('ourTeam.customerService.title') }}</h2>
        </div>
        <div class="team-grid">
          <div class="team-member-card" v-for="member in customerServiceTeam" :key="member.key">
            <div class="member-info">
              <h4>{{ member.name }}</h4>
              <p class="member-title">{{ t(`ourTeam.customerService.members.${member.key}.title`) }}</p>
              <p class="member-education">{{ t(`ourTeam.customerService.members.${member.key}.education`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 研究交流合作伙伴 -->
    <section class="research-partners-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('ourTeam.researchPartners.title') }}</h2>
        </div>
        <div class="team-grid">
          <div class="team-member-card" v-for="partner in researchPartners" :key="partner.key">
            <div class="member-info">
              <h4>{{ partner.name }}</h4>
              <p class="member-title">{{ t(`ourTeam.researchPartners.members.${partner.key}.title`) }}</p>
              <p class="member-education">{{ t(`ourTeam.researchPartners.members.${partner.key}.education`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 翻译和图表专家 -->
    <section class="translation-experts-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('ourTeam.translationExperts.title') }}</h2>
        </div>
        <div class="team-grid">
          <div class="team-member-card" v-for="expert in translationExperts" :key="expert.key">
            <div class="member-info">
              <h4>{{ expert.name }}</h4>
              <p class="member-title">{{ t(`ourTeam.translationExperts.members.${expert.key}.title`) }}</p>
              <p class="member-education">{{ t(`ourTeam.translationExperts.members.${expert.key}.education`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 业务拓展及市场营销团队 -->
    <section class="business-team-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('ourTeam.businessTeam.title') }}</h2>
        </div>
        <div class="team-grid">
          <div class="team-member-card" v-for="member in businessTeam" :key="member.key">
            <div class="member-info">
              <h4>{{ member.name }}</h4>
              <p class="member-title">{{ t(`ourTeam.businessTeam.members.${member.key}.title`) }}</p>
              <p class="member-education" v-if="member.email">{{ member.email }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 客户评价 -->
    <section class="testimonials-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('ourTeam.testimonials.title') }}</h2>
        </div>
        <div class="testimonials-grid">
          <div class="testimonial-card" v-for="testimonial in testimonials" :key="testimonial.key">
            <div class="testimonial-content">
              <div class="testimonial-author">
                <div class="author-avatar">
                  <img :src="testimonial.avatar" :alt="testimonial.name" />
                </div>
                <div class="author-info">
                  <p class="author-name">{{ testimonial.name }}</p>
                  <p class="author-title">{{ t(`ourTeam.testimonials.members.${testimonial.key}.title`) }}</p>
                </div>
              </div>
              <p class="testimonial-text">{{ t(`ourTeam.testimonials.members.${testimonial.key}.text`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('ourTeam.contact.title') }}</h2>
          <p>{{ t('ourTeam.contact.description') }}</p>
        </div>
        <div class="contact-actions">
          <a-button type="primary" size="large" @click="handleAllServices">
            {{ t('ourTeam.contact.allServices') }}
          </a-button>
          <a-button size="large" @click="handleQuickQuote">
            {{ t('ourTeam.contact.quickQuote') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  seniorEditors,
  customerServiceTeam,
  researchPartners,
  translationExperts,
  businessTeam,
  testimonials
} from './ourTeam.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 方法
const handleAllServices = () => {
  window.open('https://www.aje.cn/services/', '_blank')
}

const handleQuickQuote = () => {
  router.push('/pricing')
}

// 生命周期
onMounted(() => {
  console.log('OurTeam 页面已加载')
})
</script>

<style scoped>
.our-team-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  opacity: 0.9;
  max-width: 800px;
  margin: 0 auto;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 通用区域样式 */
.senior-editors-section,
.customer-service-section,
.research-partners-section,
.translation-experts-section,
.business-team-section,
.testimonials-section,
.contact-section {
  padding: 80px 0;
}

.senior-editors-section {
  background: white;
}

.customer-service-section {
  background: #f8fafc;
}

.research-partners-section {
  background: white;
}

.translation-experts-section {
  background: #f8fafc;
}

.business-team-section {
  background: white;
}

.testimonials-section {
  background: #f8fafc;
}

.contact-section {
  background: white;
}

/* 区域标题 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 团队网格 */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

/* 团队成员卡片 */
.team-member-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.team-member-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.customer-service-section .team-member-card,
.research-partners-section .team-member-card,
.translation-experts-section .team-member-card,
.business-team-section .team-member-card {
  background: #f7fafc;
}

.member-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 16px;
  background: #e2e8f0;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.member-title {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 4px;
}

.member-education {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.4;
}

/* 客户评价区域 */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
}

.testimonial-card {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-author {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background: #e2e8f0;
  flex-shrink: 0;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.author-title {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.testimonial-text {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  font-style: italic;
}

/* 联系我们区域 */
.contact-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 36px;
  }

  .team-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .testimonial-author {
    flex-direction: column;
    text-align: center;
  }

  .contact-actions {
    flex-direction: column;
    align-items: center;
  }

  .contact-actions .ant-btn {
    width: 200px;
  }
}
</style>
