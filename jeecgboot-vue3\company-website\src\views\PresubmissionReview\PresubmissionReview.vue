<template>
  <div class="presubmission-review-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('presubmissionReview.hero.title') }}</h1>
          <p class="hero-description">{{ t('presubmissionReview.hero.description') }}</p>
          <div class="hero-actions">
            <a-button type="primary" size="large" @click="handleOrderClick">
              {{ t('presubmissionReview.hero.orderNow') }}
            </a-button>
          </div>
        </div>
        <div class="hero-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/fc927461-7932-461b-9983-cf1441cc454e_presubmission+review+illustration.png?auto=compress,format&w=60"
            :alt="t('presubmissionReview.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 服务内容介绍 -->
    <section class="service-content-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('presubmissionReview.serviceContent.title') }}</h2>
          <p>{{ t('presubmissionReview.serviceContent.description') }}</p>
        </div>
        <div class="content-layout">
          <div class="content-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/3c016ff6-6f2a-42c3-b49a-c0062c65bd53_presubmission-review-847x900-3.jpg?auto=compress,format&w=60"
              :alt="t('presubmissionReview.serviceContent.imageAlt')" />
          </div>
          <div class="content-features">
            <div class="feature-item" v-for="feature in serviceFeatures" :key="feature.key">
              <div class="feature-icon">
                <component :is="feature.icon" />
              </div>
              <div class="feature-content">
                <h4>{{ t(`presubmissionReview.serviceContent.features.${feature.key}.title`) }}</h4>
                <p>{{ t(`presubmissionReview.serviceContent.features.${feature.key}.description`) }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="learn-more-link">
          <a-button type="link" @click="handleLearnMore">
            {{ t('presubmissionReview.serviceContent.learnMore') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 权威认证区域 -->
    <section class="authority-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('presubmissionReview.authority.title') }}</h2>
          <p>{{ t('presubmissionReview.authority.description') }}</p>
        </div>
        <div class="authority-logos">
          <div class="logo-grid">
            <div class="logo-item" v-for="logo in authorityLogos" :key="logo.key">
              <img :src="logo.image" :alt="logo.key" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 相关服务 -->
    <section class="related-services-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('presubmissionReview.relatedServices.title') }}</h2>
        </div>
        <div class="related-services-grid">
          <div class="service-card" v-for="service in relatedServices" :key="service.key">
            <div class="service-image">
              <img :src="service.image" :alt="service.key" />
            </div>
            <div class="service-content">
              <h4>{{ t(`presubmissionReview.relatedServices.services.${service.key}.title`) }}</h4>
              <p>{{ t(`presubmissionReview.relatedServices.services.${service.key}.description`) }}</p>
              <div class="service-price">{{ t(`presubmissionReview.relatedServices.services.${service.key}.price`) }}
              </div>
              <a-button type="primary" @click="handleServiceClick(service)">
                {{ t(`presubmissionReview.relatedServices.services.${service.key}.buttonText`) }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ区域 -->
    <section class="faq-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('presubmissionReview.faq.title') }}</h2>
          <p>{{ t('presubmissionReview.faq.subtitle') }}</p>
        </div>
        <a-collapse v-model:activeKey="activeFaqKeys" class="faq-collapse">
          <a-collapse-panel v-for="faq in faqData" :key="faq.key"
            :header="t(`presubmissionReview.faq.items.${faq.key}.question`)">
            <div v-html="t(`presubmissionReview.faq.items.${faq.key}.answer`)"></div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="cta-content">
        <div class="cta-actions">
          <a-button type="primary" size="large" @click="handleAllServices">
            {{ t('presubmissionReview.cta.allServices') }}
          </a-button>
          <a-button size="large" @click="handleGetQuote">
            {{ t('presubmissionReview.cta.getQuote') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { EditOutlined, CheckCircleOutlined, FileTextOutlined, BulbOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  serviceFeatures,
  authorityLogos,
  relatedServices,
  faqData
} from './presubmissionReview.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 响应式数据
const activeFaqKeys = ref(['1'])

// 方法
const handleOrderClick = () => {
  window.open('https://china.aje.com/cn/researcher/submit/get-started', '_blank')
}

const handleServiceClick = (service: any) => {
  window.open(service.link, '_blank')
}

const handleLearnMore = () => {
  window.open('https://www.aje.cn/arc/aje-pre-review-service-20230901/', '_blank')
}

const handleAllServices = () => {
  window.open('https://www.aje.cn/services/', '_blank')
}

const handleGetQuote = () => {
  router.push('/pricing')
}

// 生命周期
onMounted(() => {
  console.log('PresubmissionReview 页面已加载')
})
</script>

<style scoped>
.presubmission-review-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.hero-image {
  flex-shrink: 0;
}

.hero-image img {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: contain;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 通用区域样式 */
.service-content-section,
.authority-section,
.related-services-section,
.faq-section {
  padding: 80px 0;
}

.service-content-section {
  background: white;
}

.authority-section {
  background: #f8fafc;
}

.related-services-section {
  background: white;
}

.faq-section {
  background: #f8fafc;
}

/* 区域标题 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 服务内容区域 */
.content-layout {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.content-image {
  flex-shrink: 0;
}

.content-image img {
  width: 200px;
  height: 250px;
  border-radius: 12px;
  object-fit: cover;
}

.content-features {
  flex: 1;
}

.feature-item {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
}

.feature-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.feature-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.feature-content p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

.learn-more-link {
  text-align: center;
  margin-top: 32px;
}

/* 权威认证区域 */
.authority-logos {
  max-width: 1000px;
  margin: 0 auto;
}

.logo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 32px;
  align-items: center;
}

.logo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.logo-item:hover {
  transform: translateY(-2px);
}

.logo-item img {
  max-width: 80px;
  max-height: 40px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.logo-item:hover img {
  filter: grayscale(0%);
}

/* 相关服务区域 */
.related-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.service-image {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
}

.service-image img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.service-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.service-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.service-content p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 16px;
  flex: 1;
}

.service-price {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 16px;
}

.service-content .ant-btn {
  width: 100%;
  margin-top: auto;
}

/* FAQ区域 */
.faq-collapse {
  background: transparent;
}

.faq-collapse :deep(.ant-collapse-item) {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.faq-collapse :deep(.ant-collapse-header) {
  padding: 20px 24px;
  font-weight: 600;
  color: #1a202c;
  border-radius: 12px;
}

.faq-collapse :deep(.ant-collapse-content-box) {
  padding: 0 24px 20px;
  color: #4a5568;
  line-height: 1.6;
}

/* CTA区域 */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80px 0;
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .content-layout {
    flex-direction: column;
    gap: 40px;
  }

  .content-image img {
    width: 150px;
    height: 200px;
    margin: 0 auto;
  }

  .logo-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 16px;
  }

  .related-services-grid {
    grid-template-columns: 1fr;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-actions .ant-btn {
    width: 200px;
  }
}
</style>
