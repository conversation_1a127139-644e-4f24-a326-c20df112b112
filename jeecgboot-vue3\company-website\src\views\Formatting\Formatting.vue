<template>
  <div class="formatting-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('formatting.hero.title') }}</h1>
          <p class="hero-description">{{ t('formatting.hero.description') }}</p>
          <div class="hero-actions">
            <a-button type="primary" size="large" @click="handleOrderClick">
              {{ t('formatting.hero.orderNow') }}
            </a-button>
          </div>
        </div>
        <div class="hero-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/a55f9922-ba17-42de-bc52-efd3dd8e8c0c_2018_02_15_aje_event-8050-2+%281%29.jpg?auto=compress,format&rect=0,218,4200,2363&w=60"
            :alt="t('formatting.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 权威认证区域 -->
    <section class="authority-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('formatting.authority.title') }}</h2>
          <p>{{ t('formatting.authority.description') }}</p>
        </div>
        <div class="authority-logos">
          <div class="logo-grid">
            <div class="logo-item" v-for="logo in authorityLogos" :key="logo.key">
              <img :src="logo.image" :alt="logo.key" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 服务特色 -->
    <section class="service-features-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('formatting.serviceFeatures.title') }}</h2>
          <p>{{ t('formatting.serviceFeatures.description') }}</p>
        </div>
        <div class="features-layout">
          <div class="features-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/8e74bbc6-fb0c-4f44-ada2-c5e0cf50a353_stackable+journal+cover+images+square.png?auto=compress,format&w=60"
              :alt="t('formatting.serviceFeatures.imageAlt')" />
          </div>
          <div class="features-list">
            <div class="feature-item" v-for="feature in serviceFeatures" :key="feature.key">
              <div class="feature-icon">
                <component :is="feature.icon" />
              </div>
              <div class="feature-content">
                <h4>{{ t(`formatting.serviceFeatures.features.${feature.key}.title`) }}</h4>
                <p>{{ t(`formatting.serviceFeatures.features.${feature.key}.description`) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 格式排版示例 -->
    <section class="sample-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('formatting.sample.title') }}</h2>
        </div>
        <div class="sample-grid">
          <div class="sample-item" v-for="sample in sampleData" :key="sample.key">
            <div class="sample-content">
              <h3>{{ t(`formatting.sample.items.${sample.key}.title`) }}</h3>
              <p>{{ t(`formatting.sample.items.${sample.key}.description`) }}</p>
              <a-button type="primary" @click="downloadSample(sample.downloadUrl)">
                {{ t(`formatting.sample.items.${sample.key}.download`) }}
              </a-button>
            </div>
            <div class="sample-image">
              <img :src="sample.image" :alt="sample.key" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- AJE质量保证 -->
    <section class="quality-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('formatting.quality.title') }}</h2>
          <p>{{ t('formatting.quality.description') }}</p>
        </div>
        <div class="quality-content">
          <div class="quality-features">
            <div class="quality-item" v-for="item in qualityFeatures" :key="item.key">
              <div class="quality-icon">
                <component :is="item.icon" />
              </div>
              <div class="quality-info">
                <h4>{{ t(`formatting.quality.features.${item.key}.title`) }}</h4>
                <p>{{ t(`formatting.quality.features.${item.key}.description`) }}</p>
              </div>
            </div>
          </div>
          <div class="quality-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/c370fd7d-2ffa-409d-a9f3-c5de9021cbb3_2020_01_31_aje_corporate-1088.jpg?auto=compress,format&w=60"
              :alt="t('formatting.quality.imageAlt')" />
          </div>
        </div>
        <div class="quality-link">
          <a-button type="link" @click="handleLearnMore">
            {{ t('formatting.quality.learnMore') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 客户评价 -->
    <section class="testimonials-section">
      <div class="services-content">
        <div class="testimonials-grid">
          <div class="testimonial-card" v-for="testimonial in testimonials" :key="testimonial.key">
            <div class="testimonial-content">
              <p class="testimonial-text">{{ t(`formatting.testimonials.${testimonial.key}.text`) }}</p>
              <div class="testimonial-author">
                <div class="author-info">
                  <p class="author-name">{{ t(`formatting.testimonials.${testimonial.key}.name`) }}</p>
                  <p class="author-org">{{ t(`formatting.testimonials.${testimonial.key}.organization`) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 相关服务 -->
    <section class="related-services-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('formatting.relatedServices.title') }}</h2>
        </div>
        <div class="related-services-grid">
          <div class="service-card" v-for="service in relatedServices" :key="service.key">
            <div class="service-image">
              <img :src="service.image" :alt="service.key" />
            </div>
            <div class="service-content">
              <h4>{{ t(`formatting.relatedServices.services.${service.key}.title`) }}</h4>
              <p>{{ t(`formatting.relatedServices.services.${service.key}.description`) }}</p>
              <div class="service-price">{{ t(`formatting.relatedServices.services.${service.key}.price`) }}</div>
              <a-button type="primary" @click="handleServiceClick(service)">
                {{ t(`formatting.relatedServices.services.${service.key}.buttonText`) }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ区域 -->
    <section class="faq-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('formatting.faq.title') }}</h2>
          <p>{{ t('formatting.faq.subtitle') }}</p>
        </div>
        <a-collapse v-model:activeKey="activeFaqKeys" class="faq-collapse">
          <a-collapse-panel v-for="faq in faqData" :key="faq.key" :header="t(`formatting.faq.items.${faq.key}.question`)">
            <div v-html="t(`formatting.faq.items.${faq.key}.answer`)"></div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="cta-content">
        <div class="cta-actions">
          <a-button type="primary" size="large" @click="handleOrderClick">
            {{ t('formatting.cta.orderNow') }}
          </a-button>
          <a-button size="large" @click="handleMoreServices">
            {{ t('formatting.cta.moreServices') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { FileTextOutlined, CheckCircleOutlined, EditOutlined, SafetyOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  authorityLogos,
  serviceFeatures,
  sampleData,
  qualityFeatures,
  testimonials,
  relatedServices,
  faqData
} from './formatting.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 响应式数据
const activeFaqKeys = ref(['1'])

// 方法
const handleOrderClick = () => {
  window.open('https://china.aje.com/cn/researcher/submit/get-started', '_blank')
}

const handleServiceClick = (service: any) => {
  window.open(service.link, '_blank')
}

const downloadSample = (url: string) => {
  window.open(url, '_blank')
}

const handleLearnMore = () => {
  window.open('https://www.aje.cn/guarantee', '_blank')
}

const handleMoreServices = () => {
  window.open('https://www.aje.cn/services/', '_blank')
}

// 生命周期
onMounted(() => {
  console.log('Formatting 页面已加载')
})
</script>

<style scoped>
.formatting-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.hero-image {
  flex-shrink: 0;
}

.hero-image img {
  width: 200px;
  height: 120px;
  border-radius: 12px;
  object-fit: cover;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 通用区域样式 */
.authority-section,
.service-features-section,
.sample-section,
.quality-section,
.testimonials-section,
.related-services-section,
.faq-section {
  padding: 80px 0;
}

.authority-section {
  background: white;
}

.service-features-section {
  background: #f8fafc;
}

.sample-section {
  background: white;
}

.quality-section {
  background: #f8fafc;
}

.testimonials-section {
  background: white;
}

.related-services-section {
  background: #f8fafc;
}

.faq-section {
  background: white;
}

/* 区域标题 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 权威认证区域 */
.authority-logos {
  max-width: 1000px;
  margin: 0 auto;
}

.logo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 32px;
  align-items: center;
}

.logo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: #f7fafc;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.logo-item:hover {
  transform: translateY(-2px);
}

.logo-item img {
  max-width: 80px;
  max-height: 40px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.logo-item:hover img {
  filter: grayscale(0%);
}

/* 服务特色区域 */
.features-layout {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.features-image {
  flex-shrink: 0;
}

.features-image img {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

.features-list {
  flex: 1;
}

.feature-item {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
}

.feature-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.feature-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.feature-content p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

/* 样本示例区域 */
.sample-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 60px;
}

.sample-item {
  display: flex;
  gap: 60px;
  align-items: center;
}

.sample-item:nth-child(even) {
  flex-direction: row-reverse;
}

.sample-content {
  flex: 1;
}

.sample-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.sample-content p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 24px;
}

.sample-image {
  flex-shrink: 0;
}

.sample-image img {
  width: 200px;
  height: 250px;
  border-radius: 12px;
  object-fit: cover;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 质量保证区域 */
.quality-content {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.quality-features {
  flex: 1;
}

.quality-item {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
}

.quality-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.quality-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.quality-info p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

.quality-image {
  flex-shrink: 0;
}

.quality-image img {
  width: 200px;
  height: 150px;
  border-radius: 12px;
  object-fit: cover;
}

.quality-link {
  text-align: center;
  margin-top: 32px;
}

/* 客户评价区域 */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 32px;
}

.testimonial-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-text {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 20px;
  font-style: italic;
}

.testimonial-author {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.author-org {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.4;
}

/* 相关服务区域 */
.related-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.service-image {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
}

.service-image img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.service-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.service-content h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.service-content p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 16px;
  flex: 1;
}

.service-price {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 16px;
}

.service-content .ant-btn {
  width: 100%;
  margin-top: auto;
}

/* FAQ区域 */
.faq-collapse {
  background: transparent;
}

.faq-collapse :deep(.ant-collapse-item) {
  background: #f7fafc;
  border-radius: 12px;
  margin-bottom: 16px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.faq-collapse :deep(.ant-collapse-header) {
  padding: 20px 24px;
  font-weight: 600;
  color: #1a202c;
  border-radius: 12px;
}

.faq-collapse :deep(.ant-collapse-content-box) {
  padding: 0 24px 20px;
  color: #4a5568;
  line-height: 1.6;
}

/* CTA区域 */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80px 0;
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .features-layout,
  .quality-content {
    flex-direction: column;
    gap: 40px;
  }

  .sample-item {
    flex-direction: column !important;
    gap: 40px;
  }

  .features-image img,
  .quality-image img,
  .sample-image img {
    width: 150px;
    height: 150px;
    margin: 0 auto;
  }

  .logo-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 16px;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .related-services-grid {
    grid-template-columns: 1fr;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-actions .ant-btn {
    width: 200px;
  }
}
</style>
