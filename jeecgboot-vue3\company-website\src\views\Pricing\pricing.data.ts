// 价格页面数据配置

export const editingServicesData = [
  {
    id: 'standard',
    badge: '经济快速',
    icon: 'ThunderboltOutlined',
    title: 'standardEditing.title',
    price: 'standardEditing.price',
    type: 'standardEditing.type',
    summary: 'standardEditing.summary',
    recommendedFor: 'standardEditing.recommendedFor',
    features: [
      'standardEditing.features.feature1',
      'standardEditing.features.feature2',
      'standardEditing.features.feature3',
      'standardEditing.features.feature4',
      'standardEditing.features.feature5',
      'standardEditing.features.feature6',
      'standardEditing.features.feature7',
    ],
    deliveryTime: 'standardEditing.deliveryTime',
  },
  {
    id: 'premium',
    badge: '3年内无限润色',
    icon: 'CrownOutlined',
    title: 'premiumEditing.title',
    price: 'premiumEditing.price',
    type: 'premiumEditing.type',
    summary: 'premiumEditing.summary',
    recommendedFor: 'premiumEditing.recommendedFor',
    features: [
      'premiumEditing.features.feature1',
      'premiumEditing.features.feature2',
      'premiumEditing.features.feature3',
      'premiumEditing.features.feature4',
      'premiumEditing.features.feature5',
      'premiumEditing.features.feature6',
      'premiumEditing.features.feature7',
    ],
    deliveryTime: 'premiumEditing.deliveryTime',
  },
  {
    id: 'scientific',
    badge: '科学评审编辑',
    icon: 'ExperimentOutlined',
    title: 'scientificReview.title',
    price: 'scientificReview.price',
    type: 'scientificReview.type',
    summary: 'scientificReview.summary',
    recommendedFor: 'scientificReview.recommendedFor',
    features: [
      'scientificReview.features.feature1',
      'scientificReview.features.feature2',
      'scientificReview.features.feature3',
      'scientificReview.features.feature4',
      'scientificReview.features.feature5',
      'scientificReview.features.feature6',
      'scientificReview.features.feature7',
      'scientificReview.features.feature8',
      'scientificReview.features.feature9',
    ],
    deliveryTime: 'scientificReview.deliveryTime',
  },
  {
    id: 'advanced',
    badge: '论文科学编辑',
    icon: 'RocketOutlined',
    title: 'advancedEditing.title',
    price: 'advancedEditing.price',
    type: 'advancedEditing.type',
    summary: 'advancedEditing.summary',
    recommendedFor: 'advancedEditing.recommendedFor',
    features: [
      'advancedEditing.features.feature1',
      'advancedEditing.features.feature2',
      'advancedEditing.features.feature3',
      'advancedEditing.features.feature4',
      'advancedEditing.features.feature5',
      'advancedEditing.features.feature6',
      'advancedEditing.features.feature7',
    ],
    rushOption: 'advancedEditing.rushOption',
    deliveryTime: 'advancedEditing.deliveryTime',
  },
];
export const translationServiceData = {
  id: 'translation',
  title: 'translation.service.title',
  subtitle: 'translation.service.subtitle',
  price: 'translation.service.price',
  features: [
    'translation.service.features.feature1',
    'translation.service.features.feature2',
    'translation.service.features.feature3',
    'translation.service.features.feature4',
  ],
  image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAvPdc1huKlrb_manuscript-formatting.png?auto=format,compress&w=60',
  rushOption: 'translation.service.rushOption',
};

export const journalServiceData = {
  id: 'journal',
  title: 'journal.service.title',
  subtitle: 'journal.service.subtitle',
  price: 'journal.service.price',
  benefits: [
    'journal.service.benefits.benefit1',
    'journal.service.benefits.benefit2',
    'journal.service.benefits.benefit3',
    'journal.service.benefits.benefit4',
  ],
  image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAvPdc1huKlrb_manuscript-formatting.png?auto=format,compress&w=60',
};

export const formattingServicesData = [
  {
    id: 'formatting',
    title: 'formatting.services.formatting.title',
    description: 'formatting.services.formatting.description',
    price: 'formatting.services.formatting.price',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAvPdc1huKlrb_manuscript-formatting.png?auto=format,compress',
    link: '/services/formatting',
  },
  {
    id: 'figures',
    title: 'formatting.services.figures.title',
    description: 'formatting.services.figures.description',
    price: 'formatting.services.figures.price',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/66ce856e-1f94-4f32-a0e7-8c8126fb6aa3_figure-formatting-128.png?auto=compress,format',
    link: '/services/figures',
  },
  {
    id: 'video',
    title: 'formatting.services.video.title',
    description: 'formatting.services.video.description',
    price: 'formatting.services.video.price',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBF_fPdc1huKlrT_formatting-services-group.png?auto=format,compress',
    link: '/services/research-promotion',
  },
  {
    id: 'graphical',
    title: 'formatting.services.graphical.title',
    description: 'formatting.services.graphical.description',
    price: 'formatting.services.graphical.price',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/3407a1aa-58bf-4321-96bf-139c0f302973_custom-Illustration-128.png?auto=format,compress',
    link: '/services/research-promotion',
  },
];

export const paymentMethodsData = [
  {
    id: 'quick',
    title: 'payment.methods.quick.title',
    description: 'payment.methods.quick.description',
    icon: 'https://www.aje.cn/dist/img/quick-payment.png',
  },
  {
    id: 'bank',
    title: 'payment.methods.bank.title',
    description: 'payment.methods.bank.description',
    icon: 'https://www.aje.cn/dist/img/bank-transfer.png',
  },
  {
    id: 'prepaid',
    title: 'payment.methods.prepaid.title',
    description: 'payment.methods.prepaid.description',
    icon: 'https://www.aje.cn/dist/img/prepaid.png',
  },
];

export const faqData = [
  {
    id: '1',
    question: 'faq.questions.delivery.question',
    answer: 'faq.questions.delivery.answer',
  },
  {
    id: '2',
    question: 'faq.questions.groupDiscount.question',
    answer: 'faq.questions.groupDiscount.answer',
  },
  {
    id: '3',
    question: 'faq.questions.referral.question',
    answer: 'faq.questions.referral.answer',
  },
  {
    id: '4',
    question: 'faq.questions.rewards.question',
    answer: 'faq.questions.rewards.answer',
  },
];

// 横幅数据
export const bannerData = {
  text: 'banner.text',
  buttonText: 'banner.buttonText',
  buttonLink: '/promotions',
};

// 联系信息数据
export const contactData = {
  title: 'contact.title',
  description: 'contact.description',
  buttons: {
    primary: {
      text: 'contact.buttons.primary',
      link: 'https://china.aje.com/cn/researcher/submit/get-started',
    },
    secondary: {
      text: 'contact.buttons.secondary',
      link: '/services/',
    },
  },
};
