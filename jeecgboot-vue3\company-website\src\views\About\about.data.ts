import { FileTextOutlined, TeamOutlined, GlobalOutlined, TrophyOutlined } from '@ant-design/icons-vue'

// 润色流程步骤
export const processSteps = [
  'preparation',
  'matching',
  'editing',
  'quality',
  'submission'
]

// 合作伙伴
export const partners = [
  {
    key: 'nature',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/325fe374-c09d-43f6-bebe-f94506410e0d_nature.png?auto=compress,format&w=60'
  },
  {
    key: 'springer',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/1fe603a7-c964-4548-b1f6-a560d0b17de9_springer-logo.png?auto=compress,format&w=60'
  },
  {
    key: 'ieee',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/b30d61ac-7ae0-477a-8ef7-f259cf7dab46_ieee.png?auto=compress,format&w=60'
  },
  {
    key: 'mrs',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/d2b69b3f-8f02-4e59-90c4-290ef9ca7f87_mrs.png?auto=compress,format&w=60'
  },
  {
    key: 'cambridge',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/22796a38-d620-45b5-988a-bcc72990a514_cup.png?auto=compress,format&w=60'
  },
  {
    key: 'authoraid',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/f3979093-cf9f-4797-8343-ebfec57aa663_authoraid-logo.png?auto=compress,format&w=60'
  },
  {
    key: 'plos',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/1dedfe26-4183-4f9d-b0cb-d5e1646ff61f_plos.png?auto=compress,format&w=60'
  }
]

// 统计数据
export const statistics = [
  { key: 'papers', icon: FileTextOutlined },
  { key: 'fields', icon: GlobalOutlined },
  { key: 'years', icon: TrophyOutlined },
  { key: 'editors', icon: TeamOutlined }
]
