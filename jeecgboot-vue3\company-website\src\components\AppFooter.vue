<template>
  <footer class="app-footer">
    <!-- 主要内容区域 -->
    <div class="footer-content">
      <div class="footer-container">
        <!-- 社交媒体图标 -->
        <div class="social-media-section">
          <div class="qr-code">
            <!-- <img src="/images/qrcode.png" alt="微信二维码" /> -->
          </div>
          <div class="social-icons">
            <a href="https://space.bilibili.com/2099009306" target="_blank" class="social-link">
              <!-- <img src="/images/social/bilibili.png" alt="哔哩哔哩" /> -->
            </a>
            <a href="https://www.zhihu.com/org/aje.cn" target="_blank" class="social-link">
              <!-- <img src="/images/social/zhihu.png" alt="知乎" /> -->
            </a>
            <a href="https://weibo.com/AJEChina" target="_blank" class="social-link">
              <!-- <img src="/images/social/weibo.png" alt="微博" /> -->
            </a>
            <a href="https://www.xiaohongshu.com/user/profile/6385b934000000001f017f05" target="_blank"
              class="social-link">
              <!-- <img src="/images/social/xiaohongshu.png" alt="小红书" /> -->
            </a>
            <a href="https://www.douyin.com/user/MS4wLjABAAAAMAFGiIHIjUcuBO52a52fFFZJHJtwFXznzIaY7HaeFVeb1b-CNOhmBY7f8J0wP6YQ"
              target="_blank" class="social-link">
              <!-- <img src="/images/social/douyin.png" alt="抖音" /> -->
            </a>
          </div>
        </div>

        <!-- 链接分组 -->
        <div class="footer-links">
          <!-- 专业服务 -->
          <div class="link-group">
            <h4>{{ t('components.footer.professionalServices.title') }}</h4>
            <ul>
              <li><a href="/services/editing">{{ t('components.footer.professionalServices.editing') }}</a></li>
              <li><a href="/services/vip-editing">{{ t('components.footer.professionalServices.scientificReview') }}</a>
              </li>
              <li><a href="/services/scientific-editing">{{ t('components.footer.professionalServices.scientificEditing')
              }}</a></li>
              <li><a href="/services/translation">{{ t('components.footer.professionalServices.translation') }}</a></li>
              <li><a href="/rubriq">{{ t('components.footer.professionalServices.rubriq') }}</a></li>
            </ul>
          </div>

          <!-- 增值服务 -->
          <div class="link-group">
            <h4>{{ t('components.footer.valueAddedServices.title') }}</h4>
            <ul>
              <li><a href="/services/presubmission-review">{{
                t('components.footer.valueAddedServices.presubmissionReview') }}</a>
              </li>
              <li><a href="/services/journal-recommendation">{{
                t('components.footer.valueAddedServices.journalRecommendation')
              }}</a></li>
              <li><a href="/services/formatting">{{ t('components.footer.valueAddedServices.formatting') }}</a></li>
              <li><a href="/services/figures">{{ t('components.footer.valueAddedServices.figures') }}</a></li>
              <li><a href="/services/research-promotion">{{ t('components.footer.valueAddedServices.researchPromotion')
              }}</a></li>
            </ul>
          </div>

          <!-- 资源中心 -->
          <div class="link-group">
            <h4>{{ t('components.footer.resourceCenter.title') }}</h4>
            <ul>
              <li><a href="/arc">{{ t('components.footer.resourceCenter.authorResources') }}</a></li>
              <li><a href="/pricing">{{ t('components.footer.resourceCenter.quickQuote') }}</a></li>
              <li><a href="/about/ethics">{{ t('components.footer.resourceCenter.academicEthics') }}</a></li>
              <li><a href="/quality-standards">{{ t('components.footer.resourceCenter.qualityGuarantee') }}</a></li>
              <li><a href="/aje-plus">{{ t('components.footer.resourceCenter.memberCenter') }}</a></li>
            </ul>
          </div>

          <!-- 关于我们 -->
          <div class="link-group">
            <h4>{{ t('components.footer.aboutUs.title') }}</h4>
            <ul>
              <li><a href="/contact-us">{{ t('components.footer.aboutUs.contactUs') }}</a></li>
              <li><a href="/about">{{ t('components.footer.aboutUs.aboutAJE') }}</a></li>
              <li><a href="/about/our-team">{{ t('components.footer.aboutUs.ajeTeam') }}</a></li>
              <li><a href="/testimonials">{{ t('components.footer.aboutUs.customerReviews') }}</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="footer-bottom">
      <div class="footer-container">
        <div class="copyright-section">
          <div class="copyright-text">
            <p>© 2025 American Journal Experts LLC {{ t('components.footer.copyright.allRightsReserved') }}</p>
            <div class="legal-links">
              <a href="/security-and-privacy">{{ t('components.footer.copyright.privacyPolicy') }}</a>
              <a href="/terms-of-service">{{ t('components.footer.copyright.termsOfService') }}</a>
            </div>
          </div>
          <div class="filing-info">
            <a href="https://beian.miit.gov.cn/" target="_blank">{{ t('components.footer.filing.icp') }}</a>
            <a href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802031576" target="_blank">
              {{ t('components.footer.filing.publicSecurity') }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<style scoped>
.app-footer {
  background: #1a1a1a;
  color: #ffffff;
}

.footer-content {
  padding: 60px 0 40px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 社交媒体区域 */
.social-media-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50px;
  gap: 30px;
}

.qr-code img {
  width: 120px;
  height: 120px;
  border-radius: 8px;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-link {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.social-link:hover {
  transform: scale(1.1);
}

.social-link img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 链接分组 */
.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  margin-bottom: 40px;
}

.link-group h4 {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 20px;
  border-bottom: 2px solid #333;
  padding-bottom: 10px;
}

.link-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.link-group li {
  margin-bottom: 12px;
}

.link-group a {
  color: #cccccc;
  text-decoration: none;
  font-size: 14px;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.link-group a:hover {
  color: #1890ff;
}

/* 底部版权区域 */
.footer-bottom {
  border-top: 1px solid #333;
  padding: 20px 0;
  background: #111;
}

.copyright-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.copyright-text {
  display: flex;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
}

.copyright-text p {
  margin: 0;
  color: #999;
  font-size: 14px;
}

.legal-links {
  display: flex;
  gap: 20px;
}

.legal-links a {
  color: #999;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.legal-links a:hover {
  color: #1890ff;
}

.filing-info {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.filing-info a {
  color: #999;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.filing-info a:hover {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .social-media-section {
    flex-direction: column;
    gap: 20px;
  }

  .copyright-section {
    flex-direction: column;
    text-align: center;
  }

  .copyright-text {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .footer-links {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .social-icons {
    justify-content: center;
  }

  .legal-links,
  .filing-info {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
