# HTML Lang 属性标准语言代码参考

## 基础语言代码 (ISO 639-1)

| 语言 | 代码 | 英文名称 | 示例 |
|------|------|----------|------|
| 中文 | zh | Chinese | `<html lang="zh">` |
| 英文 | en | English | `<html lang="en">` |
| 日文 | ja | Japanese | `<html lang="ja">` |
| 韩文 | ko | Korean | `<html lang="ko">` |
| 法文 | fr | French | `<html lang="fr">` |
| 德文 | de | German | `<html lang="de">` |
| 西班牙文 | es | Spanish | `<html lang="es">` |
| 意大利文 | it | Italian | `<html lang="it">` |
| 葡萄牙文 | pt | Portuguese | `<html lang="pt">` |
| 俄文 | ru | Russian | `<html lang="ru">` |
| 阿拉伯文 | ar | Arabic | `<html lang="ar">` |
| 印地文 | hi | Hindi | `<html lang="hi">` |
| 泰文 | th | Thai | `<html lang="th">` |
| 越南文 | vi | Vietnamese | `<html lang="vi">` |

## 地区变体代码 (BCP 47)

### 中文变体
- `zh-CN` - 中文（中国大陆）简体中文
- `zh-TW` - 中文（台湾）繁体中文
- `zh-HK` - 中文（香港）繁体中文
- `zh-SG` - 中文（新加坡）简体中文
- `zh-MO` - 中文（澳门）繁体中文

### 英文变体
- `en-US` - 英文（美国）
- `en-GB` - 英文（英国）
- `en-CA` - 英文（加拿大）
- `en-AU` - 英文（澳大利亚）
- `en-IN` - 英文（印度）

### 西班牙文变体
- `es-ES` - 西班牙文（西班牙）
- `es-MX` - 西班牙文（墨西哥）
- `es-AR` - 西班牙文（阿根廷）
- `es-CO` - 西班牙文（哥伦比亚）

### 法文变体
- `fr-FR` - 法文（法国）
- `fr-CA` - 法文（加拿大）
- `fr-BE` - 法文（比利时）
- `fr-CH` - 法文（瑞士）

### 德文变体
- `de-DE` - 德文（德国）
- `de-AT` - 德文（奥地利）
- `de-CH` - 德文（瑞士）

### 葡萄牙文变体
- `pt-BR` - 葡萄牙文（巴西）
- `pt-PT` - 葡萄牙文（葡萄牙）

## BCP 47 标准格式

```
language[-script][-region][-variant][-extension][-privateuse]
```

### 示例：
- `zh-Hans-CN` - 中文-简体-中国
- `zh-Hant-TW` - 中文-繁体-台湾
- `en-US-x-custom` - 英文-美国-私有扩展

## 常用组合建议

### 企业官网推荐
1. `zh-CN` - 简体中文（主要）
2. `en-US` - 美式英语（国际）
3. `ja-JP` - 日文（可选）
4. `ko-KR` - 韩文（可选）

### 电商平台推荐
1. `zh-CN` - 简体中文
2. `zh-TW` - 繁体中文
3. `en-US` - 美式英语
4. `ja-JP` - 日文
5. `ko-KR` - 韩文
6. `es-ES` - 西班牙文
7. `fr-FR` - 法文
8. `de-DE` - 德文
