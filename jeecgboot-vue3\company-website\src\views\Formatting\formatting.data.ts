import { FileTextOutlined, CheckCircleOutlined, EditOutlined, SafetyOutlined } from '@ant-design/icons-vue'

// 权威认证logos
export const authorityLogos = [
  {
    key: 'nature',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/68ddb796-b40c-424e-9bf3-0f3d553492ae_nature-logo.png?auto=compress,format&w=60'
  },
  {
    key: 'springer',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/1fe603a7-c964-4548-b1f6-a560d0b17de9_springer-logo.png?auto=compress,format&w=60'
  },
  {
    key: 'cambridge',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/e6bfe80e-a362-49b8-a232-b70beb07f4e4_cambridge-logo.png?auto=compress,format&w=60'
  },
  {
    key: 'agu',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2dPIqRLdaBsbs_AGU-2.png?auto=format,compress&w=60'
  },
  {
    key: 'ieee',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/b30d61ac-7ae0-477a-8ef7-f259cf7dab46_ieee.png?auto=compress,format&w=60'
  },
  {
    key: 'cope',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/767dd4b2-ccfd-43a1-bd2c-19a743198264_ethics-cope.png?auto=compress,format&w=60'
  },
  {
    key: 'plos',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/1dedfe26-4183-4f9d-b0cb-d5e1646ff61f_plos.png?auto=compress,format&w=60'
  },
  {
    key: 'royal',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2gPIqRLdaBsb2_%E8%8B%B1%E5%9B%BD%E7%9A%87%E5%AE%B6%E5%AD%A6%E4%BC%9A-2.png?auto=format,compress&w=60'
  },
  {
    key: 'seismological',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB4EvIqRLdaBsck_%E5%9C%B0%E9%9C%87%E5%8D%8F%E4%BC%9A-4.png?auto=format,compress&w=60'
  },
  {
    key: 'aacr',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2f_IqRLdaBsb1_%E7%BE%8E%E5%9B%BD%E7%99%8C%E7%97%87%E7%A0%94%E7%A9%B6%E5%8D%8F%E4%BC%9A-2.png?auto=format,compress&w=60'
  },
  {
    key: 'asme',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2evIqRLdaBsbx_ASME-2.png?auto=format,compress&w=60'
  },
  {
    key: 'asm',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2efIqRLdaBsbw_ASM-2.png?auto=format,compress&w=60'
  },
  {
    key: 'aps',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2ePIqRLdaBsbu_APS-2.png?auto=format,compress&w=60'
  },
  {
    key: 'ams',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2d_IqRLdaBsbt_AMS-2.png?auto=format,compress&w=60'
  },
  {
    key: 'acs',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2cvIqRLdaBsbr_ACS-2.png?auto=format,compress&w=60'
  },
  {
    key: 'aace',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2cfIqRLdaBsbq_AACE-2.png?auto=format,compress&w=60'
  },
  {
    key: 'jpgu',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2fvIqRLdaBsb0_%E6%97%A5%E6%9C%AC%E5%9C%B0%E7%90%83%E7%A7%91%E5%AD%A6-2.png?auto=format,compress&w=60'
  },
  {
    key: 'pnas',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2fPIqRLdaBsby_PNAS-2.png?auto=format,compress&w=60'
  }
]

// 服务特色
export const serviceFeatures = [
  { key: 'compliance', icon: FileTextOutlined },
  { key: 'references', icon: EditOutlined },
  { key: 'guarantee', icon: SafetyOutlined }
]

// 样本数据
export const sampleData = [
  {
    key: 'formatting',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/d6b67f37-7501-40c4-b3ec-2348c79d7ad2_formatting-headings-example.png?auto=compress,format&w=60',
    downloadUrl: 'https://aje-cms-production.cdn.prismic.io/aje-cms-production/4e746687-7749-4486-a0d2-f0ce2b96b61f_Sample-Paper-PRE-bundle.docx'
  },
  {
    key: 'guidelines',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/07025b3f-e63b-4043-914d-bd9a6c7fdb76_formatting-guidelines-sample.png?auto=compress,format&w=60',
    downloadUrl: 'https://aje-cms-production.cdn.prismic.io/aje-cms-production/aba5dfbb-2f65-47b9-a914-f76691d81363_Sleep-and-Breathing+%281%29.pdf'
  }
]

// 质量保证特点
export const qualityFeatures = [
  { key: 'freeReformatting', icon: EditOutlined },
  { key: 'satisfaction', icon: CheckCircleOutlined }
]

// 客户评价
export const testimonials = [
  { key: 'terry' },
  { key: 'zhao' }
]

// 相关服务
export const relatedServices = [
  {
    key: 'journalRecommendation',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAfPdc1huKlra_journal-recommendation.png?auto=format,compress&w=60',
    link: 'https://www.aje.cn/services/journal-recommendation'
  },
  {
    key: 'figures',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/66ce856e-1f94-4f32-a0e7-8c8126fb6aa3_figure-formatting-128.png?auto=compress,format&w=60',
    link: 'https://www.aje.cn/services/figures'
  },
  {
    key: 'presubmission',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/fc927461-7932-461b-9983-cf1441cc454e_presubmission+review+illustration.png?auto=compress,format&w=60',
    link: 'https://www.aje.cn/services/presubmission-review'
  }
]

// FAQ数据
export const faqData = [
  { key: 'difference' },
  { key: 'tables' },
  { key: 'reformat' },
  { key: 'fileFormat' },
  { key: 'contact' }
]
