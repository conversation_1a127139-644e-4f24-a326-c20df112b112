import type { UserConfig, ConfigEnv } from 'vite';
import { loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const env = loadEnv(mode, process.cwd(), '');
  const isProd = command === 'build';

  return {
    plugins: [vue()],
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: /@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /#\//,
          replacement: pathResolve('types') + '/',
        },
      ],
    },
    server: {
      // Listening on all local IPs
      host: true,
      port: Number(env.VITE_PORT) || 3200,
      open: true,
    },
    css: {
      postcss: './postcss.config.js',
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          // 全局Less变量
          additionalData: `
            @primary-color: #1890ff;
            @success-color: #52c41a;
            @warning-color: #faad14;
            @error-color: #ff4d4f;
            @font-size-base: 14px;
            @border-radius-base: 6px;
          `,
          // 确保Less与PostCSS兼容
          modifyVars: {
            'primary-color': '#1890ff',
            'border-radius-base': '6px',
          },
        },
      },
      // 开发环境启用source map
      devSourcemap: !isProd,
    },
    build: {
      outDir: env.VITE_OUTPUT_DIR || 'dist',
      assetsDir: 'assets',
      sourcemap: !isProd,
      // CSS代码分割
      cssCodeSplit: true,
      rollupOptions: {
        output: {
          // CSS文件命名
          assetFileNames: (assetInfo) => {
            if (assetInfo.names?.[0]?.endsWith('.css')) {
              return 'css/[name]-[hash][extname]';
            }
            return 'assets/[name]-[hash][extname]';
          },
        },
      },
    },
    esbuild: {
      // 清除全局的console.log和debug
      drop: isProd ? ['console', 'debugger'] : [],
    },
    define: {
      // setting vue-i18-next
      // Suppress warning
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify({
        title: env.VITE_APP_TITLE,
        buildTime: new Date().toISOString(),
      }),
    },
    optimizeDeps: {
      esbuildOptions: {
        target: 'es2020',
      },
    },
  };
};
