<template>
  <ConfigProvider :theme="appTheme" :locale="getAntdLocale">
    <div id="app">
      <router-view />
    </div>
  </ConfigProvider>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { theme, ConfigProvider } from 'ant-design-vue';
import { useLocale } from '@/locales/useLocale';

// 解决日期时间国际化问题
import 'dayjs/locale/zh-cn';

// 支持多语言
const { getAntdLocale, getLocale } = useLocale();

// 应用主题配置
const appTheme = ref({
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: '#1890ff',
    wireframe: true,
    fontSize: 14,
    colorTextBase: '#333',
    colorSuccess: '#55D187',
    colorInfo: '#1890ff',
    borderRadius: 4,
    sizeStep: 4,
    sizeUnit: 4,
    colorWarning: '#EFBD47',
    colorError: '#ED6F6F',
    fontFamily: '-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol',
  },
});

// 监听语言变化，更新 HTML 属性
watchEffect(() => {
  const locale = getLocale.value;

  // 更新 HTML lang 属性
  document.documentElement.lang = locale;

  // 更新页面标题
  const titleMap = {
    'zh-CN': '公司官网',
    'en': 'Company Website',
  };
  document.title = titleMap[locale] || titleMap['zh-CN'];
});
</script>

<style lang="less">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

#app {
  min-height: 100vh;
}
</style>