// noinspection JSUnusedGlobalSymbols

import { unref } from 'vue';
import { useWebSocket, type UseWebSocketReturn } from '@vueuse/core';
import { getEnvConfig } from '@/utils/env';

let result: UseWebSocketReturn<any>;
const listeners = new Map();

/**
 * 开启 WebSocket 链接，全局只需执行一次
 * @param url
 */
export function connectWebSocket(url: string) {
  // 获取认证token (官网项目可能不需要认证，或使用其他认证方式)
  const token = getAuthToken();

  result = useWebSocket(url, {
    // 自动重连 (遇到错误最多重复连接10次)
    autoReconnect: {
      retries: 10,
      delay: 5000,
    },
    // 心跳检测
    heartbeat: {
      message: 'ping',
      interval: 55000,
    },
    protocols: token ? [token] : [],
    onConnected: function (ws) {
      console.log('[WebSocket] 连接成功', ws);
    },
    onDisconnected: function (ws, event) {
      console.log('[WebSocket] 连接断开：', ws, event);
    },
    onError: function (ws, event) {
      console.log('[WebSocket] 连接发生错误: ', ws, event);
    },
    onMessage: function (_ws, e) {
      console.debug('[WebSocket] -----接收消息-------', e.data);
      try {
        // 忽略心跳消息
        if (e.data === 'ping') {
          return;
        }

        const data = JSON.parse(e.data);
        for (const callback of listeners.keys()) {
          try {
            callback(data);
          } catch (err) {
            console.error(err);
          }
        }
      } catch (err) {
        console.error('[WebSocket] data解析失败：', err);
      }
    },
  });

  return result;
}

/**
 * 获取认证token (官网项目版本)
 */
function getAuthToken(): string | null {
  // 官网项目可能使用不同的认证方式
  // 可以从 localStorage、sessionStorage 或其他地方获取
  try {
    return localStorage.getItem('website-auth-token') || sessionStorage.getItem('website-auth-token') || null;
  } catch (error) {
    console.warn('[WebSocket] 获取认证token失败:', error);
    return null;
  }
}

/**
 * 设置认证token
 */
export function setAuthToken(token: string) {
  try {
    localStorage.setItem('website-auth-token', token);
  } catch (error) {
    console.warn('[WebSocket] 设置认证token失败:', error);
  }
}

/**
 * 清除认证token
 */
export function clearAuthToken() {
  try {
    localStorage.removeItem('website-auth-token');
    sessionStorage.removeItem('website-auth-token');
  } catch (error) {
    console.warn('[WebSocket] 清除认证token失败:', error);
  }
}

/**
 * 添加 WebSocket 消息监听
 * @param callback
 */
export function onWebSocket(callback: (data: object) => any) {
  if (!listeners.has(callback)) {
    if (typeof callback === 'function') {
      listeners.set(callback, null);
    } else {
      console.debug('[WebSocket] 添加 WebSocket 消息监听失败：传入的参数不是一个方法');
    }
  }
}

/**
 * 解除 WebSocket 消息监听
 * @param callback
 */
export function offWebSocket(callback: (data: object) => any) {
  listeners.delete(callback);
}

/**
 * 发送 WebSocket 消息
 * @param data
 */
export function sendWebSocketMessage(data: any) {
  if (result && result.send) {
    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      result.send(message);
      console.debug('[WebSocket] 发送消息:', message);
    } catch (error) {
      console.error('[WebSocket] 发送消息失败:', error);
    }
  } else {
    console.warn('[WebSocket] WebSocket 未连接，无法发送消息');
  }
}

/**
 * 获取 WebSocket 连接状态
 */
export function getWebSocketStatus() {
  return result?.status?.value || 'CLOSED';
}

/**
 * 手动关闭 WebSocket 连接
 */
export function closeWebSocket() {
  if (result && result.close) {
    result.close();
    console.log('[WebSocket] 手动关闭连接');
  }
}

/**
 * 手动重新连接 WebSocket
 */
export function reconnectWebSocket() {
  if (result && result.open) {
    result.open();
    console.log('[WebSocket] 手动重新连接');
  }
}

/**
 * 获取 WebSocket 实例
 */
export function useMyWebSocket() {
  return result;
}

/**
 * 创建官网项目的 WebSocket 连接
 * @param path WebSocket 路径 (可选)
 */
export function createWebSocketConnection(path: string = '/websocket') {
  const { apiBaseUrl } = getEnvConfig();

  // 构建 WebSocket URL
  let wsUrl = '';
  if (apiBaseUrl) {
    // 将 HTTP/HTTPS 转换为 WS/WSS
    wsUrl = apiBaseUrl.replace('https://', 'wss://').replace('http://', 'ws://') + path;
  } else {
    // 默认使用当前域名
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    wsUrl = `${protocol}//${host}${path}`;
  }

  console.log('[WebSocket] 连接地址:', wsUrl);
  return connectWebSocket(wsUrl);
}

/**
 * 官网项目专用的 WebSocket 消息类型
 */
export enum WebSocketMessageType {
  // 系统通知
  SYSTEM_NOTIFICATION = 'system_notification',
  // 用户消息
  USER_MESSAGE = 'user_message',
  // 在线客服
  CUSTOMER_SERVICE = 'customer_service',
  // 实时数据更新
  DATA_UPDATE = 'data_update',
  // 心跳检测
  HEARTBEAT = 'heartbeat',
}

/**
 * WebSocket 消息接口
 */
export interface WebSocketMessage {
  type: WebSocketMessageType;
  data: any;
  timestamp?: number;
  id?: string;
}

/**
 * 发送格式化的 WebSocket 消息
 */
export function sendFormattedMessage(type: WebSocketMessageType, data: any) {
  const message: WebSocketMessage = {
    type,
    data,
    timestamp: Date.now(),
    id: generateMessageId(),
  };

  sendWebSocketMessage(message);
}

/**
 * 生成消息ID
 */
function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * WebSocket 连接管理器
 */
export class WebSocketManager {
  private static instance: WebSocketManager;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * 初始化连接
   */
  init(path?: string) {
    if (!this.isConnected) {
      createWebSocketConnection(path);
      this.isConnected = true;
      this.reconnectAttempts = 0;
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    closeWebSocket();
    this.isConnected = false;
  }

  /**
   * 重连
   */
  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`[WebSocket] 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      setTimeout(() => {
        reconnectWebSocket();
      }, 1000 * this.reconnectAttempts);
    } else {
      console.error('[WebSocket] 重连次数已达上限，停止重连');
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      status: getWebSocketStatus(),
      reconnectAttempts: this.reconnectAttempts,
    };
  }
}
