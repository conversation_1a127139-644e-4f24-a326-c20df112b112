/**
 * WebSocket 服务管理
 * 提供官网项目的 WebSocket 功能封装
 */

import {
  WebSocketManager,
  WebSocketMessageType,
  onWebSocket,
  offWebSocket,
  sendFormattedMessage,
  setAuthToken,
  clearAuthToken,
  type WebSocketMessage,
} from '@/hooks/web/useWebSocket';

/**
 * 官网 WebSocket 服务类
 */
export class WebsiteWebSocketService {
  private static instance: WebsiteWebSocketService;
  private manager: WebSocketManager;
  private messageHandlers: Map<WebSocketMessageType, Array<(data: any) => void>> = new Map();
  private isInitialized = false;

  constructor() {
    this.manager = WebSocketManager.getInstance();
  }

  static getInstance(): WebsiteWebSocketService {
    if (!WebsiteWebSocketService.instance) {
      WebsiteWebSocketService.instance = new WebsiteWebSocketService();
    }
    return WebsiteWebSocketService.instance;
  }

  /**
   * 初始化 WebSocket 服务
   */
  async init(options?: { path?: string; token?: string; autoConnect?: boolean }) {
    if (this.isInitialized) {
      console.warn('[WebSocketService] 服务已初始化');
      return;
    }

    const { path = '/websocket', token, autoConnect = true } = options || {};

    // 设置认证token
    if (token) {
      setAuthToken(token);
    }

    // 添加全局消息监听
    onWebSocket(this.handleGlobalMessage.bind(this));

    // 自动连接
    if (autoConnect) {
      this.manager.init(path);
    }

    this.isInitialized = true;
    console.log('[WebSocketService] 服务初始化完成');
  }

  /**
   * 连接 WebSocket
   */
  connect(path?: string) {
    this.manager.init(path);
  }

  /**
   * 断开 WebSocket
   */
  disconnect() {
    this.manager.disconnect();
    clearAuthToken();
  }

  /**
   * 重连 WebSocket
   */
  reconnect() {
    this.manager.reconnect();
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return this.manager.getStatus();
  }

  /**
   * 发送消息
   */
  sendMessage(type: WebSocketMessageType, data: any) {
    sendFormattedMessage(type, data);
  }

  /**
   * 添加消息处理器
   */
  addMessageHandler(type: WebSocketMessageType, handler: (data: any) => void) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);
  }

  /**
   * 移除消息处理器
   */
  removeMessageHandler(type: WebSocketMessageType, handler: (data: any) => void) {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 全局消息处理
   */
  private handleGlobalMessage(data: any) {
    try {
      // 确保数据是 WebSocketMessage 格式
      const message = data as WebSocketMessage;
      if (message && message.type) {
        const handlers = this.messageHandlers.get(message.type);
        if (handlers) {
          handlers.forEach((handler) => {
            try {
              handler(message.data);
            } catch (error) {
              console.error('[WebSocketService] 消息处理器执行失败:', error);
            }
          });
        }
      }
    } catch (error) {
      console.error('[WebSocketService] 全局消息处理失败:', error);
    }
  }

  /**
   * 销毁服务
   */
  destroy() {
    this.disconnect();
    this.messageHandlers.clear();
    offWebSocket(this.handleGlobalMessage.bind(this));
    this.isInitialized = false;
  }
}

/**
 * 官网专用的 WebSocket 消息处理器
 */
export class WebsiteMessageHandlers {
  private service: WebsiteWebSocketService;

  constructor() {
    this.service = WebsiteWebSocketService.getInstance();
  }

  /**
   * 处理系统通知
   */
  handleSystemNotification(callback: (notification: SystemNotification) => void) {
    this.service.addMessageHandler(WebSocketMessageType.SYSTEM_NOTIFICATION, callback);
  }

  /**
   * 处理用户消息
   */
  handleUserMessage(callback: (message: UserMessage) => void) {
    this.service.addMessageHandler(WebSocketMessageType.USER_MESSAGE, callback);
  }

  /**
   * 处理客服消息
   */
  handleCustomerService(callback: (message: CustomerServiceMessage) => void) {
    this.service.addMessageHandler(WebSocketMessageType.CUSTOMER_SERVICE, callback);
  }

  /**
   * 处理数据更新
   */
  handleDataUpdate(callback: (update: DataUpdate) => void) {
    this.service.addMessageHandler(WebSocketMessageType.DATA_UPDATE, callback);
  }

  /**
   * 发送用户消息
   */
  sendUserMessage(content: string, targetUserId?: string) {
    this.service.sendMessage(WebSocketMessageType.USER_MESSAGE, {
      content,
      targetUserId,
      timestamp: Date.now(),
    });
  }

  /**
   * 发送客服消息
   */
  sendCustomerServiceMessage(content: string, sessionId: string) {
    this.service.sendMessage(WebSocketMessageType.CUSTOMER_SERVICE, {
      content,
      sessionId,
      timestamp: Date.now(),
    });
  }
}

/**
 * 系统通知接口
 */
export interface SystemNotification {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: number;
  read?: boolean;
}

/**
 * 用户消息接口
 */
export interface UserMessage {
  id: string;
  fromUserId: string;
  toUserId?: string;
  content: string;
  timestamp: number;
  messageType?: 'text' | 'image' | 'file';
}

/**
 * 客服消息接口
 */
export interface CustomerServiceMessage {
  id: string;
  sessionId: string;
  fromType: 'user' | 'agent';
  content: string;
  timestamp: number;
  agentId?: string;
}

/**
 * 数据更新接口
 */
export interface DataUpdate {
  type: string;
  data: any;
  timestamp: number;
  version?: string;
}

/**
 * 创建 WebSocket 服务实例
 */
export function createWebSocketService() {
  return WebsiteWebSocketService.getInstance();
}

/**
 * 创建消息处理器实例
 */
export function createMessageHandlers() {
  return new WebsiteMessageHandlers();
}

/**
 * 官网 WebSocket 工具函数
 */
export const websocketUtils = {
  /**
   * 格式化消息时间
   */
  formatMessageTime(timestamp: number): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) {
      // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) {
      // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 生成会话ID
   */
  generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  },

  /**
   * 验证消息格式
   */
  validateMessage(message: any): boolean {
    return message && typeof message === 'object' && message.type && message.data !== undefined;
  },

  /**
   * 获取消息类型显示名称
   */
  getMessageTypeDisplayName(type: WebSocketMessageType): string {
    const typeNames = {
      [WebSocketMessageType.SYSTEM_NOTIFICATION]: '系统通知',
      [WebSocketMessageType.USER_MESSAGE]: '用户消息',
      [WebSocketMessageType.CUSTOMER_SERVICE]: '客服消息',
      [WebSocketMessageType.DATA_UPDATE]: '数据更新',
      [WebSocketMessageType.HEARTBEAT]: '心跳检测',
    };
    return typeNames[type] || '未知类型';
  },
};

// 导出默认实例
export default WebsiteWebSocketService.getInstance();
