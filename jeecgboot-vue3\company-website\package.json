{"name": "company-website", "version": "1.0.0", "scripts": {"pinstall": "pnpm install", "dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueuse/core": "^10.11.1", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "pinia": "2.1.7", "vue": "^3.5.13", "vue-i18n": "^9.8.0", "vue-router": "^4.5.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^20.17.12", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "cssnano": "^6.0.3", "less": "^4.2.1", "postcss": "^8.4.49", "postcss-import": "^16.0.0", "postcss-nested": "^6.0.1", "typescript": "^4.9.5", "unocss": "^0.58.9", "vite": "^5.4.0", "vue-tsc": "^2.0.0"}}