<template>
  <div class="testimonials-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('testimonials.hero.title') }}</h1>
          <p class="hero-description">{{ t('testimonials.hero.description') }}</p>
          <div class="hero-actions">
            <a-button type="primary" size="large" @click="handleLearnTeam">
              {{ t('testimonials.hero.learnTeam') }}
            </a-button>
            <a-button size="large" @click="handleAllFields">
              {{ t('testimonials.hero.allFields') }}
            </a-button>
          </div>
        </div>
        <div class="hero-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/Z-z-KHdAxsiBwNkg_testimonials-800x800.png?auto=format,compress&w=60"
            :alt="t('testimonials.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 真实客户评价 -->
    <section class="testimonials-content-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('testimonials.content.title') }}</h2>
          <p>{{ t('testimonials.content.description') }}</p>
        </div>

        <!-- 筛选器 -->
        <div class="filters-section">
          <div class="filter-group">
            <label>{{ t('testimonials.filters.field') }}:</label>
            <a-select v-model:value="selectedField" style="width: 200px" @change="handleFilterChange">
              <a-select-option value="all">{{ t('testimonials.filters.allFields') }}</a-select-option>
              <a-select-option v-for="field in researchFields" :key="field" :value="field">
                {{ t(`testimonials.fields.${field}`) }}
              </a-select-option>
            </a-select>
          </div>
          <div class="filter-group">
            <label>{{ t('testimonials.filters.service') }}:</label>
            <a-select v-model:value="selectedService" style="width: 200px" @change="handleFilterChange">
              <a-select-option value="all">{{ t('testimonials.filters.allServices') }}</a-select-option>
              <a-select-option v-for="service in serviceTypes" :key="service" :value="service">
                {{ t(`testimonials.services.${service}`) }}
              </a-select-option>
            </a-select>
          </div>
        </div>

        <!-- 评价列表 -->
        <div class="testimonials-grid">
          <div class="testimonial-card" v-for="testimonial in paginatedTestimonials" :key="testimonial.key">
            <div class="testimonial-content">
              <div class="testimonial-author">
                <div class="author-avatar">
                  <img v-if="testimonial.avatar" :src="testimonial.avatar" :alt="testimonial.name" />
                  <div v-else class="avatar-placeholder">{{ testimonial.name.charAt(0).toUpperCase() }}</div>
                </div>
                <div class="author-info">
                  <p class="author-name">{{ testimonial.name }}</p>
                  <p class="author-title">{{ testimonial.title }}</p>
                  <p class="author-org" v-if="testimonial.organization">{{ testimonial.organization }}</p>
                </div>
              </div>
              <p class="testimonial-text">{{ t(`testimonials.testimonials.${testimonial.key}.text`) }}</p>
              <div class="testimonial-meta">
                <span class="service-tag" v-if="testimonial.service">
                  {{ t(`testimonials.services.${testimonial.service}`) }}
                </span>
                <span class="field-tag" v-if="testimonial.field">
                  {{ t(`testimonials.fields.${testimonial.field}`) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-section">
          <a-pagination v-model:current="currentPage" :total="filteredTestimonials.length" :page-size="pageSize"
            :show-size-changer="false" :show-quick-jumper="true"
            :show-total="(total, range) => `${range[0]}-${range[1]} / ${total} ${t('testimonials.pagination.items')}`"
            @change="handlePageChange" />
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="cta-content">
        <h2>{{ t('testimonials.cta.title') }}</h2>
        <p>{{ t('testimonials.cta.description') }}</p>
        <div class="cta-actions">
          <a-button type="primary" size="large" @click="handleOrderNow">
            {{ t('testimonials.cta.orderNow') }}
          </a-button>
          <a-button size="large" @click="handleQuickQuote">
            {{ t('testimonials.cta.quickQuote') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  testimonialsData,
  researchFields,
  serviceTypes
} from './testimonials.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 响应式数据
const selectedField = ref('all')
const selectedService = ref('all')
const currentPage = ref(1)
const pageSize = 12

// 计算属性
const filteredTestimonials = computed(() => {
  return testimonialsData.filter(testimonial => {
    const fieldMatch = selectedField.value === 'all' || testimonial.field === selectedField.value
    const serviceMatch = selectedService.value === 'all' || testimonial.service === selectedService.value
    return fieldMatch && serviceMatch
  })
})

const paginatedTestimonials = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredTestimonials.value.slice(start, end)
})

// 方法
const handleFilterChange = () => {
  currentPage.value = 1
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

const handleLearnTeam = () => {
  router.push('/about/our-team')
}

const handleAllFields = () => {
  router.push('/about/areas-of-study')
}

const handleOrderNow = () => {
  window.open('https://china.aje.com/cn/researcher/submit/get-started', '_blank')
}

const handleQuickQuote = () => {
  router.push('/pricing')
}

// 生命周期
onMounted(() => {
  console.log('Testimonials 页面已加载')
})
</script>

<style scoped>
.testimonials-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.hero-image {
  flex-shrink: 0;
}

.hero-image img {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: contain;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 评价内容区域 */
.testimonials-content-section {
  padding: 80px 0;
  background: white;
}

/* 区域标题 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 1000px;
  margin: 0 auto;
}

/* 筛选器区域 */
.filters-section {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-group label {
  font-size: 16px;
  font-weight: 500;
  color: #1a202c;
  white-space: nowrap;
}

/* 评价网格 */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  margin-bottom: 60px;
}

/* 评价卡片 */
.testimonial-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: fit-content;
}

.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-author {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.author-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background: #e2e8f0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.author-title {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 2px;
}

.author-org {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.4;
}

.testimonial-text {
  font-size: 15px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.testimonial-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.service-tag,
.field-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.service-tag {
  background: #e6f3ff;
  color: #0066cc;
}

.field-tag {
  background: #f0f9ff;
  color: #0284c7;
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: center;
}

/* CTA区域 */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.cta-content h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
}

.cta-content p {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .hero-image img {
    width: 150px;
    height: 150px;
  }

  .filters-section {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .testimonial-author {
    flex-direction: column;
    text-align: center;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-actions .ant-btn {
    width: 200px;
  }
}
</style>
