import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import relativeTime from 'dayjs/plugin/relativeTime';
import duration from 'dayjs/plugin/duration';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';

// 注册插件
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);
dayjs.extend(duration);
dayjs.extend(customParseFormat);
dayjs.extend(weekday);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);
dayjs.extend(advancedFormat);
dayjs.extend(quarterOfYear);

// 设置默认语言
dayjs.locale('zh-cn');

/**
 * 设置 dayjs 语言
 * @param locale 语言代码
 */
export function setDayjsLocale(locale: string) {
  console.log('[setDayjsLocale] 被调用，参数:', locale);

  const localeMap: Record<string, string> = {
    'zh-CN': 'zh-cn',
    zh: 'zh-cn',
    'en-US': 'en',
    en: 'en',
  };

  const dayjsLocale = localeMap[locale] || 'zh-cn';
  console.log('[setDayjsLocale] 映射结果:', locale, '->', dayjsLocale);

  try {
    const beforeLocale = dayjs.locale();
    dayjs.locale(dayjsLocale);
    const afterLocale = dayjs.locale();

    console.log(`[dayjs] 语言切换: ${beforeLocale} -> ${afterLocale}`);
    console.log(`[dayjs] 当前时间测试: ${dayjs().format('YYYY年MM月DD日 HH:mm:ss')}`);
    console.log(`[dayjs] 当前时间测试: ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`);
  } catch (error) {
    console.warn(`[dayjs] 语言切换失败: ${dayjsLocale}`, error);
    // 回退到中文
    dayjs.locale('zh-cn');
  }
}

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 */
export function formatDate(date?: dayjs.ConfigType, format = 'YYYY-MM-DD HH:mm:ss'): string {
  return dayjs(date).format(format);
}

/**
 * 获取相对时间
 * @param date 日期
 */
export function getRelativeTime(date: dayjs.ConfigType): string {
  return dayjs(date).fromNow();
}

/**
 * 获取时间差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param unit 单位
 */
export function getDiff(startDate: dayjs.ConfigType, endDate: dayjs.ConfigType, unit?: dayjs.ManipulateType): number {
  return dayjs(endDate).diff(dayjs(startDate), unit);
}

/**
 * 判断是否为今天
 * @param date 日期
 */
export function isToday(date: dayjs.ConfigType): boolean {
  return dayjs(date).isSame(dayjs(), 'day');
}

/**
 * 判断是否为本周
 * @param date 日期
 */
export function isThisWeek(date: dayjs.ConfigType): boolean {
  return dayjs(date).isSame(dayjs(), 'week');
}

/**
 * 判断是否为本月
 * @param date 日期
 */
export function isThisMonth(date: dayjs.ConfigType): boolean {
  return dayjs(date).isSame(dayjs(), 'month');
}

/**
 * 判断是否为本年
 * @param date 日期
 */
export function isThisYear(date: dayjs.ConfigType): boolean {
  return dayjs(date).isSame(dayjs(), 'year');
}

export { dayjs };
export default dayjs;
