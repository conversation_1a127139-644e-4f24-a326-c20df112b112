import {
  MedicineBoxOutlined,
  ExperimentOutlined,
  RocketOutlined,
  CalculatorOutlined,
  BuildOutlined,
  BookOutlined,
  ShopOutlined,
} from '@ant-design/icons-vue';

// 学科分类
export const disciplines = [
  {
    key: 'clinicalMedicine',
    icon: MedicineBoxOutlined,
    subspecialties: ['generalMedicine', 'diagnosticSpecialties', 'internalMedicine', 'publicHealth', 'surgery', 'interdisciplinaryMedicine'],
  },
  {
    key: 'lifeSciences',
    icon: ExperimentOutlined,
    subspecialties: [
      'agriculturalSciences',
      'biochemistry',
      'cellBiology',
      'environmentEcology',
      'evolutionDevelopment',
      'interdisciplinaryBiology',
      'microbiology',
      'molecularBiology',
      'neuroscience',
      'pharmacologyToxicology',
      'zoologyPlantBiology',
      'veterinaryMedicine',
    ],
  },
  {
    key: 'physicalSciences',
    icon: RocketOutlined,
    subspecialties: ['chemistry', 'physics', 'paleontology', 'spaceScience', 'earthSciences'],
  },
  {
    key: 'mathComputer',
    icon: CalculatorOutlined,
    subspecialties: ['computerScience', 'mathematics', 'statistics'],
  },
  {
    key: 'engineeringMaterials',
    icon: BuildOutlined,
    subspecialties: ['engineering', 'materialScience'],
  },
  {
    key: 'humanitiesSocial',
    icon: BookOutlined,
    subspecialties: ['education', 'artsHumanities', 'communication', 'publicPolicy', 'informationManagement', 'socialBehavioral'],
  },
  {
    key: 'businessLaw',
    icon: ShopOutlined,
    subspecialties: ['business', 'law'],
  },
];

// 推荐服务
export const recommendedServices = [
  {
    key: 'editing',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/866c7b29-68db-43a3-8ce5-a0bc0be98570_english-editing-64.png?auto=compress,format&w=1080',
    link: 'https://www.aje.cn/services/editing',
  },
  {
    key: 'vipEditing',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/c38a02d5-9ad3-4df2-9b6f-5a0068c5253c_vip-editing.png?auto=compress,format&w=1080',
    link: 'https://www.aje.cn/services/vip-editing',
  },
  {
    key: 'scientificEditing',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/2ff95af9-af02-46a1-bffa-2b446df899b8_scientific-editing-128.png?auto=format,compress&w=1080',
    link: 'https://www.aje.cn/services/scientific-editing',
  },
  {
    key: 'presubmissionReview',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/fc927461-7932-461b-9983-cf1441cc454e_presubmission+review+illustration.png?auto=compress,format&w=1080',
    link: 'https://www.aje.cn/services/presubmission-review',
  },
  {
    key: 'translation',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/a7abaac4-2097-44aa-9cdb-eebeb56440a3_translation-128.png?auto=compress,format&w=1080',
    link: 'https://www.aje.cn/services/translation',
  },
  {
    key: 'journalRecommendation',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAfPdc1huKlra_journal-recommendation.png?auto=format,compress&w=1080',
    link: 'https://www.aje.cn/services/journal-recommendation',
  },
];

// 子专业项目
export const subspecialtyItems: Record<string, string[]> = {
  // 临床医学
  generalMedicine: [
    'allergy',
    'criticalCare',
    'dentistry',
    'dermatology',
    'generalPractice',
    'hospitalMedicine',
    'medicalGenetics',
    'nutrition',
    'obstetrics',
    'ophthalmology',
    'pediatrics',
    'physicalMedicine',
    'physiology',
    'psychiatry',
    'sportsMedicine',
  ],
  diagnosticSpecialties: ['audiology', 'laboratoryDiagnostics', 'nuclearMedicine', 'pathology', 'physiology'],
  internalMedicine: [
    'anesthesiology',
    'cardiology',
    'endocrinology',
    'gastroenterology',
    'geriatrics',
    'hematology',
    'infectiousDiseases',
    'maternalFetal',
    'neurology',
    'nuclearMedicine',
    'oncology',
    'orthopedics',
    'otolaryngology',
    'pulmonology',
    'rheumatology',
    'reproductiveMedicine',
    'urology',
    'vascularMedicine',
  ],
  publicHealth: ['epidemiology', 'tropicalMedicine', 'occupationalMedicine', 'healthEconomics', 'preventiveMedicine', 'statisticalEpidemiology'],
  surgery: [
    'cardiothoracicSurgery',
    'gastrointestinalSurgery',
    'generalSurgery',
    'headNeckSurgery',
    'hepatobiliary',
    'neurosurgery',
    'orthopedicSurgery',
    'plasticSurgery',
    'gynecologicSurgery',
  ],
  interdisciplinaryMedicine: [
    'anesthesiology',
    'biomedicalEngineering',
    'clinicalPharmacology',
    'forensicMedicine',
    'integrativeMedicine',
    'medicalEthics',
    'medicalInformatics',
    'medicalPhysics',
    'nuclearMedicine',
    'nursing',
    'nutrition',
    'personalizedMedicine',
    'translationalMedicine',
  ],

  // 生命科学
  agriculturalSciences: [
    'agriculturalEconomics',
    'agriculturalEngineering',
    'agroecology',
    'agronomy',
    'animalScience',
    'aquaculture',
    'foodScience',
    'forestry',
    'horticulture',
  ],
  biochemistry: ['analyticalBiochemistry', 'appliedBiochemistry', 'biochemicalMethods', 'biophysics', 'generalBiochemistry', 'structuralBiology'],
  cellBiology: [
    'cellTissueEngineering',
    'cellCommunication',
    'cellCycle',
    'cellMigration',
    'cellSurvival',
    'cellMetabolism',
    'generalCellBiology',
    'immunology',
    'matrixBiology',
    'stemCells',
  ],
  environmentEcology: [
    'agroecology',
    'behavioralEcology',
    'biogeography',
    'conservationBiology',
    'ecologicalModeling',
    'environmentalEngineering',
    'environmentalPolicy',
    'forestry',
    'marineEcology',
    'populationBiology',
    'renewableResources',
    'terrestrialEcology',
    'wildlifeBiology',
  ],
  evolutionDevelopment: ['developmentalBiology', 'evolutionaryBiology', 'evoDevo', 'stemCells'],
  interdisciplinaryBiology: [
    'mathematicalBiology',
    'bioinformatics',
    'biomedicalEngineering',
    'biophysics',
    'biopolymers',
    'biostatistics',
    'biotechnology',
    'cancerBiology',
    'chemicalBiology',
    'computationalBiology',
    'systemsBiology',
  ],
  microbiology: ['appliedMicrobiology', 'bacteriology', 'generalMicrobiology', 'mycology', 'parasitology', 'virology'],
  molecularBiology: [
    'cellCommunication',
    'epigenetics',
    'evolutionaryGenetics',
    'medicalGenetics',
    'molecularBiology',
    'molecularEpidemiology',
    'genetics',
    'populationGenetics',
  ],
  neuroscience: ['cellularNeuroscience', 'cognitiveNeuroscience', 'computationalNeuroscience', 'developmentalNeuroscience', 'diseaseNeurobiology'],
  pharmacologyToxicology: [
    'clinicalPharmacology',
    'drugDelivery',
    'drugDiscovery',
    'pharmacodynamics',
    'pharmacokinetics',
    'toxicology',
    'vaccineDevelopment',
  ],
  zoologyPlantBiology: [
    'animalBehavior',
    'animalPhysiology',
    'botany',
    'entomology',
    'marineBiology',
    'plantMolecularBiology',
    'plantPhysiology',
    'taxonomy',
    'wildlifeBiology',
  ],
  veterinaryMedicine: ['internalMedicine', 'largeAnimalMedicine', 'smallAnimalMedicine', 'surgery', 'veterinaryEpidemiology', 'zoonoses'],

  // 物理科学
  chemistry: [
    'agrochemicals',
    'analyticalChemistry',
    'bioinorganicChemistry',
    'biochemistry',
    'biopolymers',
    'catalysis',
    'cementChemistry',
    'computationalChemistry',
    'crystallography',
    'electrochemistry',
    'environmentalChemistry',
    'foodChemistry',
    'inorganicChemistry',
    'massSpectrometry',
    'materialChemistry',
    'medicinalChemistry',
    'nanoscience',
    'naturalProducts',
    'nuclearChemistry',
    'organicChemistry',
    'photonics',
    'physicalChemistry',
    'polymerScience',
    'spectroscopy',
    'surfaceChemistry',
  ],
  physics: [
    'atomicPhysics',
    'biophysics',
    'computationalPhysics',
    'electronicsPhysics',
    'condensedMatter',
    'highEnergyPhysics',
    'magnetism',
    'nanoscience',
    'nuclearPhysics',
    'optics',
    'plasmaPhysics',
    'scattering',
    'softMatter',
    'theoreticalPhysics',
    'thermodynamics',
  ],
  paleontology: ['paleobotany', 'paleoecology', 'paleozoology', 'geology'],
  spaceScience: [
    'astrobiology',
    'astrochemistry',
    'astronomy',
    'astrophysics',
    'cometResearch',
    'meteorology',
    'planetaryScience',
    'spaceExploration',
    'theoreticalAstrophysics',
  ],
  earthSciences: [
    'atmosphericScience',
    'climateAnalysis',
    'climatology',
    'economicGeology',
    'geochemistry',
    'gis',
    'geology',
    'geomorphology',
    'geophysics',
    'glaciology',
    'historicalGeology',
    'hydrology',
    'meteorology',
    'oceanography',
    'petroleumGeology',
    'petrology',
    'physicalGeography',
    'planetaryGeology',
    'seismology',
    'volcanology',
  ],

  // 数学和计算机科学
  computerScience: [
    'artificialIntelligence',
    'computerArchitecture',
    'gis',
    'graphicsSystems',
    'informationRetrieval',
    'informationTheory',
    'numericalAnalysis',
    'robotics',
    'medicalEngineering',
    'systemsNetworks',
    'theoreticalCS',
  ],
  mathematics: [
    'algebra',
    'analysis',
    'appliedMathematics',
    'computationalBiology',
    'computationalMathematics',
    'discreteMathematics',
    'financialMathematics',
    'geometry',
    'logic',
    'mathematicalPhysics',
    'numericalAnalysis',
    'pureMathematics',
    'topology',
  ],
  statistics: ['appliedStatistics', 'biostatistics', 'decisionScience', 'operationsResearch', 'statisticalEpidemiology', 'statisticalTheory'],

  // 工程和材料科学
  engineering: [
    'acoustics',
    'aeronautics',
    'agriculturalEngineering',
    'biomedicalEngineering',
    'biotechnology',
    'cellTissueEngineering',
    'chemicalEngineering',
    'civilEngineering',
    'computerArchitecture',
    'electricalEngineering',
    'energyEngineering',
    'environmentalEngineering',
    'industrialEngineering',
    'materialEngineering',
    'mechanicalEngineering',
    'nuclearEngineering',
    'oceanEngineering',
    'petroleumEngineering',
    'robotics',
    'softwareEngineering',
    'systemsEngineering',
  ],
  materialScience: [
    'biomaterials',
    'ceramics',
    'electronicMaterials',
    'magneticMaterials',
    'materialChemistry',
    'materialEngineering',
    'materialTheory',
    'metallurgy',
    'nanoscience',
    'opticalMaterials',
    'polymerScience',
  ],

  // 人文和社会科学
  education: ['educationalPhilosophy', 'educationalPsychology', 'schoolCounseling', 'specialEducation'],
  artsHumanities: [
    'architecture',
    'artHistory',
    'behavioralGeography',
    'classicalStudies',
    'culturalStudies',
    'genderStudies',
    'history',
    'linguistics',
    'literature',
    'musicology',
    'philosophy',
    'religiousStudies',
    'theaterStudies',
    'womensStudies',
  ],
  communication: ['culturalStudies', 'journalism', 'mediaStudies', 'publicRelations', 'publishing', 'scienceCommunication', 'technicalCommunication'],
  publicPolicy: [
    'agriculturalPolicy',
    'urbanManagement',
    'environmentalPolicy',
    'healthPolicy',
    'internationalRelations',
    'otherPublicPolicy',
    'publicAdministration',
    'socialPolicy',
  ],
  informationManagement: ['informationRetrieval', 'libraryScience'],
  socialBehavioral: [
    'economics',
    'anthropology',
    'archaeology',
    'behavioralGeography',
    'behavioralEconomics',
    'criminology',
    'marketing',
    'nursing',
    'socialWork',
    'sociology',
    'urbanStudies',
  ],

  // 商业和法律
  business: [
    'accounting',
    'entrepreneurship',
    'finance',
    'hospitalityTourism',
    'internationalBusiness',
    'leadership',
    'management',
    'marketing',
    'otherBusiness',
    'publicRelations',
  ],
  law: [
    'administrativeLaw',
    'bankingLaw',
    'businessLaw',
    'civilRights',
    'constitutionalLaw',
    'criminalLaw',
    'environmentalLaw',
    'familyLaw',
    'healthLaw',
    'immigrationLaw',
    'intellectualProperty',
    'internationalLaw',
    'taxLaw',
  ],
};
