/// <reference types="vite/client" />

// 全局类型定义
declare type Nullable<T> = T | null;
declare type Recordable<T = any> = Record<string, T>;

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_PORT: number;
  readonly VITE_ADMIN_URL: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_COMPANY_NAME: string;
  readonly VITE_COMPANY_PHONE: string;
  readonly VITE_COMPANY_EMAIL: string;
  readonly VITE_COMPANY_ADDRESS: string;
  readonly VITE_DEBUG: boolean;
  readonly VITE_OUTPUT_DIR: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
