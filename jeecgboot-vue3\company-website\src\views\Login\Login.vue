<template>
  <div class="login-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 登录主体区域 -->
    <div class="login-container">
      <div class="login-content">
        <!-- 左侧品牌区域 -->
        <div class="brand-section">
          <div class="brand-content">
            <div class="logo-section">
              <img
                src="https://www.aje.cn/externalimages/aje-cms-production/Zpqz9x5LeNNTxUJ7_AJEPoweredbyMPS300x300-1-.png?auto=format,compress&w=256"
                alt="AJE Logo" class="brand-logo" />
            </div>
            <h1>{{ t('login.brand.title') }}</h1>
            <p>{{ t('login.brand.description') }}</p>
            <div class="features-list">
              <div class="feature-item">
                <CheckCircleOutlined />
                <span>{{ t('login.brand.features.professional') }}</span>
              </div>
              <div class="feature-item">
                <CheckCircleOutlined />
                <span>{{ t('login.brand.features.trusted') }}</span>
              </div>
              <div class="feature-item">
                <CheckCircleOutlined />
                <span>{{ t('login.brand.features.global') }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧登录表单区域 -->
        <div class="form-section">
          <div class="form-container">
            <div class="form-header">
              <h2>{{ getCurrentTitle() }}</h2>
              <div class="mode-switch-link">
                <a v-if="currentMode === 'login'" @click="switchMode('register')">{{ t('login.switch.toRegister') }}</a>
                <a v-if="currentMode === 'register'" @click="switchMode('login')">{{ t('login.switch.toLogin') }}</a>
                <a v-if="currentMode === 'reset'" @click="switchMode('login')">{{ t('login.switch.backToLogin') }}</a>
              </div>
            </div>

            <div v-if="currentMode === 'login'" class="form-subtitle">
              <p>{{ t('login.form.subtitle') }}</p>
            </div>
            <div v-if="currentMode === 'reset'" class="form-subtitle">
              <p>{{ t('login.reset.subtitle') }}</p>
            </div>

            <!-- 登录表单 -->
            <a-form v-if="currentMode === 'login'" :model="loginForm" :rules="loginRules" @finish="handleLogin"
              @finishFailed="handleLoginFailed" layout="vertical" class="login-form">
              <!-- 用户名/邮箱输入 -->
              <a-form-item name="username" :label="t('login.form.username.label')">
                <a-input v-model:value="loginForm.username" :placeholder="t('login.form.username.placeholder')"
                  size="large">
                  <template #prefix>
                    <UserOutlined />
                  </template>
                </a-input>
              </a-form-item>

              <!-- 密码输入 -->
              <a-form-item name="password" :label="t('login.form.password.label')">
                <a-input-password v-model:value="loginForm.password" :placeholder="t('login.form.password.placeholder')"
                  size="large">
                  <template #prefix>
                    <LockOutlined />
                  </template>
                </a-input-password>
              </a-form-item>

              <!-- 记住我和忘记密码 -->
              <div class="form-options">
                <a-checkbox v-model:checked="loginForm.remember">
                  {{ t('login.form.remember') }}
                </a-checkbox>
                <a-button type="link" @click="switchMode('reset')" class="forgot-link">
                  {{ t('login.form.forgotPassword') }}
                </a-button>
              </div>

              <!-- 登录按钮 -->
              <a-form-item>
                <a-button type="primary" html-type="submit" size="large" :loading="loginLoading" class="login-button"
                  block>
                  {{ t('login.form.loginButton') }}
                </a-button>
              </a-form-item>
            </a-form>

            <!-- 注册表单 -->
            <a-form v-if="currentMode === 'register'" :model="registerForm" :rules="registerRules"
              @finish="handleRegister" layout="vertical" class="register-form">
              <!-- 邮箱输入 -->
              <a-form-item name="email" :label="t('login.form.username.label')">
                <a-input v-model:value="registerForm.email" :placeholder="t('login.form.username.placeholder')"
                  size="large">
                  <template #prefix>
                    <MailOutlined />
                  </template>
                </a-input>
              </a-form-item>

              <!-- 姓名输入 -->
              <div class="name-row">
                <a-form-item name="firstName" :label="t('login.register.firstName')" class="name-item">
                  <a-input v-model:value="registerForm.firstName" :placeholder="t('login.register.firstNamePlaceholder')"
                    size="large" />
                </a-form-item>
                <a-form-item name="lastName" :label="t('login.register.lastName')" class="name-item">
                  <a-input v-model:value="registerForm.lastName" :placeholder="t('login.register.lastNamePlaceholder')"
                    size="large" />
                </a-form-item>
              </div>

              <!-- 注册按钮 -->
              <a-form-item>
                <a-button type="primary" html-type="submit" size="large" :loading="loginLoading" class="register-button"
                  block>
                  {{ t('login.register.button') }}
                </a-button>
              </a-form-item>

              <!-- 法律条款 -->
              <div class="terms-text">
                <span>{{ t('login.register.terms.text') }}</span>
                <a href="https://www.aje.com/terms-of-service/" target="_blank">{{ t('login.register.terms.service')
                }}</a>
                <span>{{ t('login.register.terms.and') }}</span>
                <a href="https://www.aje.com/security-and-privacy" target="_blank">{{ t('login.register.terms.privacy')
                }}</a>
                <span>{{ t('login.register.terms.dot') }}</span>
              </div>
            </a-form>

            <!-- 重置密码表单 -->
            <a-form v-if="currentMode === 'reset'" :model="resetForm" :rules="resetRules" @finish="handleReset"
              layout="vertical" class="reset-form">
              <!-- 邮箱输入 -->
              <a-form-item name="email" :label="t('login.form.username.label')">
                <a-input v-model:value="resetForm.email" :placeholder="t('login.form.username.placeholder')" size="large">
                  <template #prefix>
                    <MailOutlined />
                  </template>
                </a-input>
              </a-form-item>

              <!-- 重置按钮 -->
              <a-form-item>
                <a-button type="primary" html-type="submit" size="large" :loading="loginLoading" class="reset-button"
                  block>
                  {{ t('login.reset.button') }}
                </a-button>
              </a-form-item>
            </a-form>

            <!-- 帮助链接 (仅在登录模式显示) -->
            <div v-if="currentMode === 'login'" class="help-section">
              <a-button type="link" @click="handleHelp" class="help-link">
                {{ t('login.help.support') }}
              </a-button>
              <span class="separator">|</span>
              <a-button type="link" @click="handleContact" class="help-link">
                {{ t('login.help.contact') }}
              </a-button>
            </div>

            <!-- 法律条款 (在重置密码模式显示) -->
            <div v-if="currentMode === 'reset'" class="legal-notice">
              <p>{{ t('login.legal.google') }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  UserOutlined,
  LockOutlined,
  CheckCircleOutlined,
  MailOutlined
} from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import type { Rule } from 'ant-design-vue/es/form'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 当前模式：login, register, reset
const currentMode = ref('login')

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

const registerForm = reactive({
  email: '',
  firstName: '',
  lastName: ''
})

const resetForm = reactive({
  email: ''
})

// 加载状态
const loginLoading = ref(false)

// 表单验证规则
const loginRules: Record<string, Rule[]> = {
  username: [
    { required: true, message: t('login.validation.username.required'), trigger: 'blur' },
    { min: 3, message: t('login.validation.username.min'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('login.validation.password.required'), trigger: 'blur' },
    { min: 6, message: t('login.validation.password.min'), trigger: 'blur' }
  ]
}

const registerRules: Record<string, Rule[]> = {
  email: [
    { required: true, message: t('login.validation.email.required'), trigger: 'blur' },
    { type: 'email', message: t('login.validation.email.format'), trigger: 'blur' }
  ],
  firstName: [
    { required: true, message: t('login.validation.firstName.required'), trigger: 'blur' }
  ],
  lastName: [
    { required: true, message: t('login.validation.lastName.required'), trigger: 'blur' }
  ]
}

const resetRules: Record<string, Rule[]> = {
  email: [
    { required: true, message: t('login.validation.email.required'), trigger: 'blur' },
    { type: 'email', message: t('login.validation.email.format'), trigger: 'blur' }
  ]
}

// 模式切换
const switchMode = (mode: 'login' | 'register' | 'reset') => {
  currentMode.value = mode
  // 清空表单数据
  if (mode === 'login') {
    Object.assign(loginForm, { username: '', password: '', remember: false })
  } else if (mode === 'register') {
    Object.assign(registerForm, { email: '', firstName: '', lastName: '' })
  } else if (mode === 'reset') {
    Object.assign(resetForm, { email: '' })
  }
}

// 获取当前标题
const getCurrentTitle = () => {
  switch (currentMode.value) {
    case 'login':
      return t('login.form.title')
    case 'register':
      return t('login.register.title')
    case 'reset':
      return t('login.reset.title')
    default:
      return t('login.form.title')
  }
}

// 处理登录
const handleLogin = async (values: any) => {
  loginLoading.value = true
  try {
    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟登录成功，存储token
    const mockToken = 'mock-jwt-token-' + Date.now()
    localStorage.setItem('userToken', mockToken)
    localStorage.setItem('userInfo', JSON.stringify({
      name: '杨 程',
      email: values.username
    }))

    message.success(t('login.messages.success'))
    // 登录成功后跳转到研究者页面
    router.push('/researcher')
  } catch (error) {
    message.error(t('login.messages.error'))
  } finally {
    loginLoading.value = false
  }
}

// 处理登录失败
const handleLoginFailed = (errorInfo: any) => {
  console.log('Login failed:', errorInfo)
  message.error(t('login.messages.validationError'))
}

// 处理注册
const handleRegister = async (values: any) => {
  loginLoading.value = true
  try {
    // 模拟注册API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    message.success(t('login.messages.registerSuccess'))
    // 注册成功后切换到登录模式
    switchMode('login')
  } catch (error) {
    message.error(t('login.messages.registerError'))
  } finally {
    loginLoading.value = false
  }
}

// 处理重置密码
const handleReset = async (values: any) => {
  loginLoading.value = true
  try {
    // 模拟重置密码API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    message.success(t('login.messages.resetSuccess'))
    // 重置成功后切换到登录模式
    switchMode('login')
  } catch (error) {
    message.error(t('login.messages.resetError'))
  } finally {
    loginLoading.value = false
  }
}

// 处理帮助
const handleHelp = () => {
  window.open('https://customer-support.aje.com/portal/zh/kb', '_blank')
}

// 处理联系我们
const handleContact = () => {
  router.push('/contact-us')
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 登录容器 */
.login-container {
  min-height: calc(100vh - 160px);
  /* 减去页头页脚高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
}

.login-content {
  max-width: 1200px;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

/* 左侧品牌区域 */
.brand-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-content {
  text-align: center;
  max-width: 400px;
}

.logo-section {
  margin-bottom: 32px;
}

.brand-logo {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  background: white;
  padding: 16px;
}

.brand-content h1 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
  line-height: 1.2;
}

.brand-content p {
  font-size: 16px;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 32px;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  opacity: 0.9;
}

.feature-item .anticon {
  color: #52c41a;
  font-size: 16px;
}

/* 右侧表单区域 */
.form-section {
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.form-header p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

/* 表单样式 */
.login-form {
  margin-bottom: 24px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-link {
  padding: 0;
  font-size: 14px;
  color: #667eea;
}

.forgot-link:hover {
  color: #764ba2;
}

.login-button {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 社交登录 */
.social-login {
  margin-bottom: 24px;
}

.social-button {
  height: 48px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  transition: all 0.3s ease;
}



.social-button .anticon {
  margin-right: 8px;
  font-size: 16px;
}

/* 注册和帮助区域 */
.register-section {
  text-align: center;
  margin-bottom: 16px;
  font-size: 14px;
  color: #4a5568;
}

.register-link {
  padding: 0;
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
}

.register-link:hover {
  color: #764ba2;
}

.help-section {
  text-align: center;
  font-size: 12px;
}

.help-link {
  padding: 0;
  font-size: 12px;
  color: #6b7280;
}

.help-link:hover {
  color: #667eea;
}

.separator {
  margin: 0 8px;
  color: #d1d5db;
}

/* 模式切换链接 */
.mode-switch-link {
  margin-top: 8px;
}

.mode-switch-link a {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.mode-switch-link a:hover {
  color: #764ba2;
  text-decoration: none;
}

/* 姓名行样式 */
.name-row {
  display: flex;
  gap: 16px;
}

.name-item {
  flex: 1;
}

/* 条款文本样式 */
.terms-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin-top: 16px;
  text-align: center;
}

.terms-text a {
  color: #667eea;
  text-decoration: none;
}

.terms-text a:hover {
  text-decoration: underline;
}

/* 法律条款样式 */
.legal-notice {
  margin-top: 24px;
  text-align: center;
}

.legal-notice p {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    grid-template-columns: 1fr;
    min-height: auto;
  }

  .brand-section {
    padding: 40px 24px;
  }

  .brand-content h1 {
    font-size: 24px;
  }

  .brand-logo {
    width: 80px;
    height: 80px;
  }

  .form-section {
    padding: 40px 24px;
  }

  .form-header h2 {
    font-size: 24px;
  }

  .login-container {
    padding: 20px 16px;
    min-height: calc(100vh - 120px);
  }
}

@media (max-width: 480px) {
  .brand-section {
    padding: 32px 16px;
  }

  .form-section {
    padding: 32px 16px;
  }

  .login-container {
    padding: 16px 12px;
  }

  .login-content {
    border-radius: 12px;
  }
}
</style>
