import { EditOutlined, TeamOutlined, SafetyOutlined, MailOutlined } from '@ant-design/icons-vue';

// Service features for introduction section
export const serviceFeatures = [
  {
    key: 'nativeEditors',
    icon: TeamOutlined,
  },
  {
    key: 'expertMatching',
    icon: EditOutlined,
  },
  {
    key: 'qualityGuarantee',
    icon: SafetyOutlined,
  },
  {
    key: 'coverLetters',
    icon: MailOutlined,
  },
];

// Standard editing service features
export const standardFeatures = [
  'grammarCorrection',
  'originalMeaning',
  'writingStyle',
  'nativeEditors',
  'expertEditing',
  'universityBackground',
  'advancedTraining',
  'reEditDiscount',
  'editingCertificate',
  'coverLetters',
  'expertQA',
  'limitedOffer',
];

// Premium editing service features
export const premiumFeatures = [
  'grammarCorrection',
  'originalMeaning',
  'writingStyle',
  'nativeEditors',
  'expertEditing',
  'universityBackground',
  'advancedTraining',
  'unlimitedReEdit',
  'editingCertificate',
  'coverLetters',
  'expertQA',
  'limitedOffer',
];

// Scientific review editing features
export const scientificReviewFeatures = [
  {
    key: 'originality',
    title: 'standardEditing.scientificReview.features.originality.title',
    description: 'standardEditing.scientificReview.features.originality.description',
  },
  {
    key: 'researchGoals',
    title: 'standardEditing.scientificReview.features.researchGoals.title',
    description: 'standardEditing.scientificReview.features.researchGoals.description',
  },
  {
    key: 'validity',
    title: 'standardEditing.scientificReview.features.validity.title',
    description: 'standardEditing.scientificReview.features.validity.description',
  },
  {
    key: 'organization',
    title: 'standardEditing.scientificReview.features.organization.title',
    description: 'standardEditing.scientificReview.features.organization.description',
  },
];

// Scientific editing service features
export const scientificEditingFeatures = [
  {
    key: 'deepAssessment',
    title: 'standardEditing.scientificEditing.features.deepAssessment.title',
    description: 'standardEditing.scientificEditing.features.deepAssessment.description',
  },
  {
    key: 'strategicEditing',
    title: 'standardEditing.scientificEditing.features.strategicEditing.title',
    description: 'standardEditing.scientificEditing.features.strategicEditing.description',
  },
  {
    key: 'customReport',
    title: 'standardEditing.scientificEditing.features.customReport.title',
    description: 'standardEditing.scientificEditing.features.customReport.description',
  },
  {
    key: 'continuousSupport',
    title: 'standardEditing.scientificEditing.features.continuousSupport.title',
    description: 'standardEditing.scientificEditing.features.continuousSupport.description',
  },
];

// Quality guarantee features
export const qualityGuaranteeFeatures = [
  {
    key: 'satisfaction',
    title: 'standardEditing.qualityGuarantee.features.satisfaction.title',
    description: 'standardEditing.qualityGuarantee.features.satisfaction.description',
  },
  {
    key: 'reEditing',
    title: 'standardEditing.qualityGuarantee.features.reEditing.title',
    description: 'standardEditing.qualityGuarantee.features.reEditing.description',
  },
  {
    key: 'contact',
    title: 'standardEditing.qualityGuarantee.features.contact.title',
    description: 'standardEditing.qualityGuarantee.features.contact.description',
  },
];

// 导入图片
import presubmissionReviewImg from './img/presubmission-review.png';
import manuscriptFormattingImg from './img/manuscript-formatting.png';
import journalRecommendationImg from './img/journal-recommendation.png';

// Related services
export const relatedServices = [
  {
    key: 'presubmissionReview',
    title: 'standardEditing.relatedServices.presubmissionReview.title',
    description: 'standardEditing.relatedServices.presubmissionReview.description',
    price: 'standardEditing.relatedServices.presubmissionReview.price',
    image: presubmissionReviewImg,
    link: '/services/presubmission-review',
  },
  {
    key: 'formatting',
    title: 'standardEditing.relatedServices.formatting.title',
    description: 'standardEditing.relatedServices.formatting.description',
    price: 'standardEditing.relatedServices.formatting.price',
    image: manuscriptFormattingImg,
    link: '/services/formatting',
  },
  {
    key: 'journalRecommendation',
    title: 'standardEditing.relatedServices.journalRecommendation.title',
    description: 'standardEditing.relatedServices.journalRecommendation.description',
    price: 'standardEditing.relatedServices.journalRecommendation.price',
    image: journalRecommendationImg,
    link: '/services/journal-recommendation',
  },
];

// FAQ data
export const faqData = [
  {
    key: '1',
    question: 'standardEditing.faq.editors.question',
    answer: 'standardEditing.faq.editors.answer',
  },
  {
    key: '2',
    question: 'standardEditing.faq.certificate.question',
    answer: 'standardEditing.faq.certificate.answer',
  },
  {
    key: '3',
    question: 'standardEditing.faq.rcp.question',
    answer: 'standardEditing.faq.rcp.answer',
  },
  {
    key: '4',
    question: 'standardEditing.faq.latex.question',
    answer: 'standardEditing.faq.latex.answer',
  },
  {
    key: '5',
    question: 'standardEditing.faq.coverLetter.question',
    answer: 'standardEditing.faq.coverLetter.answer',
  },
];
