import { defineStore } from 'pinia';
import { store } from '@/store';
import { createLocalStorage } from '@/utils/cache';

const ls = createLocalStorage();

export interface UserInfo {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  organization: string;
  department?: string;
  country: string;
  title: string;
  academicLevel?: string;
  researchField: string;
  avatar?: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  twoFactorEnabled: boolean;
  language: string;
  timezone: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  marketingEmails: boolean;
  createdAt: string;
  lastLoginAt: string;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  marketingEmails: boolean;
  currency: string;
  dateFormat: string;
  theme: 'light' | 'dark' | 'auto';
}

interface UserState {
  userInfo: UserInfo | null;
  preferences: UserPreferences;
  isLoggedIn: boolean;
  token: string | null;
}

const defaultPreferences: UserPreferences = {
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  emailNotifications: true,
  smsNotifications: false,
  marketingEmails: true,
  currency: 'CNY',
  dateFormat: 'YYYY-MM-DD',
  theme: 'light',
};

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): UserState => ({
    userInfo: ls.get('userInfo') || null,
    preferences: ls.get('userPreferences') || defaultPreferences,
    isLoggedIn: !!ls.get('token'),
    token: ls.get('token') || null,
  }),

  getters: {
    getUserInfo(): UserInfo | null {
      return this.userInfo;
    },

    getFullName(): string {
      if (!this.userInfo) return '';
      return `${this.userInfo.firstName} ${this.userInfo.lastName}`.trim();
    },

    getPreferences(): UserPreferences {
      return this.preferences;
    },

    getIsLoggedIn(): boolean {
      return this.isLoggedIn;
    },

    getToken(): string | null {
      return this.token;
    },
  },

  actions: {
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo;
      ls.set('userInfo', userInfo);
    },

    updateUserInfo(updates: Partial<UserInfo>) {
      if (this.userInfo) {
        this.userInfo = { ...this.userInfo, ...updates };
        ls.set('userInfo', this.userInfo);
      }
    },

    setPreferences(preferences: Partial<UserPreferences>) {
      this.preferences = { ...this.preferences, ...preferences };
      ls.set('userPreferences', this.preferences);
    },

    setToken(token: string) {
      this.token = token;
      this.isLoggedIn = true;
      ls.set('token', token);
    },

    logout() {
      this.userInfo = null;
      this.token = null;
      this.isLoggedIn = false;
      ls.remove('userInfo');
      ls.remove('token');
    },

    // 登录 - 调用API接口
    async login(email: string, password: string) {
      try {
        // 实际项目中调用登录API
        // const response = await api.post('/auth/login', { email, password });

        // 模拟API调用
        const mockUserInfo: UserInfo = {
          id: '1',
          email: email,
          firstName: '小',
          lastName: '程',
          phone: '+86 138 0013 8000',
          organization: '清华大学',
          country: 'China',
          title: '博士研究生',
          researchField: '计算机科学',
          emailVerified: true,
          phoneVerified: false,
          twoFactorEnabled: false,
          language: 'zh-CN',
          timezone: 'Asia/Shanghai',
          emailNotifications: true,
          smsNotifications: false,
          marketingEmails: true,
          createdAt: '2023-01-15T08:00:00Z',
          lastLoginAt: new Date().toISOString(),
        };

        this.setUserInfo(mockUserInfo);
        this.setToken('mock-jwt-token-' + Date.now());

        return { success: true, user: mockUserInfo };
      } catch (error) {
        throw new Error('登录失败');
      }
    },

    // 更新密码
    async updatePassword(currentPassword: string, newPassword: string) {
      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true, message: 'Password updated successfully' });
        }, 1000);
      });
    },

    // 启用/禁用双因素认证
    async toggleTwoFactor(enabled: boolean) {
      if (this.userInfo) {
        this.updateUserInfo({ twoFactorEnabled: enabled });
      }
      return { success: true };
    },

    // 发送验证邮件
    async sendVerificationEmail() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true, message: 'Verification email sent' });
        }, 1000);
      });
    },

    // 验证手机号
    async verifyPhone(code: string) {
      if (this.userInfo) {
        this.updateUserInfo({ phoneVerified: true });
      }
      return { success: true };
    },
  },
});

// 在setup外使用
export function useUserStoreWithOut() {
  return useUserStore(store);
}
