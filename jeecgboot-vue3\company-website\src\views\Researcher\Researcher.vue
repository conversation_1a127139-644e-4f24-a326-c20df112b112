<template>
  <div class="researcher-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 主要内容区域 -->
    <div class="researcher-container">
      <!-- 优惠活动横幅 -->
      <div class="promotion-banner">
        <p>{{ t('researcher.promotion.text') }}</p>
      </div>

      <!-- 欢迎信息 -->
      <div class="welcome-section">
        <div class="welcome-info">
          <h1>{{ t('researcher.welcome.title') }}, {{ userInfo.name }}</h1>
          <div class="user-actions">
            <!-- 这里可以添加用户操作按钮 -->
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 未完成订单提醒 -->
        <!-- <div v-if="hasIncompleteOrder" class="incomplete-order-notice">
          <p>
            {{ t('researcher.incompleteOrder.text') }}
            <a @click="continueOrder" class="continue-link">{{ t('researcher.incompleteOrder.continue') }}</a>
          </p>
        </div> -->

        <!-- 我的订单区域 -->
        <div class="orders-section">
          <div class="section-header">
            <h2>{{ t('researcher.orders.title') }}</h2>
            <div class="orders-summary">
              {{ t('researcher.orders.total', { count: totalOrders }) }}
            </div>
          </div>

          <!-- 订单列表 -->
          <div v-if="paginatedOrders.length > 0" class="orders-list">
            <div v-for="order in paginatedOrders" :key="order.id" class="order-item">
              <div class="order-header">
                <span class="order-id">{{ t('researcher.orders.orderId') }}: {{ order.id }}</span>
                <span class="order-status" :class="getStatusClass(order.status)">
                  {{ getStatusText(order.status) }}
                </span>
              </div>
              <div class="order-details">
                <p class="order-title">{{ order.title }}</p>
                <p class="order-service">{{ order.service }}</p>
                <p class="order-date">{{ t('researcher.orders.submitDate') }}: {{ formatDate(order.submitDate) }}</p>
                <p class="order-price">{{ t('researcher.orders.price') }}: {{ formatPrice(order.price) }}</p>
              </div>
              <div class="order-actions">
                <a-button @click="viewOrder(order)" type="primary" size="small">
                  {{ t('researcher.orders.viewDetails') }}
                </a-button>
              </div>
            </div>
          </div>

          <!-- 分页组件 -->
          <div v-if="totalOrders > pageSize" class="pagination-wrapper">
            <a-pagination v-model:current="currentPage" :total="totalOrders" :page-size="pageSize"
              :show-size-changer="false" :show-quick-jumper="true" :show-total="(total, range) => t('researcher.orders.pagination.total', {
                start: range[0],
                end: range[1],
                total: total
              })" @change="handlePageChange" />
          </div>

          <!-- 空状态 -->
          <div v-if="totalOrders === 0" class="empty-state">
            <p>{{ t('researcher.orders.empty') }}</p>
            <a @click="submitManuscript" class="submit-link">
              {{ t('researcher.orders.submit') }}
              <ArrowRightOutlined />
            </a>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="divider"></div>

        <!-- 功能卡片区域 -->
        <div class="feature-cards">
          <!-- 推荐奖励卡片 -->
          <!-- <div class="feature-card">
            <div class="card-header">
              <GiftOutlined class="card-icon" />
              <h4>{{ t('researcher.referral.title') }}</h4>
            </div>
            <p class="card-description">{{ t('researcher.referral.description') }}</p>
            <div class="referral-code">
              <a-input v-model:value="referralCode" readonly class="referral-input" />
              <a-button @click="copyReferralCode" type="default">
                {{ t('researcher.referral.copy') }}
              </a-button>
            </div>
            <a @click="viewReferrals" class="card-link">
              {{ t('researcher.referral.viewReferrals') }}
            </a>
          </div> -->

          <!-- 会员等级卡片 -->
          <!-- <div class="feature-card">
            <div class="card-header">
              <CrownOutlined class="card-icon" />
              <h4>{{ t('researcher.membership.title') }}</h4>
            </div>
            <p class="card-description">{{ t('researcher.membership.description') }}</p>
            <div class="membership-progress">
              <a-progress :percent="membershipProgress" :show-info="false" stroke-color="#667eea" />
              <div class="membership-info">
                <span class="membership-level">{{ t('researcher.membership.level') }}</span>
                <span class="membership-points">{{ membershipPoints }} {{ t('researcher.membership.points') }}</span>
              </div>
            </div>
            <a @click="viewMembership" class="card-link">
              {{ t('researcher.membership.viewBenefits') }}
            </a>
          </div> -->

          <!-- 团队优惠代码卡片 -->
          <!-- <div class="feature-card">
            <div class="card-header">
              <TeamOutlined class="card-icon" />
              <h4>{{ t('researcher.teamCode.title') }}</h4>
            </div>
            <p class="card-description">{{ t('researcher.teamCode.description') }}</p>
            <div class="team-code-input">
              <a-input v-model:value="teamCode" :placeholder="t('researcher.teamCode.placeholder')" class="team-input" />
              <a-button @click="submitTeamCode" type="primary">
                {{ t('researcher.teamCode.submit') }}
              </a-button>
            </div>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { message } from 'ant-design-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import { mockOrders, type Order } from '../../data/mockOrders'
import {
  ArrowRightOutlined,
  GiftOutlined,
  CrownOutlined,
  TeamOutlined
} from '@ant-design/icons-vue'

const { t, locale } = useI18n()

const router = useRouter()

// 用户信息
const userInfo = reactive({
  name: '杨 程',
  email: '<EMAIL>'
})

// 订单数据
const orders = ref<Order[]>([])
const hasIncompleteOrder = ref(true)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(6)
const totalOrders = computed(() => orders.value.length)

// 分页后的订单数据
const paginatedOrders = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return orders.value.slice(start, end)
})

// 推荐奖励
const referralCode = ref('https://china.aje.com/r/FRFQRL38')

// 会员信息
const membershipProgress = ref(0)
const membershipPoints = ref(0)

// 团队代码
const teamCode = ref('')

// 获取订单状态样式类
const getStatusClass = (status: string) => {
  const statusMap = {
    'pending': 'status-pending',
    'processing': 'status-processing',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled'
  }
  return statusMap[status] || 'status-default'
}

// 获取订单状态文本
const getStatusText = (status: string) => {
  return t(`researcher.orders.status.${status}`)
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString(locale.value === 'zh-CN' ? 'zh-CN' : 'en-US')
}

// 格式化价格
const formatPrice = (price: number) => {
  return locale.value === 'zh-CN'
    ? `¥${price.toFixed(2)}`
    : `$${(price / 7).toFixed(2)}`
}

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page
}



// 继续未完成订单
const continueOrder = () => {
  // 跳转到订单创建页面
  window.open('/cn/researcher/submit/select', '_blank')
}

// 提交文稿
const submitManuscript = () => {
  // 跳转到文稿提交页面
  window.open('/cn/researcher/submit/new', '_blank')
}

// 查看订单详情
const viewOrder = (order: any) => {
  // 这里可以实现查看订单详情的逻辑
  message.info(t('researcher.messages.viewOrderDetails'))
}

// 复制推荐代码
const copyReferralCode = async () => {
  try {
    await navigator.clipboard.writeText(referralCode.value)
    message.success(t('researcher.messages.copySuccess'))
  } catch (error) {
    message.error(t('researcher.messages.copyError'))
  }
}

// 查看推荐记录
const viewReferrals = () => {
  router.push('/referral-rewards')
}

// 查看会员福利
const viewMembership = () => {
  router.push('/membership')
}

// 提交团队代码
const submitTeamCode = () => {
  if (!teamCode.value.trim()) {
    message.warning(t('researcher.messages.teamCodeRequired'))
    return
  }

  // 模拟提交团队代码
  message.success(t('researcher.messages.teamCodeSubmitted'))
  teamCode.value = ''
}

// 页面初始化
onMounted(() => {
  // 加载模拟订单数据
  orders.value = mockOrders
  console.log('Researcher page mounted with', orders.value.length, 'orders')
})
</script>

<style scoped>
.researcher-page {
  min-height: 100vh;
  background-color: #f8fafc;
}

.researcher-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 优惠活动横幅 */
.promotion-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 24px;
  margin: 20px 0;
  border-radius: 8px;
  text-align: center;
}

.promotion-banner p {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
}

/* 欢迎区域 */
.welcome-section {
  background: white;
  padding: 32px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.welcome-info h1 {
  font-size: 28px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 未完成订单提醒 */
.incomplete-order-notice {
  background: #fff7ed;
  border-left: 4px solid #f59e0b;
  padding: 16px 24px;
  margin-bottom: 0;
}

.incomplete-order-notice p {
  margin: 0;
  color: #92400e;
  font-size: 14px;
}

.continue-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
}

.continue-link:hover {
  text-decoration: underline;
}

/* 订单区域 */
.orders-section {
  padding: 32px 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.orders-summary {
  font-size: 14px;
  color: #64748b;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.order-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.order-id {
  font-weight: 600;
  color: #1a202c;
}

.order-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-processing {
  background: #dbeafe;
  color: #1e40af;
}

.status-completed {
  background: #d1fae5;
  color: #065f46;
}

.status-cancelled {
  background: #fee2e2;
  color: #991b1b;
}

.order-details p {
  margin: 4px 0;
  color: #64748b;
  font-size: 14px;
}

.order-title {
  font-weight: 500;
  color: #1a202c !important;
}

.order-price {
  font-weight: 600;
  color: #667eea !important;
}

.order-actions {
  margin-top: 16px;
  text-align: right;
}

/* 分页组件 */
.pagination-wrapper {
  margin-top: 32px;
  text-align: center;
  padding: 24px 0;
  border-top: 1px solid #e2e8f0;
}

.pagination-wrapper :deep(.ant-pagination) {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.pagination-wrapper :deep(.ant-pagination-item) {
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.pagination-wrapper :deep(.ant-pagination-item:hover) {
  border-color: #667eea;
}

.pagination-wrapper :deep(.ant-pagination-item-active) {
  background: #667eea;
  border-color: #667eea;
}

.pagination-wrapper :deep(.ant-pagination-item-active a) {
  color: white;
}

.pagination-wrapper :deep(.ant-pagination-prev),
.pagination-wrapper :deep(.ant-pagination-next) {
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.pagination-wrapper :deep(.ant-pagination-prev:hover),
.pagination-wrapper :deep(.ant-pagination-next:hover) {
  border-color: #667eea;
  color: #667eea;
}

.pagination-wrapper :deep(.ant-pagination-total-text) {
  color: #64748b;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 48px 24px;
}

.empty-state p {
  color: #64748b;
  margin-bottom: 16px;
}

.submit-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.submit-link:hover {
  text-decoration: underline;
}

/* 分隔线 */
.divider {
  height: 1px;
  background: #e2e8f0;
  margin: 0 24px;
}

/* 功能卡片区域 */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  padding: 32px 24px;
}

.feature-card {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.feature-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.card-icon {
  font-size: 20px;
  color: #667eea;
}

.card-header h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.card-description {
  color: #64748b;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
}

/* 推荐代码输入 */
.referral-code {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.referral-input {
  flex: 1;
}

/* 会员进度 */
.membership-progress {
  margin-bottom: 16px;
}

.membership-info {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 14px;
}

.membership-level {
  color: #1a202c;
  font-weight: 500;
}

.membership-points {
  color: #64748b;
}

/* 团队代码输入 */
.team-code-input {
  display: flex;
  gap: 8px;
}

.team-input {
  flex: 1;
}

/* 卡片链接 */
.card-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
}

.card-link:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .researcher-container {
    padding: 0 16px;
  }

  .welcome-section {
    padding: 24px 16px;
  }

  .orders-section {
    padding: 24px 16px;
  }

  .feature-cards {
    grid-template-columns: 1fr;
    padding: 24px 16px;
  }

  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .referral-code,
  .team-code-input {
    flex-direction: column;
  }

  .promotion-banner {
    margin: 16px 0;
    padding: 12px 16px;
  }

  .promotion-banner p {
    font-size: 13px;
  }
}
</style>
