<template>
  <div class="i18n-usage-example">
    <h1>多语言使用示例</h1>

    <div class="language-switcher">
      <h2>语言切换</h2>
      <LocalePicker />
    </div>

    <div class="usage-examples">
      <!-- 基础用法 -->
      <section class="example-section">
        <h3>1. 基础用法</h3>
        <div class="code-example">
          <h4>模板中使用：</h4>
          <pre><code>&lt;template&gt;
  &lt;h1&gt;{{ t('home.title') }}&lt;/h1&gt;
  &lt;p&gt;{{ t('home.hero.subtitle') }}&lt;/p&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { useI18n } from '@/locales/useI18n'
const { t } = useI18n()
&lt;/script&gt;</code></pre>

          <h4>效果：</h4>
          <div class="result">
            <h1>{{ t('home.title') }}</h1>
            <p>{{ t('home.hero.subtitle') }}</p>
          </div>
        </div>
      </section>

      <!-- 组合式API用法 -->
      <section class="example-section">
        <h3>2. 组合式 API 用法</h3>
        <div class="code-example">
          <h4>脚本中使用：</h4>
          <pre><code>&lt;script setup&gt;
import { useI18n } from '@/locales/useI18n'

const { t } = useI18n()

const message = t('components.message.success')
const title = t('about.company.title')
&lt;/script&gt;</code></pre>

          <h4>效果：</h4>
          <div class="result">
            <p>消息: {{ compositionApiMessage }}</p>
            <p>标题: {{ compositionApiTitle }}</p>
          </div>
        </div>
      </section>

      <!-- 动态参数 -->
      <section class="example-section">
        <h3>3. 动态参数</h3>
        <div class="code-example">
          <h4>带参数的翻译：</h4>
          <pre><code>// 语言文件中
export default {
  user: {
    welcome: '欢迎 {name}，您有 {count} 条新消息'
  }
}

// 组件中使用
{{ t('user.welcome', { name: '张三', count: 5 }) }}</code></pre>

          <h4>效果：</h4>
          <div class="result">
            <p>{{ t('user.welcome', { name: '张三', count: 5 }) || '欢迎 张三，您有 5 条新消息' }}</p>
          </div>
        </div>
      </section>

      <!-- 复数形式 -->
      <section class="example-section">
        <h3>4. 复数形式</h3>
        <div class="code-example">
          <h4>复数处理：</h4>
          <pre><code>// 语言文件中
export default {
  items: {
    count: '没有项目 | 1 个项目 | {count} 个项目'
  }
}

// 组件中使用
{{ t('items.count', 0) }}
{{ t('items.count', 1) }}
{{ t('items.count', 5) }}</code></pre>

          <h4>效果：</h4>
          <div class="result">
            <p>0个: {{ t('items.count', 0) || '没有项目' }}</p>
            <p>1个: {{ t('items.count', 1) || '1 个项目' }}</p>
            <p>5个: {{ t('items.count', 5) || '5 个项目' }}</p>
          </div>
        </div>
      </section>

      <!-- 条件渲染 -->
      <section class="example-section">
        <h3>5. 条件渲染</h3>
        <div class="code-example">
          <h4>根据语言条件渲染：</h4>
          <pre><code>&lt;template&gt;
  &lt;div v-if="locale === 'zh-CN'"&gt;
    中文特有内容
  &lt;/div&gt;
  &lt;div v-else&gt;
    English specific content
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { useI18n } from '@/locales/useI18n'
const { locale } = useI18n()
&lt;/script&gt;</code></pre>

          <h4>效果：</h4>
          <div class="result">
            <div v-if="currentLocale === 'zh-CN'">
              中文特有内容
            </div>
            <div v-else>
              English specific content
            </div>
          </div>
        </div>
      </section>

      <!-- 组件属性 -->
      <section class="example-section">
        <h3>6. 组件属性中使用</h3>
        <div class="code-example">
          <h4>在组件属性中使用：</h4>
          <pre><code>&lt;template&gt;
  &lt;a-button :title="t('components.form.submit')"&gt;
    {{ t('components.form.submit') }}
  &lt;/a-button&gt;

  &lt;a-input :placeholder="t('contact.form.fields.namePlaceholder')" /&gt;
&lt;/template&gt;</code></pre>

          <h4>效果：</h4>
          <div class="result">
            <a-button :title="t('components.form.submit')">
              {{ t('components.form.submit') }}
            </a-button>
            <br><br>
            <a-input :placeholder="t('contact.form.fields.namePlaceholder')" />
          </div>
        </div>
      </section>
    </div>

    <div class="best-practices">
      <h2>最佳实践</h2>
      <ul>
        <li>✅ 使用有意义的键名，如 <code>home.hero.title</code> 而不是 <code>title1</code></li>
        <li>✅ 保持键名结构与页面结构一致</li>
        <li>✅ 为每个页面创建独立的语言文件</li>
        <li>✅ 使用嵌套结构组织相关的翻译</li>
        <li>✅ 在组件中优先使用 <code>$t()</code> 而不是 <code>useI18n()</code></li>
        <li>✅ 为动态内容提供默认值</li>
        <li>✅ 定期检查未使用的翻译键</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@/locales/useI18n'
import { useLocale } from '@/locales/useLocale'
import LocalePicker from '@/components/LocalePicker.vue'

const { t } = useI18n()
const { getLocale } = useLocale()

// 当前语言
const currentLocale = computed(() => getLocale.value)

// 组合式API示例
const compositionApiMessage = t('components.message.success')
const compositionApiTitle = t('about.company.title')
</script>

<style scoped>
.i18n-usage-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.language-switcher {
  background: #f0f2f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.usage-examples {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.example-section {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.example-section h3 {
  margin-top: 0;
  color: #1890ff;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 10px;
}

.code-example h4 {
  color: #333;
  margin: 15px 0 10px 0;
}

.code-example pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  border-left: 4px solid #1890ff;
}

.code-example code {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.result {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  margin-top: 10px;
}

.best-practices {
  background: #e6f7ff;
  padding: 20px;
  border-radius: 8px;
  margin-top: 30px;
}

.best-practices h2 {
  color: #1890ff;
  margin-top: 0;
}

.best-practices ul {
  list-style: none;
  padding: 0;
}

.best-practices li {
  padding: 8px 0;
  border-bottom: 1px solid #d9d9d9;
}

.best-practices li:last-child {
  border-bottom: none;
}

.best-practices code {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}
</style>
