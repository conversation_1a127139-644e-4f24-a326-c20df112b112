/**
 * 获取环境变量配置
 */
export function getEnvConfig() {
  const {
    VITE_APP_TITLE,
    VITE_ADMIN_URL,
    VITE_API_BASE_URL,
    VITE_COMPANY_NAME,
    VITE_COMPANY_PHONE,
    VITE_COMPANY_EMAIL,
    VITE_COMPANY_ADDRESS,
    VITE_DEBUG,
  } = import.meta.env;

  return {
    appTitle: VITE_APP_TITLE,
    adminUrl: VITE_ADMIN_URL,
    apiBaseUrl: VITE_API_BASE_URL,
    companyInfo: {
      name: VITE_COMPANY_NAME,
      phone: VITE_COMPANY_PHONE,
      email: VITE_COMPANY_EMAIL,
      address: VITE_COMPANY_ADDRESS,
    },
    isDebug: VITE_DEBUG === 'true',
  };
}

/**
 * 是否为开发环境
 */
export function isDev(): boolean {
  return import.meta.env.DEV;
}

/**
 * 是否为生产环境
 */
export function isProd(): boolean {
  return import.meta.env.PROD;
}

/**
 * 是否为开发模式
 */
export function isDevMode(): boolean {
  return isDev();
}
