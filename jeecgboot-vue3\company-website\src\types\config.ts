export type LocaleType = 'zh-CN' | 'en';

export interface LocaleSetting {
  showPicker: boolean;
  // Current language
  locale: LocaleType;
  // default language
  fallback: LocaleType;
  // available Locales
  availableLocales: LocaleType[];
}

export interface DropMenu {
  text: string;
  event: string | number;
  disabled?: boolean;
}

// 全局类型声明
declare global {
  type Recordable<T = any> = Record<string, T>;
  type Nullable<T> = T | null;
}
