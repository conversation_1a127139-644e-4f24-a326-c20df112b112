import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home/Home.vue'),
    meta: { title: '首页' },
  },
  {
    path: '/pricing',
    name: 'Pricing',
    component: () => import('@/views/Pricing/Pricing.vue'),
    meta: { title: '价格' },
  },
  {
    path: '/services/editing',
    name: 'StandardEditing',
    component: () => import('@/views/StandardEditing/StandardEditing.vue'),
    meta: { title: '英文润色' },
  },
  {
    path: '/services/vip-editing',
    name: 'VipEditing',
    component: () => import('@/views/VipEditing/VipEditing.vue'),
    meta: { title: '科学评审编辑' },
  },
  {
    path: '/services/scientific-editing',
    name: 'ScientificEditing',
    component: () => import('@/views/ScientificEditing/ScientificEditing.vue'),
    meta: { title: '论文科学编辑' },
  },
  {
    path: '/services/translation',
    name: 'Translation',
    component: () => import('@/views/Translation/Translation.vue'),
    meta: { title: '学术论文翻译' },
  },
  {
    path: '/services/presubmission-review',
    name: 'PresubmissionReview',
    component: () => import('@/views/PresubmissionReview/PresubmissionReview.vue'),
    meta: { title: '预审评论服务' },
  },
  {
    path: '/services/journal-recommendation',
    name: 'JournalRecommendation',
    component: () => import('@/views/JournalRecommendation/JournalRecommendation.vue'),
    meta: { title: '期刊选择' },
  },
  {
    path: '/services/formatting',
    name: 'Formatting',
    component: () => import('@/views/Formatting/Formatting.vue'),
    meta: { title: '文稿格式排版' },
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/About/About.vue'),
    meta: { title: '关于我们' },
  },
  {
    path: '/about/our-team',
    name: 'OurTeam',
    component: () => import('@/views/OurTeam/OurTeam.vue'),
    meta: { title: '团队介绍' },
  },
  {
    path: '/testimonials',
    name: 'Testimonials',
    component: () => import('@/views/Testimonials/Testimonials.vue'),
    meta: { title: '客户评价' },
  },
  {
    path: '/about/areas-of-study',
    name: 'AreasOfStudy',
    component: () => import('@/views/AreasOfStudy/AreasOfStudy.vue'),
    meta: { title: '全学科领域' },
  },
  {
    path: '/contact-us',
    name: 'ContactUs',
    component: () => import('@/views/ContactUs/ContactUs.vue'),
    meta: { title: '联系我们' },
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/Login.vue'),
    meta: { title: '登录' },
  },
  {
    path: '/researcher',
    name: 'Researcher',
    component: () => import('@/views/Researcher/Researcher.vue'),
    meta: { title: '我的订单', requiresAuth: true },
  },
  {
    path: '/account-settings',
    name: 'AccountSettings',
    component: () => import('@/views/AccountSettings/AccountSettings.vue'),
    meta: { title: '账户设置', requiresAuth: true },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

router.beforeEach((to) => {
  const title = to.meta.title as string;
  if (title) {
    document.title = `${title}`;
  }
});

export default router;
