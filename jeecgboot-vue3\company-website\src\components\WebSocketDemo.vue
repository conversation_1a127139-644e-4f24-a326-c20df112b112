<template>
  <div class="websocket-demo">
    <a-card title="WebSocket 连接测试" class="demo-card">
      <!-- 连接状态 -->
      <div class="status-section">
        <div class="status-item">
          <span class="label">连接状态:</span>
          <a-tag :color="statusColor">{{ status }}</a-tag>
        </div>
        <div class="status-item">
          <span class="label">服务地址:</span>
          <span class="value">{{ wsUrl }}</span>
        </div>
        <div class="status-item">
          <span class="label">重连次数:</span>
          <span class="value">{{ managerStatus.reconnectAttempts }}</span>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="control-section">
        <a-space>
          <a-button type="primary" :disabled="isConnected" @click="connect">
            连接
          </a-button>
          <a-button danger :disabled="!isConnected" @click="disconnect">
            断开
          </a-button>
          <a-button :disabled="!isConnected" @click="reconnect">
            重连
          </a-button>
          <a-button @click="clearMessages">
            清空消息
          </a-button>
        </a-space>
      </div>

      <!-- 发送消息 -->
      <div class="send-section">
        <h4>发送消息</h4>
        <a-space direction="vertical" style="width: 100%">
          <a-select v-model:value="messageType" placeholder="选择消息类型">
            <a-select-option v-for="type in messageTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </a-select-option>
          </a-select>

          <a-textarea v-model:value="messageContent" placeholder="请输入消息内容" :rows="3" :disabled="!isConnected" />

          <a-button type="primary" :disabled="!isConnected || !messageContent.trim()" @click="sendMessage">
            发送消息
          </a-button>
        </a-space>
      </div>
    </a-card>

    <!-- 消息记录 -->
    <a-card title="消息记录" class="messages-card">
      <div class="messages-container">
        <div v-if="messages.length === 0" class="no-messages">
          暂无消息记录
        </div>
        <div v-for="message in messages" :key="message.id" class="message-item" :class="message.type">
          <div class="message-header">
            <span class="message-type">{{ getMessageTypeLabel(message.messageType) }}</span>
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          </div>
          <div class="message-content">
            {{ message.content }}
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  WebSocketManager,
  WebSocketMessageType,
  onWebSocket,
  offWebSocket,
  sendFormattedMessage,
  getWebSocketStatus,
  type WebSocketMessage
} from '@/hooks/web/useWebSocket'

// 响应式数据
const status = ref('CLOSED')
const wsUrl = ref('')
const messageType = ref<WebSocketMessageType>(WebSocketMessageType.USER_MESSAGE)
const messageContent = ref('')
const messages = ref<Array<{
  id: string
  type: 'sent' | 'received'
  messageType: WebSocketMessageType
  content: string
  timestamp: number
}>>([])

// WebSocket 管理器
const manager = WebSocketManager.getInstance()

// 消息类型选项
const messageTypes = [
  { label: '用户消息', value: WebSocketMessageType.USER_MESSAGE },
  { label: '系统通知', value: WebSocketMessageType.SYSTEM_NOTIFICATION },
  { label: '客服消息', value: WebSocketMessageType.CUSTOMER_SERVICE },
  { label: '数据更新', value: WebSocketMessageType.DATA_UPDATE },
]

// 计算属性
const isConnected = computed(() => status.value === 'OPEN')
const statusColor = computed(() => {
  switch (status.value) {
    case 'OPEN': return 'success'
    case 'CONNECTING': return 'processing'
    case 'CLOSED': return 'default'
    case 'CLOSING': return 'warning'
    default: return 'error'
  }
})

const managerStatus = computed(() => manager.getStatus())

// WebSocket 消息处理
function handleWebSocketMessage(data: any) {
  console.log('收到 WebSocket 消息:', data)

  messages.value.unshift({
    id: `received_${Date.now()}_${Math.random()}`,
    type: 'received',
    messageType: data.type,
    content: typeof data.data === 'string' ? data.data : JSON.stringify(data.data),
    timestamp: data.timestamp || Date.now()
  })

  // 显示通知
  message.info(`收到${getMessageTypeLabel(data.type)}消息`)
}

// 连接 WebSocket
function connect() {
  try {
    manager.init('/websocket/demo')
    updateStatus()
    message.success('正在连接 WebSocket...')
  } catch (error) {
    console.error('连接失败:', error)
    message.error('连接失败')
  }
}

// 断开连接
function disconnect() {
  manager.disconnect()
  updateStatus()
  message.info('已断开 WebSocket 连接')
}

// 重连
function reconnect() {
  manager.reconnect()
  updateStatus()
  message.info('正在重新连接...')
}

// 发送消息
function sendMessage() {
  if (!messageContent.value.trim()) {
    message.warning('请输入消息内容')
    return
  }

  try {
    sendFormattedMessage(messageType.value, messageContent.value)

    // 添加到消息记录
    messages.value.unshift({
      id: `sent_${Date.now()}_${Math.random()}`,
      type: 'sent',
      messageType: messageType.value,
      content: messageContent.value,
      timestamp: Date.now()
    })

    messageContent.value = ''
    message.success('消息发送成功')
  } catch (error) {
    console.error('发送消息失败:', error)
    message.error('发送消息失败')
  }
}

// 清空消息
function clearMessages() {
  messages.value = []
  message.info('消息记录已清空')
}

// 更新状态
function updateStatus() {
  status.value = getWebSocketStatus()
}

// 获取消息类型标签
function getMessageTypeLabel(type: WebSocketMessageType): string {
  const typeMap = {
    [WebSocketMessageType.USER_MESSAGE]: '用户消息',
    [WebSocketMessageType.SYSTEM_NOTIFICATION]: '系统通知',
    [WebSocketMessageType.CUSTOMER_SERVICE]: '客服消息',
    [WebSocketMessageType.DATA_UPDATE]: '数据更新',
    [WebSocketMessageType.HEARTBEAT]: '心跳检测',
  }
  return typeMap[type] || '未知类型'
}

// 格式化时间
function formatTime(timestamp: number): string {
  return new Date(timestamp).toLocaleTimeString()
}

// 定时更新状态
let statusTimer: number

onMounted(() => {
  // 添加消息监听
  onWebSocket(handleWebSocketMessage)

  // 定时更新状态
  statusTimer = window.setInterval(updateStatus, 1000)

  // 设置 WebSocket URL (用于显示)
  wsUrl.value = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/websocket/demo`
})

onUnmounted(() => {
  // 移除消息监听
  offWebSocket(handleWebSocketMessage)

  // 清除定时器
  if (statusTimer) {
    clearInterval(statusTimer)
  }
})
</script>

<style scoped>
.websocket-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-card {
  margin-bottom: 20px;
}

.status-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 6px;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  margin-right: 10px;
  min-width: 80px;
}

.value {
  color: #666;
  font-family: monospace;
}

.control-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e8e8e8;
}

.send-section h4 {
  margin-bottom: 15px;
  color: #333;
}

.messages-card {
  max-height: 600px;
}

.messages-container {
  max-height: 500px;
  overflow-y: auto;
}

.no-messages {
  text-align: center;
  color: #999;
  padding: 40px;
}

.message-item {
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 6px;
  border-left: 4px solid #ddd;
}

.message-item.sent {
  background: #e6f7ff;
  border-left-color: #1890ff;
}

.message-item.received {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-type {
  font-weight: 500;
  color: #333;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-content {
  color: #666;
  line-height: 1.5;
  word-break: break-word;
}
</style>
