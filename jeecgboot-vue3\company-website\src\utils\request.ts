import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { message } from 'ant-design-vue';
import { getEnvConfig } from './env';

// 主项目兼容的类型定义
export interface RequestOptions {
  joinParamsToUrl?: boolean;
  formatDate?: boolean;
  isTransformResponse?: boolean;
  isReturnNativeResponse?: boolean;
  joinPrefix?: boolean;
  apiUrl?: string;
  urlPrefix?: string;
  errorMessageMode?: 'none' | 'modal' | 'message' | undefined;
  successMessageMode?: 'none' | 'success' | 'error' | undefined;
  joinTime?: boolean;
  ignoreCancelToken?: boolean;
  withToken?: boolean;
}

export interface Result<T = any> {
  code: number;
  type: 'success' | 'error' | 'warning';
  message: string;
  result: T;
}

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showMessage?: boolean; // 是否显示错误消息
  showLoading?: boolean; // 是否显示加载状态
}

// 获取环境配置
const { apiBaseUrl } = getEnvConfig();

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  baseURL: apiBaseUrl || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config: any) => {
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    // 可以在这里添加 token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }

    return config;
  },
  (error: AxiosError) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response;

    // 如果是文件下载等特殊情况，直接返回
    if (response.config.responseType === 'blob') {
      return response;
    }

    // 检查业务状态码
    if (data.success === false || data.code !== 200) {
      const errorMessage = data.message || '请求失败';

      // 显示错误消息
      const config = response.config as any;
      if (config.showMessage !== false) {
        message.error(errorMessage);
      }

      return Promise.reject(new Error(errorMessage));
    }

    return data;
  },
  (error: AxiosError) => {
    let errorMessage = '网络错误';

    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 400:
          errorMessage = '请求参数错误';
          break;
        case 401:
          errorMessage = '未授权，请重新登录';
          // 可以在这里处理登录跳转
          break;
        case 403:
          errorMessage = '拒绝访问';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        default:
          errorMessage = (data as any)?.message || `请求失败 (${status})`;
      }
    } else if (error.request) {
      errorMessage = '网络连接失败';
    }

    // 显示错误消息
    const config = error.config as any;
    if (config?.showMessage !== false) {
      message.error(errorMessage);
    }

    console.error('响应拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 封装常用的请求方法
export const http = {
  get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.get(url, config);
  },

  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.post(url, data, config);
  },

  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.put(url, data, config);
  },

  delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.delete(url, config);
  },

  upload<T = any>(url: string, formData: FormData, config?: RequestConfig): Promise<ApiResponse<T>> {
    return request.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  async download(url: string, params?: any, filename?: string): Promise<void> {
    const response = await request.get(url, {
      params,
      responseType: 'blob',
    } as any);

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  },
};

// 创建兼容主项目的 defHttp 对象
export const defHttp = {
  get<T = any>(config: { url: string; params?: any }, options?: RequestOptions): Promise<T> {
    return request.get(config.url, { params: config.params, ...options } as any);
  },

  post<T = any>(config: { url: string; params?: any; data?: any }, options?: RequestOptions): Promise<T> {
    const requestData = config.params || config.data;
    return request.post(config.url, requestData, options as any);
  },

  put<T = any>(config: { url: string; params?: any; data?: any }, options?: RequestOptions): Promise<T> {
    const requestData = config.params || config.data;
    return request.put(config.url, requestData, options as any);
  },

  delete<T = any>(config: { url: string; params?: any }, options?: RequestOptions): Promise<T> {
    return request.delete(config.url, { params: config.params, ...options } as any);
  },

  request<T = any>(config: AxiosRequestConfig & { method?: string }, options?: RequestOptions): Promise<T> {
    return request.request({ ...config, ...options } as any);
  },
};

export default request;
