/**
 * Multi-language related operations
 */
import type { LocaleType } from '@/types/config';

import { i18n } from './setupI18n';
import { useLocaleStoreWithOut } from '@/store/modules/locale';
import { unref, computed } from 'vue';
import { loadLocalePool, setHtmlPageLang } from './helper';
import { importAllLocales } from '@/utils/i18n';
import { setDayjsLocale } from '@/utils/dateUtil';

interface LangModule {
  message: Recordable;
  dateLocale: Recordable;
  dateLocaleName: string;
}

function setI18nLanguage(locale: LocaleType) {
  const localeStore = useLocaleStoreWithOut();

  if (i18n.mode === 'legacy') {
    i18n.global.locale = locale;
  } else {
    (i18n.global.locale as any).value = locale;
  }
  localeStore.setLocaleInfo({ locale });
  setHtmlPageLang(locale);

  // 同步设置 dayjs 语言
  setDayjsLocale(locale);
}

export function useLocale() {
  const localeStore = useLocaleStoreWithOut();
  const getLocale = computed(() => localeStore.getLocale);
  const getShowLocalePicker = computed(() => localeStore.getShowPicker);

  const getAntdLocale = computed((): any => {
    return i18n.global.getLocaleMessage(unref(getLocale))?.antdLocale ?? {};
  });

  // Switching the language will change the locale of useI18n
  // And submit to configuration modification
  async function changeLocale(locale: LocaleType) {
    console.log('changeLocale 被调用:', locale);
    const globalI18n = i18n.global;
    const currentLocale = unref(globalI18n.locale);
    console.log('当前语言:', currentLocale, '目标语言:', locale);
    if (currentLocale === locale) {
      console.log('语言相同，无需切换');
      return locale;
    }

    if (loadLocalePool.includes(locale)) {
      setI18nLanguage(locale);
      return locale;
    }

    const langModule = ((await import(`./lang/${locale}.ts`)) as any).default as LangModule;
    if (!langModule) return;

    const { message } = langModule;

    // 自动导入页面/组件的多语言文件
    const autoMessages = importAllLocales();

    // 合并自动导入的语言包
    const mergedMessages = {
      ...message,
      ...(autoMessages[locale] || {}),
    };

    globalI18n.setLocaleMessage(locale, mergedMessages);
    loadLocalePool.push(locale);
    console.log('locale :>> ', locale);
    setI18nLanguage(locale);
    return locale;
  }

  return {
    getLocale,
    getShowLocalePicker,
    changeLocale,
    getAntdLocale,
  };
}
