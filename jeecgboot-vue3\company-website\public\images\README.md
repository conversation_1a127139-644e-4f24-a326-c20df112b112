# Public Images Directory

This directory contains public images used throughout the application.

## Directory Structure:

```
public/images/
├── qrcode.png                    # WeChat QR code
├── social/                      # Social media icons
│   ├── bilibili.png             # Bilibili icon
│   ├── zhihu.png                # Zhihu icon
│   ├── weibo.png                # Weibo icon
│   ├── xiaohongshu.png          # Xiaohongshu icon
│   └── douyin.png               # Douyin icon
└── README.md                    # This file
```

## Image Requirements:

### QR Code:
- **qrcode.png**: 120x120px, WeChat QR code for customer service

### Social Media Icons:
- **Size**: 40x40px (will be displayed in circular containers)
- **Format**: PNG with transparent background preferred
- **Quality**: High resolution for retina displays

## Usage:

These images are referenced in the AppFooter component:
- QR code: Used in the social media section
- Social icons: Used as clickable links to social media profiles

## Notes:

- All images should be optimized for web use
- Use appropriate compression to reduce file size
- Ensure images are accessible and have proper alt text
- Social media icons should match the brand guidelines of each platform
