export default {
  areasOfStudy: {
    hero: {
      title: '我们拥有400多个学科领域的2,000多位专业编辑和翻译人员',
      description:
        '我们拥有每个主要学术领域的编辑专家，他们都拥有美国重点大学的高级学位，包括哈佛、麻省理工、耶鲁和斯坦福等世界知名机构。以下是我们在每个领域可以涵盖的细分领域，如果在列表上没有看到您的学科领域，请联系我们的网站在线客服或拨打售前电话：400-6291770，您也可以发邮件至：<EMAIL>。',
      imageAlt: '学科领域图标',
    },
    disciplines: {
      clinicalMedicine: {
        title: '临床医学',
        description: '我们拥有在临床医学全领域的编辑专家，他们拥有高学历且在临床医学领域有丰富的从业或科研经验。',
        intro: '我们拥有在临床医学全领域的编辑专家，他们拥有高学历且在临床医学领域有丰富的从业或科研经验。',
      },
      lifeSciences: {
        title: '生命科学',
        description: '我们拥有在生命科学全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
        intro: '我们拥有在生命科学全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
      },
      physicalSciences: {
        title: '物理科学',
        description: '我们拥有在物理科学全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
        intro: '我们拥有在物理科学全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
      },
      mathComputer: {
        title: '数学和计算机科学',
        description: '我们拥有在数学和计算机科学全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
        intro: '我们拥有在数学和计算机科学全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
      },
      engineeringMaterials: {
        title: '工程和材料科学',
        description: '我们拥有在工程和材料科学全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
        intro: '我们拥有在工程和材料科学全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
      },
      humanitiesSocial: {
        title: '人文和社会科学',
        description: '我们拥有在人文社科全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
        intro: '我们拥有在人文社科全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
      },
      businessLaw: {
        title: '商业和法律',
        description: '我们拥有在商业和法律全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
        intro: '我们拥有在商业和法律全领域的编辑专家，他们拥有高学历且在该领域有丰富的从业或科研经验。',
      },
    },
    subspecialties: {
      // 临床医学
      generalMedicine: {
        title: '普通医学',
        items: {
          allergy: '过敏症和免疫性疾病',
          criticalCare: '重症监护和急救医学',
          dentistry: '牙科',
          dermatology: '皮肤病学',
          generalPractice: '全科医生',
          hospitalMedicine: '医院医学',
          medicalGenetics: '医学遗传学',
          nutrition: '营养与饮食学',
          obstetrics: '产科和妇科',
          ophthalmology: '眼科',
          pediatrics: '小儿科',
          physicalMedicine: '物理医学与康复',
          physiology: '生理学',
          psychiatry: '精神病学',
          sportsMedicine: '运动医学和运动学',
        },
      },
      diagnosticSpecialties: {
        title: '诊断专业',
        items: {
          audiology: '听力学和语言病理学',
          laboratoryDiagnostics: '实验室诊断学',
          nuclearMedicine: '核医学和医学影像学',
          pathology: '病理学',
          physiology: '生理学',
        },
      },
      internalMedicine: {
        title: '内科专业',
        items: {
          anesthesiology: '麻醉学与疼痛医学',
          cardiology: '心脏和心血管系统',
          endocrinology: '内分泌学和代谢学',
          gastroenterology: '胃肠病学和肝脏病学',
          geriatrics: '老年医学和老年病学',
          hematology: '血液学',
          infectiousDiseases: '传染性疾病',
          maternalFetal: '孕产妇和胎儿医学',
          neurology: '神经病学',
          nuclearMedicine: '核医学和医学影像学',
          oncology: '肿瘤学',
          orthopedics: '骨科',
          otolaryngology: '耳鼻喉科',
          pulmonology: '肺病学',
          rheumatology: '风湿病学',
          reproductiveMedicine: '性与生殖医学',
          urology: '泌尿外科和肾脏科',
          vascularMedicine: '血管医学',
        },
      },
      publicHealth: {
        title: '公共卫生',
        items: {
          epidemiology: '流行病学',
          tropicalMedicine: '热带医学',
          occupationalMedicine: '职业医学',
          healthEconomics: '卫生经济学和结果研究',
          preventiveMedicine: '预防医学',
          statisticalEpidemiology: '统计流行病学',
        },
      },
      surgery: {
        title: '手术',
        items: {
          cardiothoracicSurgery: '心胸外科',
          gastrointestinalSurgery: '胃肠道外科',
          generalSurgery: '普通外科',
          headNeckSurgery: '头颈外科',
          hepatobiliary: '肝胆及移植手术',
          neurosurgery: '神经外科',
          orthopedicSurgery: '骨科手术',
          plasticSurgery: '整形与重建外科',
          gynecologicSurgery: '妇产科手术',
        },
      },
      interdisciplinaryMedicine: {
        title: '跨学科医学',
        items: {
          anesthesiology: '麻醉学与疼痛医学',
          biomedicalEngineering: '生物医学工程',
          clinicalPharmacology: '临床药理学',
          forensicMedicine: '法医学',
          integrativeMedicine: '综合与补充医学',
          medicalEthics: '医学伦理学',
          medicalInformatics: '医学信息学',
          medicalPhysics: '医学物理学',
          nuclearMedicine: '核医学和医学影像学',
          nursing: '护理学',
          nutrition: '营养与饮食学',
          personalizedMedicine: '个人化医学',
          translationalMedicine: '转化医学',
        },
      },

      // 生命科学
      agriculturalSciences: {
        title: '农业科学',
        items: {
          agriculturalEconomics: '农业经济学',
          agriculturalEngineering: '农业工程',
          agroecology: '农业生态学',
          agronomy: '农学',
          animalScience: '动物科学',
          aquaculture: '水产养殖',
          foodScience: '食品科学',
          forestry: '林业',
          horticulture: '园艺学',
        },
      },
      biochemistry: {
        title: '生物化学',
        items: {
          analyticalBiochemistry: '分析生物化学',
          appliedBiochemistry: '应用生物化学',
          biochemicalMethods: '生物化学方法',
          biophysics: '生物物理学',
          generalBiochemistry: '普通生物化学',
          structuralBiology: '结构生物学',
        },
      },
      cellBiology: {
        title: '细胞生物学',
        items: {
          cellTissueEngineering: '细胞和组织工程',
          cellCommunication: '细胞通讯',
          cellCycle: '细胞周期',
          cellMigration: '细胞迁移',
          cellSurvival: '细胞存活',
          cellMetabolism: '细胞代谢',
          generalCellBiology: '普通细胞生物学',
          immunology: '免疫学',
          matrixBiology: '基质生物学',
          stemCells: '干细胞',
        },
      },
      environmentEcology: {
        title: '环境和生态学',
        items: {
          agroecology: '农业生态学',
          behavioralEcology: '行为生态学',
          biogeography: '生物地理学',
          conservationBiology: '保护生物学',
          ecologicalModeling: '生态建模',
          environmentalEngineering: '环境工程',
          environmentalPolicy: '环境政策',
          forestry: '林业',
          marineEcology: '海洋生态学',
          populationBiology: '种群生物学',
          renewableResources: '可再生资源',
          terrestrialEcology: '陆地生态学',
          wildlifeBiology: '野生动物生物学',
        },
      },
      evolutionDevelopment: {
        title: '进化和发育',
        items: {
          developmentalBiology: '发育生物学',
          evolutionaryBiology: '进化生物学',
          evoDevo: '进化发育生物学',
          stemCells: '干细胞',
        },
      },
      interdisciplinaryBiology: {
        title: '跨学科生物学',
        items: {
          mathematicalBiology: '数学生物学',
          bioinformatics: '生物信息学',
          biomedicalEngineering: '生物医学工程',
          biophysics: '生物物理学',
          biopolymers: '生物聚合物',
          biostatistics: '生物统计学',
          biotechnology: '生物技术',
          cancerBiology: '癌症生物学',
          chemicalBiology: '化学生物学',
          computationalBiology: '计算生物学',
          systemsBiology: '系统生物学',
        },
      },
      microbiology: {
        title: '微生物学',
        items: {
          appliedMicrobiology: '应用微生物学',
          bacteriology: '细菌学',
          generalMicrobiology: '普通微生物学',
          mycology: '真菌学',
          parasitology: '寄生虫学',
          virology: '病毒学',
        },
      },
      molecularBiology: {
        title: '分子生物学',
        items: {
          cellCommunication: '细胞通讯',
          epigenetics: '表观遗传学',
          evolutionaryGenetics: '进化遗传学',
          medicalGenetics: '医学遗传学',
          molecularBiology: '分子生物学',
          molecularEpidemiology: '分子流行病学',
          genetics: '遗传学',
          populationGenetics: '群体遗传学',
        },
      },
      neuroscience: {
        title: '神经科学',
        items: {
          cellularNeuroscience: '细胞神经科学',
          cognitiveNeuroscience: '认知神经科学',
          computationalNeuroscience: '计算神经科学',
          developmentalNeuroscience: '发育神经科学',
          diseaseNeurobiology: '疾病神经生物学',
        },
      },
      pharmacologyToxicology: {
        title: '药理学和毒理学',
        items: {
          clinicalPharmacology: '临床药理学',
          drugDelivery: '药物递送',
          drugDiscovery: '药物发现',
          pharmacodynamics: '药效学',
          pharmacokinetics: '药代动力学',
          toxicology: '毒理学',
          vaccineDevelopment: '疫苗开发',
        },
      },
      zoologyPlantBiology: {
        title: '动物学和植物生物学',
        items: {
          animalBehavior: '动物行为学',
          animalPhysiology: '动物生理学',
          botany: '植物学',
          entomology: '昆虫学',
          marineBiology: '海洋生物学',
          plantMolecularBiology: '植物分子生物学',
          plantPhysiology: '植物生理学',
          taxonomy: '分类学',
          wildlifeBiology: '野生动物生物学',
        },
      },
      veterinaryMedicine: {
        title: '兽医学',
        items: {
          internalMedicine: '内科学',
          largeAnimalMedicine: '大动物医学',
          smallAnimalMedicine: '小动物医学',
          surgery: '外科学',
          veterinaryEpidemiology: '兽医流行病学',
          zoonoses: '人畜共患病',
        },
      },

      // 物理科学
      chemistry: {
        title: '化学',
        items: {
          agrochemicals: '农用化学品',
          analyticalChemistry: '分析化学',
          bioinorganicChemistry: '生物无机化学',
          biochemistry: '生物化学',
          biopolymers: '生物聚合物',
          catalysis: '催化',
          cementChemistry: '水泥化学',
          computationalChemistry: '计算化学',
          crystallography: '晶体学',
          electrochemistry: '电化学',
          environmentalChemistry: '环境化学',
          foodChemistry: '食品化学',
          inorganicChemistry: '无机化学',
          massSpectrometry: '质谱学',
          materialChemistry: '材料化学',
          medicinalChemistry: '药物化学',
          nanoscience: '纳米科学',
          naturalProducts: '天然产物',
          nuclearChemistry: '核化学',
          organicChemistry: '有机化学',
          photonics: '光子学',
          physicalChemistry: '物理化学',
          polymerScience: '聚合物科学',
          spectroscopy: '光谱学',
          surfaceChemistry: '表面化学',
        },
      },
      physics: {
        title: '物理学',
        items: {
          atomicPhysics: '原子物理学',
          biophysics: '生物物理学',
          computationalPhysics: '计算物理学',
          electronicsPhysics: '电子物理学',
          condensedMatter: '凝聚态物理',
          highEnergyPhysics: '高能物理学',
          magnetism: '磁学',
          nanoscience: '纳米科学',
          nuclearPhysics: '核物理学',
          optics: '光学',
          plasmaPhysics: '等离子体物理学',
          scattering: '散射',
          softMatter: '软物质',
          theoreticalPhysics: '理论物理学',
          thermodynamics: '热力学',
        },
      },
      paleontology: {
        title: '古生物学',
        items: {
          paleobotany: '古植物学',
          paleoecology: '古生态学',
          paleozoology: '古动物学',
          geology: '地质学',
        },
      },
      spaceScience: {
        title: '空间科学',
        items: {
          astrobiology: '天体生物学',
          astrochemistry: '天体化学',
          astronomy: '天文学',
          astrophysics: '天体物理学',
          cometResearch: '彗星研究',
          meteorology: '气象学',
          planetaryScience: '行星科学',
          spaceExploration: '太空探索',
          theoreticalAstrophysics: '理论天体物理学',
        },
      },
      earthSciences: {
        title: '地球科学',
        items: {
          atmosphericScience: '大气科学',
          climateAnalysis: '气候分析',
          climatology: '气候学',
          economicGeology: '经济地质学',
          geochemistry: '地球化学',
          gis: '地理信息系统',
          geology: '地质学',
          geomorphology: '地貌学',
          geophysics: '地球物理学',
          glaciology: '冰川学',
          historicalGeology: '历史地质学',
          hydrology: '水文学',
          meteorology: '气象学',
          oceanography: '海洋学',
          petroleumGeology: '石油地质学',
          petrology: '岩石学',
          physicalGeography: '自然地理学',
          planetaryGeology: '行星地质学',
          seismology: '地震学',
          volcanology: '火山学',
        },
      },

      // 数学和计算机科学
      computerScience: {
        title: '计算机科学',
        items: {
          artificialIntelligence: '人工智能',
          computerArchitecture: '计算机体系结构',
          gis: '地理信息系统',
          graphicsSystems: '图形系统',
          informationRetrieval: '信息检索',
          informationTheory: '信息论',
          numericalAnalysis: '数值分析',
          robotics: '机器人学',
          medicalEngineering: '医学工程',
          systemsNetworks: '系统与网络',
          theoreticalCS: '理论计算机科学',
        },
      },
      mathematics: {
        title: '数学',
        items: {
          algebra: '代数学',
          analysis: '分析学',
          appliedMathematics: '应用数学',
          computationalBiology: '计算生物学',
          computationalMathematics: '计算数学',
          discreteMathematics: '离散数学',
          financialMathematics: '金融数学',
          geometry: '几何学',
          logic: '逻辑学',
          mathematicalPhysics: '数学物理',
          numericalAnalysis: '数值分析',
          pureMathematics: '纯数学',
          topology: '拓扑学',
        },
      },
      statistics: {
        title: '统计学',
        items: {
          appliedStatistics: '应用统计学',
          biostatistics: '生物统计学',
          decisionScience: '决策科学',
          operationsResearch: '运筹学',
          statisticalEpidemiology: '统计流行病学',
          statisticalTheory: '统计理论',
        },
      },

      // 工程和材料科学
      engineering: {
        title: '工程学',
        items: {
          acoustics: '声学',
          aeronautics: '航空学',
          agriculturalEngineering: '农业工程',
          biomedicalEngineering: '生物医学工程',
          biotechnology: '生物技术',
          cellTissueEngineering: '细胞和组织工程',
          chemicalEngineering: '化学工程',
          civilEngineering: '土木工程',
          computerArchitecture: '计算机体系结构',
          electricalEngineering: '电气工程',
          energyEngineering: '能源工程',
          environmentalEngineering: '环境工程',
          industrialEngineering: '工业工程',
          materialEngineering: '材料工程',
          mechanicalEngineering: '机械工程',
          nuclearEngineering: '核工程',
          oceanEngineering: '海洋工程',
          petroleumEngineering: '石油工程',
          robotics: '机器人学',
          softwareEngineering: '软件工程',
          systemsEngineering: '系统工程',
        },
      },
      materialScience: {
        title: '材料科学',
        items: {
          biomaterials: '生物材料',
          ceramics: '陶瓷',
          electronicMaterials: '电子材料',
          magneticMaterials: '磁性材料',
          materialChemistry: '材料化学',
          materialEngineering: '材料工程',
          materialTheory: '材料理论',
          metallurgy: '冶金学',
          nanoscience: '纳米科学',
          opticalMaterials: '光学材料',
          polymerScience: '聚合物科学',
        },
      },

      // 人文和社会科学
      education: {
        title: '教育学',
        items: {
          educationalPhilosophy: '教育哲学',
          educationalPsychology: '教育心理学',
          schoolCounseling: '学校咨询',
          specialEducation: '特殊教育',
        },
      },
      artsHumanities: {
        title: '艺术和人文学科',
        items: {
          architecture: '建筑学',
          artHistory: '艺术史',
          behavioralGeography: '行为地理学',
          classicalStudies: '古典研究',
          culturalStudies: '文化研究',
          genderStudies: '性别研究',
          history: '历史学',
          linguistics: '语言学',
          literature: '文学',
          musicology: '音乐学',
          philosophy: '哲学',
          religiousStudies: '宗教研究',
          theaterStudies: '戏剧研究',
          womensStudies: '妇女研究',
        },
      },
      communication: {
        title: '传播学',
        items: {
          culturalStudies: '文化研究',
          journalism: '新闻学',
          mediaStudies: '媒体研究',
          publicRelations: '公共关系',
          publishing: '出版学',
          scienceCommunication: '科学传播',
          technicalCommunication: '技术传播',
        },
      },
      publicPolicy: {
        title: '公共政策',
        items: {
          agriculturalPolicy: '农业政策',
          urbanManagement: '城市管理',
          environmentalPolicy: '环境政策',
          healthPolicy: '卫生政策',
          internationalRelations: '国际关系',
          otherPublicPolicy: '其他公共政策',
          publicAdministration: '公共管理',
          socialPolicy: '社会政策',
        },
      },
      informationManagement: {
        title: '信息管理',
        items: {
          informationRetrieval: '信息检索',
          libraryScience: '图书馆学',
        },
      },
      socialBehavioral: {
        title: '社会行为科学',
        items: {
          economics: '经济学',
          anthropology: '人类学',
          archaeology: '考古学',
          behavioralGeography: '行为地理学',
          behavioralEconomics: '行为经济学',
          criminology: '犯罪学',
          marketing: '市场营销',
          nursing: '护理学',
          socialWork: '社会工作',
          sociology: '社会学',
          urbanStudies: '城市研究',
        },
      },

      // 商业和法律
      business: {
        title: '商业',
        items: {
          accounting: '会计学',
          entrepreneurship: '创业学',
          finance: '金融学',
          hospitalityTourism: '酒店旅游',
          internationalBusiness: '国际商务',
          leadership: '领导学',
          management: '管理学',
          marketing: '市场营销',
          otherBusiness: '其他商业',
          publicRelations: '公共关系',
        },
      },
      law: {
        title: '法律',
        items: {
          administrativeLaw: '行政法',
          bankingLaw: '银行法',
          businessLaw: '商法',
          civilRights: '民权法',
          constitutionalLaw: '宪法',
          criminalLaw: '刑法',
          environmentalLaw: '环境法',
          familyLaw: '家庭法',
          healthLaw: '卫生法',
          immigrationLaw: '移民法',
          intellectualProperty: '知识产权',
          internationalLaw: '国际法',
          taxLaw: '税法',
        },
      },
    },
    servicesRecommendation: {
      title: '我们为各学科领域提供高质量的论文润色和原稿准备服务',
      services: {
        editing: {
          title: '英文润色',
          description:
            '我们的标准润色服务可以满足作者对语法、用词等方面的需求，确保文稿达到期刊对英语语言的要求；我们的高级润色服务提供3年内不限次数的免费重新编辑服务，为您顺利发表论文提供最全面的保障。',
          price: '价格：331.48元起',
          buttonText: '立即下单',
        },
        vipEditing: {
          title: '科学评审编辑',
          description:
            'AJE的科学评审编辑是在英文润色的基础之上，为科研学者打造的一款从文稿的结构、科学逻辑、实验过程、研究细节等方面提出全面修改建议的服务。使用该服务可以帮助科研工作者降低文稿被拒的风险，提升目标期刊的接收率。',
          price: '价格：3,216.68元起',
          buttonText: '立即下单',
        },
        scientificEditing: {
          title: '论文科学编辑',
          description:
            '论文科学编辑是由AJE中曾担任顶刊编辑的成员组成的专业小组，为您提供深度科学编辑服务。选购此服务，您将获得遵循顶刊质量标准的编辑团队提供的专家级深入编辑和策略性意见。该服务包含高级英语编辑、发展性编辑、质保编辑和一份总结报告。',
          price: '价格：10,746.14元起',
          buttonText: '立即下单',
        },
        presubmissionReview: {
          title: '预审评论服务',
          description:
            '在稿件提交给期刊前了解所在领域的专家如何评价您的文稿，为应对同行评审做好准备工作；同时获取除了语言方面更深入的评论和建议，提高文稿的接收率。我们的编辑专家将在您的文稿中添加深度评论，检查研究重点的整体表述、文稿各部分信息的逻辑性等并提出建议。',
          price: '2,070.42元',
          buttonText: '立即下单',
        },
        translation: {
          title: '学术论文翻译',
          description:
            '不同于普通的翻译服务，AJE拥有资深的翻译和编辑团队，他们拥有您所在研究领域的科研背景，能够将专业的学术翻译与高质量的英文润色相结合，让您的文稿直接达到国际期刊的发表水准。购买此服务可享受免费重新润色以及一次最多1500个新字的免费翻译。',
          price: '价格：2,095.50元起',
          buttonText: '立即下单',
        },
        journalRecommendation: {
          title: '期刊选择',
          description:
            '购买我们的期刊选择服务，您将收到根据您的研究内容、范围、影响和出版目标给出三份推荐期刊的详细报告。该报告包括每个推荐期刊的范围和影响因子，您的研究工作与其匹配程度，以及期刊如何符合您的投稿要求等这些详细信息。',
          price: '价格：967.15元',
          buttonText: '立即下单',
        },
      },
    },
    cta: {
      title: '如何与我们联系并获取更多服务信息？',
      description:
        '您可以通过邮件、网站在线客服、售前电话：400-6291770 与我们取得联系，我们在美国和中国均设有客服团队，在中国的工作时间将有北京办公室的客服为您服务；非中国工作时间将有美国客户团队为您服务（注意：非工作时间请发送邮件至***************）。',
      orderNow: '立即下单',
      quickQuote: '获取报价',
    },
  },
};
