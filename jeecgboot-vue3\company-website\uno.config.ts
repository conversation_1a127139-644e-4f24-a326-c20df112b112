import { defineConfig, presetUno, presetAttributify, presetTypography } from 'unocss';

export default defineConfig({
  presets: [presetUno(), presetAttributify(), presetTypography()],
  shortcuts: {
    // 常用的组合样式
    'btn-primary': 'bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors cursor-pointer',
    'btn-secondary': 'bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded transition-colors cursor-pointer',
    card: 'bg-white rounded-lg shadow-md p-6',
    'container-center': 'max-w-6xl mx-auto px-4',
    'flex-center': 'flex items-center justify-center',
    'text-gradient': 'bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent',
  },
  theme: {
    colors: {
      primary: '#1890ff',
      secondary: '#6c757d',
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f',
    },
    breakpoints: {
      xs: '480px',
      sm: '576px',
      md: '768px',
      lg: '992px',
      xl: '1200px',
      xxl: '1600px',
    },
  },
  rules: [
    // 自定义规则
    ['text-shadow', { 'text-shadow': '2px 2px 4px rgba(0,0,0,0.1)' }],
  ],
  // 避免与PostCSS处理的样式冲突
  safelist: ['router-link-active', 'ant-btn', 'ant-input', 'ant-select'],
});
