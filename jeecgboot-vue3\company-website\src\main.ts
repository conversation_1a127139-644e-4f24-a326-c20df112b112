import { createApp } from 'vue';
import App from './App.vue';
import router from '@/router';
import { setupStore } from '@/store';
import Antd from 'ant-design-vue';
import { setupI18n } from '@/locales/setupI18n';
import { setDayjsLocale } from '@/utils/dateUtil';

// 样式引入
import 'ant-design-vue/dist/reset.css';
import '@/styles/index.less';

async function bootstrap() {
  const app = createApp(App);

  // 配置 store
  setupStore(app);

  // 配置路由
  app.use(router as any);

  // 配置 Ant Design Vue
  app.use(Antd as any);

  // 配置国际化
  await setupI18n(app);

  // 初始化 dayjs 语言（使用默认语言）
  setDayjsLocale('zh-CN');

  app.mount('#app');
}

bootstrap();
