<template>
  <div class="preferences">
    <div class="section-header">
      <h3>{{ t('accountSettings.preferences.title') }}</h3>
      <p>{{ t('accountSettings.preferences.subtitle') }}</p>
    </div>

    <a-form
      ref="formRef"
      :model="formData"
      layout="vertical"
      @finish="handleSubmit"
    >
      <!-- 语言和地区设置 -->
      <div class="preference-section">
        <h4>语言和地区</h4>
        
        <a-row :gutter="24">
          <a-col :xs="24" :md="12">
            <a-form-item name="language" :label="t('accountSettings.preferences.language.label')">
              <a-select v-model:value="formData.language">
                <a-select-option value="zh-CN">中文（简体）</a-select-option>
                <a-select-option value="en">English</a-select-option>
              </a-select>
              <div class="form-help">{{ t('accountSettings.preferences.language.description') }}</div>
            </a-form-item>
          </a-col>
          
          <a-col :xs="24" :md="12">
            <a-form-item name="timezone" :label="t('accountSettings.preferences.timezone.label')">
              <a-select v-model:value="formData.timezone" show-search>
                <a-select-option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</a-select-option>
                <a-select-option value="America/New_York">America/New_York (UTC-5)</a-select-option>
                <a-select-option value="Europe/London">Europe/London (UTC+0)</a-select-option>
                <a-select-option value="Asia/Tokyo">Asia/Tokyo (UTC+9)</a-select-option>
              </a-select>
              <div class="form-help">{{ t('accountSettings.preferences.timezone.description') }}</div>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 通知设置 -->
      <div class="preference-section">
        <h4>{{ t('accountSettings.preferences.notifications.title') }}</h4>
        
        <div class="notification-item">
          <div class="notification-info">
            <div class="notification-title">{{ t('accountSettings.preferences.notifications.email.label') }}</div>
            <div class="notification-desc">{{ t('accountSettings.preferences.notifications.email.description') }}</div>
          </div>
          <a-switch v-model:checked="formData.emailNotifications" />
        </div>
        
        <div class="notification-item">
          <div class="notification-info">
            <div class="notification-title">{{ t('accountSettings.preferences.notifications.sms.label') }}</div>
            <div class="notification-desc">{{ t('accountSettings.preferences.notifications.sms.description') }}</div>
          </div>
          <a-switch v-model:checked="formData.smsNotifications" />
        </div>
        
        <div class="notification-item">
          <div class="notification-info">
            <div class="notification-title">{{ t('accountSettings.preferences.notifications.marketing.label') }}</div>
            <div class="notification-desc">{{ t('accountSettings.preferences.notifications.marketing.description') }}</div>
          </div>
          <a-switch v-model:checked="formData.marketingEmails" />
        </div>
      </div>

      <!-- 显示设置 -->
      <div class="preference-section">
        <h4>{{ t('accountSettings.preferences.display.title') }}</h4>
        
        <a-row :gutter="24">
          <a-col :xs="24" :md="8">
            <a-form-item name="theme" :label="t('accountSettings.preferences.display.theme.label')">
              <a-select v-model:value="formData.theme">
                <a-select-option value="light">{{ t('accountSettings.preferences.display.theme.light') }}</a-select-option>
                <a-select-option value="dark">{{ t('accountSettings.preferences.display.theme.dark') }}</a-select-option>
                <a-select-option value="auto">{{ t('accountSettings.preferences.display.theme.auto') }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          
          <a-col :xs="24" :md="8">
            <a-form-item name="currency" :label="t('accountSettings.preferences.display.currency.label')">
              <a-select v-model:value="formData.currency">
                <a-select-option value="CNY">CNY (¥)</a-select-option>
                <a-select-option value="USD">USD ($)</a-select-option>
                <a-select-option value="EUR">EUR (€)</a-select-option>
                <a-select-option value="GBP">GBP (£)</a-select-option>
              </a-select>
              <div class="form-help">{{ t('accountSettings.preferences.display.currency.description') }}</div>
            </a-form-item>
          </a-col>
          
          <a-col :xs="24" :md="8">
            <a-form-item name="dateFormat" :label="t('accountSettings.preferences.display.dateFormat.label')">
              <a-select v-model:value="formData.dateFormat">
                <a-select-option value="YYYY-MM-DD">2024-01-15</a-select-option>
                <a-select-option value="MM/DD/YYYY">01/15/2024</a-select-option>
                <a-select-option value="DD/MM/YYYY">15/01/2024</a-select-option>
                <a-select-option value="YYYY年MM月DD日">2024年01月15日</a-select-option>
              </a-select>
              <div class="form-help">{{ t('accountSettings.preferences.display.dateFormat.description') }}</div>
            </a-form-item>
          </a-col>
        </a-row>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <a-button type="primary" html-type="submit" :loading="saving">
          {{ t('accountSettings.preferences.buttons.save') }}
        </a-button>
        <a-button @click="resetToDefault">
          {{ t('accountSettings.preferences.buttons.reset') }}
        </a-button>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from '@/locales/useI18n';
import { useUserStore } from '@/store/modules/user';
import { useLocaleStore } from '@/store/modules/locale';
import type { UserPreferences } from '@/store/modules/user';

const { t } = useI18n();
const userStore = useUserStore();
const localeStore = useLocaleStore();

// 响应式数据
const formRef = ref();
const saving = ref(false);

// 表单数据
const formData = reactive<UserPreferences>({
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  emailNotifications: true,
  smsNotifications: false,
  marketingEmails: true,
  currency: 'CNY',
  dateFormat: 'YYYY-MM-DD',
  theme: 'light',
});

// 方法
const loadPreferences = () => {
  const preferences = userStore.getPreferences;
  Object.assign(formData, preferences);
};

const handleSubmit = async () => {
  try {
    saving.value = true;
    
    // 保存偏好设置
    userStore.setPreferences(formData);
    
    // 如果语言发生变化，更新语言设置
    if (formData.language !== localeStore.getLocale) {
      localeStore.setLocaleInfo({ locale: formData.language as any });
      // 刷新页面以应用新语言
      window.location.reload();
    }
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success(t('accountSettings.preferences.messages.saveSuccess'));
  } catch (error) {
    message.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

const resetToDefault = () => {
  const defaultPreferences: UserPreferences = {
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    emailNotifications: true,
    smsNotifications: false,
    marketingEmails: true,
    currency: 'CNY',
    dateFormat: 'YYYY-MM-DD',
    theme: 'light',
  };
  
  Object.assign(formData, defaultPreferences);
  message.success(t('accountSettings.preferences.messages.resetSuccess'));
};

// 生命周期
onMounted(() => {
  loadPreferences();
});
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';

.preferences {
  .section-header {
    margin-bottom: @padding-xl;
    
    h3 {
      margin: 0 0 @padding-xs;
      font-size: 20px;
      font-weight: 600;
      color: @gray-10;
    }
    
    p {
      margin: 0;
      color: @gray-7;
    }
  }
  
  .preference-section {
    margin-bottom: @padding-xl;
    padding: @padding-lg;
    background: @gray-2;
    border-radius: @border-radius-base;
    
    h4 {
      margin: 0 0 @padding-lg;
      font-size: @font-size-lg;
      font-weight: 500;
      color: @gray-10;
    }
  }
  
  .form-help {
    margin-top: 4px;
    font-size: @font-size-sm;
    color: @gray-7;
  }
  
  .notification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: @padding-md;
    margin-bottom: @padding-sm;
    background: white;
    border-radius: @border-radius-sm;
    border: 1px solid @border-color-base;
    
    .notification-info {
      flex: 1;
      
      .notification-title {
        font-weight: 500;
        color: @gray-10;
        margin-bottom: 4px;
      }
      
      .notification-desc {
        font-size: @font-size-sm;
        color: @gray-7;
      }
    }
  }
  
  .form-actions {
    display: flex;
    gap: @padding-md;
    margin-top: @padding-xl;
    padding-top: @padding-lg;
    border-top: 1px solid @border-color-base;
  }
}

@media (max-width: @screen-sm) {
  .preferences {
    .notification-item {
      flex-direction: column;
      gap: @padding-sm;
      align-items: flex-start;
    }
    
    .form-actions {
      flex-direction: column;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}
</style>
