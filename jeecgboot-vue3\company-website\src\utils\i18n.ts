/**
 * 多语言工具函数
 * 支持自动扫描和加载各个页面/模块的多语言文件
 */

// 支持的语言列表
export const LOCALE_OPTIONS = [
  { label: '中文', value: 'zh-CN' },
  { label: 'English', value: 'en' },
];

// 默认语言
export const DEFAULT_LOCALE = 'zh-CN';

// 本地存储键名
const LOCALE_KEY = 'locale';

/**
 * 获取当前语言设置
 */
export function getLocale(): string {
  if (typeof window === 'undefined') {
    return DEFAULT_LOCALE;
  }

  // 从 localStorage 获取
  const savedLocale = localStorage.getItem(LOCALE_KEY);
  if (savedLocale && LOCALE_OPTIONS.some((option) => option.value === savedLocale)) {
    return savedLocale;
  }

  // 从浏览器语言获取
  const browserLocale = navigator.language;
  const matchedLocale = LOCALE_OPTIONS.find((option) => browserLocale.startsWith(option.value.split('-')[0]));

  return matchedLocale?.value || DEFAULT_LOCALE;
}

/**
 * 设置当前语言
 */
export function setLocale(locale: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.setItem(LOCALE_KEY, locale);
  setHtmlLang(locale);
}

/**
 * 设置 HTML lang 属性
 */
export function setHtmlLang(locale: string): void {
  if (typeof document !== 'undefined') {
    document.documentElement.lang = locale;
  }
}

/**
 * 自动导入所有多语言文件
 */
export function importAllLocales(): Record<string, any> {
  const modules: Record<string, any> = {};

  try {
    // 导入 @/views 下的多语言文件
    const viewsModules = import.meta.glob('../views/**/locales/*.ts', { eager: true });
    processModules(viewsModules, modules);

    // 导入 @/components 下的多语言文件
    const componentsModules = import.meta.glob('../components/**/locales/*.ts', { eager: true });
    processModules(componentsModules, modules);

    console.log('🌐 自动导入的多语言模块:', modules);
  } catch (error) {
    console.warn('导入多语言文件时出错:', error);
  }

  return modules;
}

/**
 * 处理模块导入
 */
function processModules(requireModules: Record<string, any>, modules: Record<string, any>) {
  for (const path in requireModules) {
    const moduleContent = requireModules[path];
    if (moduleContent.default) {
      // 从路径中提取语言名称 (zh-CN, en, etc.)
      const languageName = path.replace(/(.*\/)*([^.]+)\.ts$/gi, '$2');

      if (modules[languageName]) {
        modules[languageName] = {
          ...modules[languageName],
          ...moduleContent.default,
        };
      } else {
        modules[languageName] = moduleContent.default;
      }

      console.log(`📁 导入多语言文件: ${path} -> ${languageName}`);
    }
  }
}

/**
 * 获取嵌套对象的值
 */
export function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * 设置嵌套对象的值
 */
export function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  const lastKey = keys.pop()!;
  const target = keys.reduce((current, key) => {
    if (!current[key]) {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
}

/**
 * 合并多语言对象
 */
export function mergeLocaleMessages(target: Record<string, any>, source: Record<string, any>): Record<string, any> {
  const result = { ...target };

  for (const locale in source) {
    if (result[locale]) {
      result[locale] = deepMerge(result[locale], source[locale]);
    } else {
      result[locale] = source[locale];
    }
  }

  return result;
}

/**
 * 深度合并对象
 */
function deepMerge(target: any, source: any): any {
  if (typeof target !== 'object' || typeof source !== 'object') {
    return source;
  }

  const result = { ...target };

  for (const key in source) {
    if (typeof source[key] === 'object' && typeof target[key] === 'object') {
      result[key] = deepMerge(target[key], source[key]);
    } else {
      result[key] = source[key];
    }
  }

  return result;
}

/**
 * 验证多语言文件结构
 */
export function validateLocaleStructure(locales: Record<string, any>): boolean {
  const supportedLocales = LOCALE_OPTIONS.map((option) => option.value);

  for (const locale of supportedLocales) {
    if (!locales[locale]) {
      console.warn(`缺少语言包: ${locale}`);
      return false;
    }
  }

  return true;
}

/**
 * 获取当前语言的显示名称
 */
export function getLocaleDisplayName(locale: string): string {
  const option = LOCALE_OPTIONS.find((option) => option.value === locale);
  return option?.label || locale;
}
