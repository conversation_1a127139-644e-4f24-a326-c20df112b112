<template>
  <div class="websocket-test">
    <div class="header">
      <h1>WebSocket 功能测试</h1>
      <p>测试官网项目的 WebSocket 连接和消息功能</p>
    </div>

    <div class="test-sections">
      <!-- 基础连接测试 -->
      <section class="test-section">
        <h2>🔗 基础连接测试</h2>
        <WebSocketDemo />
      </section>

      <!-- 消息处理测试 -->
      <section class="test-section">
        <h2>💬 消息处理测试</h2>
        <a-card>
          <div class="message-test">
            <div class="test-controls">
              <a-space direction="vertical" style="width: 100%">
                <div>
                  <label>系统通知测试:</label>
                  <a-button @click="sendSystemNotification" type="primary">
                    发送系统通知
                  </a-button>
                </div>
                
                <div>
                  <label>用户消息测试:</label>
                  <a-input-group compact>
                    <a-input 
                      v-model:value="userMessage" 
                      placeholder="输入用户消息"
                      style="width: 70%"
                    />
                    <a-button @click="sendUserMessage" type="primary">
                      发送
                    </a-button>
                  </a-input-group>
                </div>

                <div>
                  <label>客服消息测试:</label>
                  <a-input-group compact>
                    <a-input 
                      v-model:value="customerServiceMessage" 
                      placeholder="输入客服消息"
                      style="width: 70%"
                    />
                    <a-button @click="sendCustomerServiceMessage" type="primary">
                      发送
                    </a-button>
                  </a-input-group>
                </div>

                <div>
                  <label>数据更新测试:</label>
                  <a-button @click="sendDataUpdate" type="primary">
                    发送数据更新
                  </a-button>
                </div>
              </a-space>
            </div>

            <div class="received-messages">
              <h4>接收到的消息:</h4>
              <div class="messages-list">
                <div 
                  v-for="msg in receivedMessages" 
                  :key="msg.id"
                  class="message-item"
                  :class="msg.type"
                >
                  <div class="message-header">
                    <span class="message-type">{{ msg.typeName }}</span>
                    <span class="message-time">{{ formatTime(msg.timestamp) }}</span>
                  </div>
                  <div class="message-content">{{ msg.content }}</div>
                </div>
              </div>
            </div>
          </div>
        </a-card>
      </section>

      <!-- 服务状态监控 -->
      <section class="test-section">
        <h2>📊 服务状态监控</h2>
        <a-card>
          <div class="status-monitor">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="连接状态">
                <a-tag :color="statusColor">{{ serviceStatus.status }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="是否已连接">
                {{ serviceStatus.isConnected ? '是' : '否' }}
              </a-descriptions-item>
              <a-descriptions-item label="重连次数">
                {{ serviceStatus.reconnectAttempts }}
              </a-descriptions-item>
              <a-descriptions-item label="消息处理器数量">
                {{ messageHandlerCount }}
              </a-descriptions-item>
            </a-descriptions>

            <div class="status-actions">
              <a-space>
                <a-button @click="refreshStatus" type="default">
                  刷新状态
                </a-button>
                <a-button @click="clearReceivedMessages" type="default">
                  清空消息
                </a-button>
                <a-button @click="testReconnect" type="default">
                  测试重连
                </a-button>
              </a-space>
            </div>
          </div>
        </a-card>
      </section>

      <!-- API 使用示例 -->
      <section class="test-section">
        <h2>📖 API 使用示例</h2>
        <a-card>
          <div class="api-examples">
            <h4>基础用法:</h4>
            <pre><code>import { createWebSocketService, createMessageHandlers } from '@/services/websocket'

// 创建服务实例
const wsService = createWebSocketService()
const messageHandlers = createMessageHandlers()

// 初始化连接
await wsService.init({
  path: '/websocket',
  token: 'your-auth-token',
  autoConnect: true
})

// 处理系统通知
messageHandlers.handleSystemNotification((notification) => {
  console.log('收到系统通知:', notification)
})

// 发送用户消息
messageHandlers.sendUserMessage('Hello WebSocket!')</code></pre>

            <h4>在组件中使用:</h4>
            <pre><code>// 在 Vue 组件中
import { onMounted, onUnmounted } from 'vue'
import { createMessageHandlers } from '@/services/websocket'

export default {
  setup() {
    const messageHandlers = createMessageHandlers()
    
    const handleNotification = (notification) => {
      // 处理通知逻辑
    }
    
    onMounted(() => {
      messageHandlers.handleSystemNotification(handleNotification)
    })
    
    onUnmounted(() => {
      messageHandlers.removeMessageHandler('system_notification', handleNotification)
    })
  }
}</code></pre>
          </div>
        </a-card>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import WebSocketDemo from '@/components/WebSocketDemo.vue'
import {
  createWebSocketService,
  createMessageHandlers,
  websocketUtils,
  type SystemNotification,
  type UserMessage,
  type CustomerServiceMessage,
  type DataUpdate
} from '@/services/websocket'

// 响应式数据
const userMessage = ref('')
const customerServiceMessage = ref('')
const receivedMessages = ref<Array<{
  id: string
  type: string
  typeName: string
  content: string
  timestamp: number
}>>([])

// 服务实例
const wsService = createWebSocketService()
const messageHandlers = createMessageHandlers()

// 计算属性
const serviceStatus = computed(() => wsService.getStatus())
const statusColor = computed(() => {
  switch (serviceStatus.value.status) {
    case 'OPEN': return 'success'
    case 'CONNECTING': return 'processing'
    case 'CLOSED': return 'default'
    case 'CLOSING': return 'warning'
    default: return 'error'
  }
})

const messageHandlerCount = ref(0)

// 消息处理函数
function handleSystemNotification(notification: SystemNotification) {
  addReceivedMessage('system', '系统通知', notification.content, notification.timestamp)
  message.info(`收到系统通知: ${notification.title}`)
}

function handleUserMessage(msg: UserMessage) {
  addReceivedMessage('user', '用户消息', msg.content, msg.timestamp)
  message.info('收到用户消息')
}

function handleCustomerService(msg: CustomerServiceMessage) {
  addReceivedMessage('service', '客服消息', msg.content, msg.timestamp)
  message.info('收到客服消息')
}

function handleDataUpdate(update: DataUpdate) {
  const content = `数据类型: ${update.type}, 数据: ${JSON.stringify(update.data)}`
  addReceivedMessage('data', '数据更新', content, update.timestamp)
  message.info('收到数据更新')
}

// 添加接收到的消息
function addReceivedMessage(type: string, typeName: string, content: string, timestamp: number) {
  receivedMessages.value.unshift({
    id: `${type}_${Date.now()}_${Math.random()}`,
    type,
    typeName,
    content,
    timestamp
  })
}

// 发送消息函数
function sendSystemNotification() {
  const notification: SystemNotification = {
    id: `notif_${Date.now()}`,
    title: '测试通知',
    content: '这是一个测试系统通知消息',
    type: 'info',
    timestamp: Date.now()
  }
  
  messageHandlers.sendUserMessage(JSON.stringify(notification))
  message.success('系统通知已发送')
}

function sendUserMessage() {
  if (!userMessage.value.trim()) {
    message.warning('请输入用户消息内容')
    return
  }
  
  messageHandlers.sendUserMessage(userMessage.value)
  userMessage.value = ''
  message.success('用户消息已发送')
}

function sendCustomerServiceMessage() {
  if (!customerServiceMessage.value.trim()) {
    message.warning('请输入客服消息内容')
    return
  }
  
  const sessionId = websocketUtils.generateSessionId()
  messageHandlers.sendCustomerServiceMessage(customerServiceMessage.value, sessionId)
  customerServiceMessage.value = ''
  message.success('客服消息已发送')
}

function sendDataUpdate() {
  const update: DataUpdate = {
    type: 'test_data',
    data: {
      id: Date.now(),
      value: Math.random(),
      status: 'updated'
    },
    timestamp: Date.now(),
    version: '1.0.0'
  }
  
  messageHandlers.sendUserMessage(JSON.stringify(update))
  message.success('数据更新已发送')
}

// 工具函数
function formatTime(timestamp: number): string {
  return websocketUtils.formatMessageTime(timestamp)
}

function refreshStatus() {
  messageHandlerCount.value = Math.floor(Math.random() * 10) // 模拟数据
  message.info('状态已刷新')
}

function clearReceivedMessages() {
  receivedMessages.value = []
  message.info('消息记录已清空')
}

function testReconnect() {
  wsService.reconnect()
  message.info('正在测试重连...')
}

// 生命周期
onMounted(async () => {
  // 初始化 WebSocket 服务
  await wsService.init({
    path: '/websocket/test',
    autoConnect: true
  })
  
  // 注册消息处理器
  messageHandlers.handleSystemNotification(handleSystemNotification)
  messageHandlers.handleUserMessage(handleUserMessage)
  messageHandlers.handleCustomerService(handleCustomerService)
  messageHandlers.handleDataUpdate(handleDataUpdate)
  
  message.success('WebSocket 测试页面已初始化')
})

onUnmounted(() => {
  // 清理资源
  wsService.destroy()
})
</script>

<style scoped>
.websocket-test {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e8e8e8;
}

.header h1 {
  color: #1890ff;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section h2 {
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.message-test {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.test-controls label {
  display: inline-block;
  width: 120px;
  font-weight: 500;
  margin-bottom: 10px;
}

.test-controls > div {
  margin-bottom: 15px;
}

.received-messages h4 {
  margin-bottom: 15px;
  color: #333;
}

.messages-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 10px;
}

.message-item {
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
}

.message-item.system {
  background: #fff7e6;
  border-left-color: #fa8c16;
}

.message-item.user {
  background: #e6f7ff;
  border-left-color: #1890ff;
}

.message-item.service {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.message-item.data {
  background: #f9f0ff;
  border-left-color: #722ed1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.message-type {
  font-weight: 500;
  color: #333;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-content {
  color: #666;
  line-height: 1.4;
}

.status-monitor {
  margin-bottom: 20px;
}

.status-actions {
  margin-top: 20px;
  text-align: center;
}

.api-examples h4 {
  color: #333;
  margin: 20px 0 10px 0;
}

.api-examples pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  margin-bottom: 20px;
}

.api-examples code {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>
