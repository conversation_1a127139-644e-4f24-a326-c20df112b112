<template>
  <div class="account-settings-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 主要内容区域 -->
    <div class="account-settings-container">
      <div class="container-center">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>{{ t('accountSettings.title') }}</h1>
          <div class="user-info-brief">
            <a-avatar :size="40" :src="userInfo?.avatar">
              {{ userInfo?.firstName?.charAt(0) }}{{ userInfo?.lastName?.charAt(0) }}
            </a-avatar>
            <div class="user-details">
              <div class="user-name">{{ fullName }}</div>
              <div class="user-email">{{ userInfo?.email }}</div>
            </div>
          </div>
        </div>

        <!-- 标签页内容 -->
        <div class="settings-content">
          <a-tabs v-model:activeKey="activeTab" type="card" size="large" @change="handleTabChange">
            <a-tab-pane key="userInfo" :tab="t('accountSettings.tabs.userInfo')">
              <UserInfo />
            </a-tab-pane>

            <a-tab-pane key="workInfo" :tab="t('accountSettings.tabs.workInfo')">
              <WorkInfo />
            </a-tab-pane>

            <a-tab-pane key="changePassword" :tab="t('accountSettings.tabs.changePassword')">
              <ChangePassword />
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from '@/locales/useI18n';
import { useUserStore } from '@/store/modules/user';
import AppHeader from '@/components/AppHeader.vue';
import AppFooter from '@/components/AppFooter.vue';
import UserInfo from './components/UserInfo.vue';
import WorkInfo from './components/WorkInfo.vue';
import ChangePassword from './components/ChangePassword.vue';

// 导入国际化文件 - 暂时注释掉，可以通过其他方式注册
// import zhCN from './locales/zh-CN';
// import en from './locales/en';

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();

// 注册国际化消息 - 这里可以通过其他方式注册，暂时注释掉
// mergeLocaleMessage('zh-CN', zhCN);
// mergeLocaleMessage('en', en);

// 响应式数据
const activeTab = ref('userInfo');

// 计算属性
const userInfo = computed(() => userStore.getUserInfo);
const fullName = computed(() => userStore.getFullName);

// 生命周期
onMounted(() => {
  // 检查用户是否已登录
  if (!userStore.getIsLoggedIn) {
    router.push('/login');
    return;
  }

  // 从URL参数获取活动标签页
  const urlParams = new URLSearchParams(window.location.search);
  const tab = urlParams.get('tab');
  if (tab && ['userInfo', 'workInfo', 'changePassword'].includes(tab)) {
    activeTab.value = tab;
  }
});

// 监听标签页变化，更新URL
const handleTabChange = (key: string) => {
  const url = new URL(window.location.href);
  url.searchParams.set('tab', key);
  window.history.replaceState({}, '', url.toString());
};
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';

.account-settings-page {
  min-height: 100vh;
  background-color: @gray-2;
}

.account-settings-container {
  padding: @padding-xl 0;
  min-height: calc(100vh - 140px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: @padding-xl;
  padding: @padding-lg;
  background: white;
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-card;

  h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: @gray-10;
  }

  .user-info-brief {
    display: flex;
    align-items: center;
    gap: @padding-md;

    .user-details {
      .user-name {
        font-size: @font-size-lg;
        font-weight: 500;
        color: @gray-10;
        margin-bottom: 2px;
      }

      .user-email {
        font-size: @font-size-sm;
        color: @gray-7;
      }
    }
  }
}

.settings-content {
  background: white;
  border-radius: @border-radius-lg;
  box-shadow: @box-shadow-card;
  overflow: hidden;

  :deep(.ant-tabs) {
    .ant-tabs-nav {
      margin: 0;
      padding: 0 @padding-lg;
      background: @gray-2;

      .ant-tabs-tab {
        border-radius: @border-radius-base @border-radius-base 0 0;
        border: none;
        background: transparent;

        &.ant-tabs-tab-active {
          background: white;

          .ant-tabs-tab-btn {
            color: @primary-color;
            font-weight: 500;
          }
        }

        .ant-tabs-tab-btn {
          padding: @padding-md @padding-lg;
          font-size: @font-size-base;
        }
      }
    }

    .ant-tabs-content-holder {
      padding: @padding-xl;
    }
  }
}

// 响应式设计
@media (max-width: @screen-md) {
  .page-header {
    flex-direction: column;
    gap: @padding-md;
    text-align: center;

    h1 {
      font-size: 24px;
    }
  }

  .settings-content {
    :deep(.ant-tabs) {
      .ant-tabs-nav {
        padding: 0 @padding-sm;
      }

      .ant-tabs-content-holder {
        padding: @padding-lg;
      }
    }
  }
}

@media (max-width: @screen-sm) {
  .account-settings-container {
    padding: @padding-lg 0;
  }

  .page-header {
    margin: 0 @padding-md @padding-lg;
    padding: @padding-md;

    h1 {
      font-size: 20px;
    }
  }

  .settings-content {
    margin: 0 @padding-md;

    :deep(.ant-tabs) {
      .ant-tabs-nav {
        padding: 0 @padding-xs;

        .ant-tabs-tab {
          .ant-tabs-tab-btn {
            padding: @padding-sm;
            font-size: @font-size-sm;
          }
        }
      }

      .ant-tabs-content-holder {
        padding: @padding-md;
      }
    }
  }
}
</style>
