/**
 * dayjs 配置和语言切换工具
 */

import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import 'dayjs/locale/en'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import duration from 'dayjs/plugin/duration'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import weekYear from 'dayjs/plugin/weekYear'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import localeData from 'dayjs/plugin/localeData'

// 注册插件
dayjs.extend(relativeTime)
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(duration)
dayjs.extend(customParseFormat)
dayjs.extend(weekOfYear)
dayjs.extend(weekYear)
dayjs.extend(advancedFormat)
dayjs.extend(localeData)

/**
 * 语言代码映射
 */
const LOCALE_MAP = {
  'zh-CN': 'zh-cn',
  'en-US': 'en',
  'en': 'en',
  'zh': 'zh-cn'
} as const

/**
 * 设置 dayjs 语言
 * @param locale 语言代码 (如: 'zh-CN', 'en-US')
 */
export function setDayjsLocale(locale: string) {
  const dayjsLocale = LOCALE_MAP[locale as keyof typeof LOCALE_MAP] || 'en'
  
  try {
    dayjs.locale(dayjsLocale)
    console.log(`[dayjs] 语言已切换到: ${dayjsLocale}`)
  } catch (error) {
    console.warn(`[dayjs] 语言切换失败: ${dayjsLocale}`, error)
    // 回退到英文
    dayjs.locale('en')
  }
}

/**
 * 获取当前 dayjs 语言
 */
export function getDayjsLocale(): string {
  return dayjs.locale()
}

/**
 * 格式化时间的工具函数
 */
export const dayjsUtils = {
  /**
   * 格式化为标准日期时间
   */
  formatDateTime(date?: dayjs.ConfigType, format = 'YYYY-MM-DD HH:mm:ss'): string {
    return dayjs(date).format(format)
  },

  /**
   * 格式化为日期
   */
  formatDate(date?: dayjs.ConfigType, format = 'YYYY-MM-DD'): string {
    return dayjs(date).format(format)
  },

  /**
   * 格式化为时间
   */
  formatTime(date?: dayjs.ConfigType, format = 'HH:mm:ss'): string {
    return dayjs(date).format(format)
  },

  /**
   * 相对时间 (如: 2小时前)
   */
  fromNow(date?: dayjs.ConfigType): string {
    return dayjs(date).fromNow()
  },

  /**
   * 到现在的相对时间 (如: 2小时后)
   */
  toNow(date?: dayjs.ConfigType): string {
    return dayjs(date).toNow()
  },

  /**
   * 获取本地化的星期几
   */
  getWeekday(date?: dayjs.ConfigType): string {
    return dayjs(date).format('dddd')
  },

  /**
   * 获取本地化的月份
   */
  getMonth(date?: dayjs.ConfigType): string {
    return dayjs(date).format('MMMM')
  },

  /**
   * 判断是否为今天
   */
  isToday(date?: dayjs.ConfigType): boolean {
    return dayjs(date).isSame(dayjs(), 'day')
  },

  /**
   * 判断是否为昨天
   */
  isYesterday(date?: dayjs.ConfigType): boolean {
    return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
  },

  /**
   * 判断是否为明天
   */
  isTomorrow(date?: dayjs.ConfigType): boolean {
    return dayjs(date).isSame(dayjs().add(1, 'day'), 'day')
  },

  /**
   * 智能时间显示 (今天显示时间，昨天显示"昨天"，其他显示日期)
   */
  smartFormat(date?: dayjs.ConfigType): string {
    const target = dayjs(date)
    const now = dayjs()

    if (target.isSame(now, 'day')) {
      return target.format('HH:mm')
    } else if (target.isSame(now.subtract(1, 'day'), 'day')) {
      return dayjs.locale() === 'zh-cn' ? '昨天' : 'Yesterday'
    } else if (target.isSame(now.add(1, 'day'), 'day')) {
      return dayjs.locale() === 'zh-cn' ? '明天' : 'Tomorrow'
    } else if (target.isSame(now, 'year')) {
      return target.format('MM-DD')
    } else {
      return target.format('YYYY-MM-DD')
    }
  }
}

/**
 * 响应式的当前时间
 */
export function useCurrentTime(format = 'YYYY-MM-DD HH:mm:ss', interval = 1000) {
  const { ref, onMounted, onUnmounted } = require('vue')
  
  const currentTime = ref(dayjs().format(format))
  let timer: NodeJS.Timeout | null = null

  const updateTime = () => {
    currentTime.value = dayjs().format(format)
  }

  onMounted(() => {
    timer = setInterval(updateTime, interval)
  })

  onUnmounted(() => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  })

  return {
    currentTime,
    updateTime
  }
}

// 默认导出配置好的 dayjs
export default dayjs
