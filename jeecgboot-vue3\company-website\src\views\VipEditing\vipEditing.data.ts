// 统计数据
export const statsData = [
  { key: 'journals' },
  { key: 'subjects' },
  { key: 'manuscripts' }
]

// 服务内容特点
export const serviceContentFeatures = [
  { key: 'identify', icon: 'CheckOutlined' },
  { key: 'understand', icon: 'EditOutlined' },
  { key: 'master', icon: 'TeamOutlined' }
]

// 权威机构logo
export const authorityLogos = [
  {
    name: 'Nature',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/c28de959-7b32-4bb6-9f69-cae90ffbfcd9_nature+logo.svg'
  },
  {
    name: '<PERSON>',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/1fe603a7-c964-4548-b1f6-a560d0b17de9_springer-logo.png?auto=format,compress&w=60'
  },
  {
    name: '英国皇家学会',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2gPIqRLdaBsb2_%E8%8B%B1%E5%9B%BD%E7%9A%87%E5%AE%B6%E5%AD%A6%E4%BC%9A-2.png?auto=format,compress&w=60'
  },
  {
    name: '地震协会',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/aBB4EvIqRLdaBsck_%E5%9C%B0%E9%9C%87%E5%8D%8F%E4%BC%9A-4.png?auto=format,compress&w=60'
  },
  {
    name: 'PNAS',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/aA8lOfIqRLdaBp-u_PNAS-1.png?auto=format,compress&w=60'
  },
  {
    name: '美国物理学会',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/aA8kvvIqRLdaBp-h_%E7%BE%8E%E5%9B%BD%E7%89%A9%E7%90%86%E5%AD%A6%E4%BC%9A%EF%BC%88APS%EF%BC%89.png?auto=format,compress&w=60'
  },
  {
    name: '美国微生物学会',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/aA8kvfIqRLdaBp-f_%E7%BE%8E%E5%9B%BD%E5%BE%AE%E7%94%9F%E7%89%A9%E5%AD%A6%E4%BC%9A%EF%BC%88ASM%EF%BC%89.png?auto=format,compress&w=60'
  },
  {
    name: '美国化学学会',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/aA8ku_IqRLdaBp-d_%E7%BE%8E%E5%9B%BD%E5%8C%96%E5%AD%A6%E5%AD%A6%E4%BC%9A%EF%BC%88ACS%EF%BC%89.png?auto=format,compress&w=60'
  },
  {
    name: '美国癌症研究协会',
    src: 'https://www.aje.cn/externalimages/aje-cms-production/aA8qdPIqRLdaBqAq_%E7%BE%8E%E5%9B%BD%E7%99%8C%E7%97%87%E7%A0%94%E7%A9%B6%E5%8D%8F%E4%BC%9A-1.png?auto=format,compress&w=60'
  }
]

// 服务特点
export const serviceFeatures = [
  {
    key: 'acceptance',
    icon: 'https://www.aje.cn/externalimages/aje-cms-production/8e0fb00f-465e-46a1-836b-c94149de30fa_funding-services-group-128.png?auto=format,compress&w=60'
  },
  {
    key: 'writing',
    icon: 'https://www.aje.cn/externalimages/aje-cms-production/c55d7c3c-85fb-4de3-9990-5b6607194058_english-editing-64.png?auto=format,compress&w=60'
  },
  {
    key: 'competitive',
    icon: 'https://www.aje.cn/externalimages/aje-cms-production/bda5bda7-a076-4aa3-94ae-824ed978b35c_american-journal-experts-128.png?auto=format,compress&w=60'
  }
]

// 编辑团队
export const editorTeam = [
  {
    key: 'tias',
    name: 'Tias Paul',
    avatar: 'https://www.aje.cn/externalimages/aje-cms-production/ZiIkpfPdc1huKoSI_TiasPaul.png?auto=format%2Ccompress&rect=0%2C41%2C351%2C351&w=60'
  },
  {
    key: 'jaime',
    name: 'Jaime Watson',
    avatar: 'https://www.aje.cn/externalimages/aje-cms-production/ZiIga_Pdc1huKoQ0_JaimeWatson.png?auto=format%2Ccompress&rect=4%2C0%2C263%2C263&w=60'
  }
]

// 质量保证特点
export const qualityFeatures = [
  { key: 'satisfaction', icon: 'CheckCircleOutlined' },
  { key: 'unlimited', icon: 'SafetyOutlined' },
  { key: 'ethics', icon: 'MailOutlined' }
]

// 相关服务
export const relatedServices = [
  {
    key: 'journal',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBGAfPdc1huKlra_journal-recommendation.png?auto=format,compress&w=60',
    link: 'https://www.aje.cn/services/journal-recommendation'
  },
  {
    key: 'grammar',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/ZiBF__Pdc1huKlrW_grammar-check.png?auto=format,compress&w=60',
    link: 'https://www.aje.cn/grammar-check'
  },
  {
    key: 'translation',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/a7abaac4-2097-44aa-9cdb-eebeb56440a3_translation-128.png?auto=compress,format&w=60',
    link: 'https://www.aje.cn/services/translation'
  }
]

// FAQ数据
export const faqData = [
  { key: 'editor' },
  { key: 'revision' },
  { key: 'comments' },
  { key: 'certificate' },
  { key: 'latex' }
]
