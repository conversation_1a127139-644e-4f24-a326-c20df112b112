/**
 * WebSocket 类型修复验证测试
 * 用于验证 @vueuse/core 的 UseWebSocketReturn 类型是否正确导入
 */

import { useWebSocket, type UseWebSocketReturn } from '@vueuse/core'

// 测试类型定义
function testWebSocketTypes() {
  // 测试 UseWebSocketReturn 类型
  const wsResult: UseWebSocketReturn<any> = useWebSocket('ws://localhost:8080/test', {
    autoReconnect: false,
    heartbeat: false,
  })

  // 验证返回的属性
  console.log('WebSocket 状态:', wsResult.status.value)
  console.log('WebSocket 数据:', wsResult.data.value)
  
  // 验证方法
  if (wsResult.send) {
    wsResult.send('test message')
  }
  
  if (wsResult.close) {
    wsResult.close()
  }
  
  if (wsResult.open) {
    wsResult.open()
  }

  return wsResult
}

// 导出测试函数
export { testWebSocketTypes }

// 类型验证通过的标记
export const WEBSOCKET_TYPES_FIXED = true

console.log('✅ WebSocket 类型修复验证通过！UseWebSocketReturn 类型可以正常使用。')
