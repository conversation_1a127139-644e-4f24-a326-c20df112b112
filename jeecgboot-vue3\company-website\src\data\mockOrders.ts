// 模拟订单数据
export interface Order {
  id: string
  title: string
  service: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  submitDate: string
  price: number
}

// 服务类型映射
export const serviceTypes = {
  standard: 'researcher.orders.serviceTypes.standard',
  premium: 'researcher.orders.serviceTypes.premium', 
  scientific: 'researcher.orders.serviceTypes.scientific',
  editorial: 'researcher.orders.serviceTypes.editorial',
  translation: 'researcher.orders.serviceTypes.translation'
}

// 论文标题数据
const paperTitles = [
  'Effects of Climate Change on Marine Ecosystems',
  'Novel Approaches to Cancer Treatment Using Immunotherapy',
  'Machine Learning Applications in Financial Risk Assessment',
  'Sustainable Energy Solutions for Urban Development',
  'Genetic Factors in Alzheimer\'s Disease Progression',
  'Quantum Computing Algorithms for Optimization Problems',
  'Impact of Social Media on Mental Health in Adolescents',
  'Advanced Materials for Renewable Energy Storage',
  'Artificial Intelligence in Medical Diagnosis',
  'Biodiversity Conservation in Tropical Rainforests',
  'Blockchain Technology in Supply Chain Management',
  'Neuroplasticity and Learning in Adult Brains',
  'Smart City Infrastructure and IoT Integration',
  'Gene Therapy Approaches for Rare Diseases',
  'Sustainable Agriculture Practices and Food Security',
  'Deep Learning for Natural Language Processing',
  'Environmental Impact of Microplastics in Oceans',
  'Personalized Medicine and Genomic Analysis',
  'Robotics Applications in Healthcare Systems',
  'Climate Adaptation Strategies for Coastal Cities'
]

// 生成模拟订单数据
export const generateMockOrders = (): Order[] => {
  const services = Object.keys(serviceTypes)
  const statuses: Order['status'][] = ['pending', 'processing', 'completed', 'cancelled']
  
  const mockOrders: Order[] = []
  
  for (let i = 1; i <= 20; i++) {
    // 生成随机日期（过去一年内）
    const randomDate = new Date()
    randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 365))
    
    // 随机选择服务类型
    const randomService = services[Math.floor(Math.random() * services.length)]
    
    // 随机选择状态
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
    
    // 根据服务类型设置价格范围
    let priceRange = { min: 500, max: 1500 }
    switch (randomService) {
      case 'standard':
        priceRange = { min: 300, max: 800 }
        break
      case 'premium':
        priceRange = { min: 800, max: 2000 }
        break
      case 'scientific':
        priceRange = { min: 2000, max: 4000 }
        break
      case 'editorial':
        priceRange = { min: 5000, max: 12000 }
        break
      case 'translation':
        priceRange = { min: 1500, max: 3500 }
        break
    }
    
    const randomPrice = Math.floor(
      Math.random() * (priceRange.max - priceRange.min) + priceRange.min
    )
    
    mockOrders.push({
      id: `AJE${String(i).padStart(6, '0')}`,
      title: paperTitles[i - 1],
      service: serviceTypes[randomService as keyof typeof serviceTypes],
      status: randomStatus,
      submitDate: randomDate.toISOString(),
      price: randomPrice
    })
  }
  
  // 按提交日期降序排序（最新的在前）
  return mockOrders.sort((a, b) => 
    new Date(b.submitDate).getTime() - new Date(a.submitDate).getTime()
  )
}

// 导出默认的模拟数据
export const mockOrders = generateMockOrders()
