<template>
  <div class="contact-us-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('contactUs.hero.title') }}</h1>
          <p class="hero-description">{{ t('contactUs.hero.description') }}</p>
        </div>
        <div class="hero-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/9707f487-306c-4be9-a370-49867b117fd2_2018_02_15_aje_event-7787-2.jpg?auto=compress,format&w=60"
            :alt="t('contactUs.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 联系方式区域 -->
    <section class="contact-methods-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('contactUs.methods.title') }}</h2>
        </div>
        <div class="contact-methods-grid">
          <div class="contact-method-card">
            <div class="method-icon">
              <PhoneOutlined />
            </div>
            <h3>{{ t('contactUs.methods.phone.title') }}</h3>
            <p>{{ t('contactUs.methods.phone.description') }}</p>
            <div class="contact-info">
              <a href="tel:400-6291770" class="contact-link">400-6291770</a>
              <span class="contact-time">{{ t('contactUs.methods.phone.time') }}</span>
            </div>
          </div>

          <div class="contact-method-card">
            <div class="method-icon">
              <MessageOutlined />
            </div>
            <h3>{{ t('contactUs.methods.online.title') }}</h3>
            <p>{{ t('contactUs.methods.online.description') }}</p>
            <div class="contact-info">
              <span class="contact-time">{{ t('contactUs.methods.online.time') }}</span>
            </div>
          </div>

          <div class="contact-method-card">
            <div class="method-icon">
              <MailOutlined />
            </div>
            <h3>{{ t('contactUs.methods.email.title') }}</h3>
            <p>{{ t('contactUs.methods.email.description') }}</p>
            <div class="contact-info">
              <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
            </div>
          </div>

          <div class="contact-method-card">
            <div class="method-icon">
              <TeamOutlined />
            </div>
            <h3>{{ t('contactUs.methods.institution.title') }}</h3>
            <p>{{ t('contactUs.methods.institution.description') }}</p>
            <div class="contact-info">
              <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
            </div>
          </div>

          <div class="contact-method-card">
            <div class="method-icon">
              <WechatOutlined />
            </div>
            <h3>{{ t('contactUs.methods.wechat.title') }}</h3>
            <p>{{ t('contactUs.methods.wechat.description') }}</p>
            <div class="wechat-qr">
              <img
                src="https://www.aje.cn/externalimages/aje-cms-production/Z9u6dTiBA97GisMW_wechat-contact--3092%C3%972061-new-8.png?auto=format,compress&w=60"
                alt="WeChat QR Code" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 团体优惠区域 -->
    <section class="group-discount-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('contactUs.groupDiscount.title') }}</h2>
          <p>{{ t('contactUs.groupDiscount.description') }}</p>
        </div>
        <div class="group-discount-content">
          <div class="discount-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/eb65a8d8-cf37-4c2e-a62f-eb2cd84c6013_2018_02_15_aje_event-7683+%281%29.jpg?auto=compress,format&w=60"
              :alt="t('contactUs.groupDiscount.imageAlt')" />
          </div>
          <div class="discount-info">
            <div class="discount-feature">
              <h4>{{ t('contactUs.groupDiscount.features.discount.title') }}</h4>
              <p>{{ t('contactUs.groupDiscount.features.discount.description') }}</p>
            </div>
            <div class="discount-feature">
              <h4>{{ t('contactUs.groupDiscount.features.services.title') }}</h4>
              <p>{{ t('contactUs.groupDiscount.features.services.description') }}</p>
            </div>
            <a-button type="primary" size="large" @click="handleLearnMore">
              {{ t('contactUs.groupDiscount.learnMore') }}
            </a-button>
          </div>
        </div>
      </div>
    </section>

    <!-- 以作者为中心区域 -->
    <section class="author-centered-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('contactUs.authorCentered.title') }}</h2>
        </div>
        <div class="author-centered-content">
          <div class="feature-card">
            <h4>{{ t('contactUs.authorCentered.mission.title') }}</h4>
            <p>{{ t('contactUs.authorCentered.mission.description') }}</p>
          </div>
          <div class="feature-card">
            <h4>{{ t('contactUs.authorCentered.service.title') }}</h4>
            <p>{{ t('contactUs.authorCentered.service.description') }}</p>
          </div>
        </div>
        <div class="quote-section">
          <a-button type="primary" size="large" @click="handleGetQuote">
            {{ t('contactUs.authorCentered.getQuote') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 公司地址区域 -->
    <section class="address-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('contactUs.address.title') }}</h2>
        </div>
        <div class="address-grid">
          <div class="address-card">
            <h3>{{ t('contactUs.address.us.title') }}</h3>
            <p class="address-text">{{ t('contactUs.address.us.address') }}</p>
            <div class="contact-info">
              <span class="label">{{ t('contactUs.address.us.emailLabel') }}:</span>
              <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
            </div>
          </div>
          <div class="address-card">
            <h3>{{ t('contactUs.address.china.title') }}</h3>
            <p class="company-name">{{ t('contactUs.address.china.companyName') }}</p>
            <p class="address-text">{{ t('contactUs.address.china.address') }}</p>
          </div>
        </div>
        <div class="map-section">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/ZhztmDjCgu4jz0Ln_contactus-2024.png?auto=format,compress&w=60"
            :alt="t('contactUs.address.mapAlt')" class="map-image" />
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { PhoneOutlined, MessageOutlined, MailOutlined, TeamOutlined, WechatOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 方法
const handleLearnMore = () => {
  // 跳转到外部链接或现有页面
  window.open('https://www.aje.cn/group-discounts/', '_blank')
}

const handleGetQuote = () => {
  router.push('/pricing')
}

// 生命周期
onMounted(() => {
  console.log('ContactUs 页面已加载')
})
</script>

<style scoped>
.contact-us-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  opacity: 0.9;
}

.hero-image {
  flex-shrink: 0;
}

.hero-image img {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 联系方式区域 */
.contact-methods-section {
  padding: 80px 0;
  background: white;
}

/* 区域标题 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 联系方式网格 */
.contact-methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.contact-method-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 32px 24px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid transparent;
}

.contact-method-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.method-icon {
  font-size: 48px;
  color: #667eea;
  margin-bottom: 16px;
}

.contact-method-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.contact-method-card p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 16px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-link {
  color: #667eea;
  font-weight: 600;
  text-decoration: none;
  font-size: 16px;
}

.contact-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

.contact-time {
  font-size: 14px;
  color: #4a5568;
  font-style: italic;
}

.wechat-qr img {
  width: 120px;
  height: 120px;
  border-radius: 8px;
}

/* 团体优惠区域 */
.group-discount-section {
  padding: 80px 0;
  background: #f8fafc;
}

.group-discount-content {
  display: flex;
  align-items: center;
  gap: 60px;
}

.discount-image {
  flex-shrink: 0;
}

.discount-image img {
  width: 300px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

.discount-info {
  flex: 1;
}

.discount-feature {
  margin-bottom: 24px;
}

.discount-feature h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.discount-feature p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

/* 以作者为中心区域 */
.author-centered-section {
  padding: 80px 0;
  background: white;
}

.author-centered-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
  margin-bottom: 40px;
}

.feature-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 32px 24px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-card h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.feature-card p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.6;
}

.quote-section {
  text-align: center;
}

/* 公司地址区域 */
.address-section {
  padding: 80px 0;
  background: #f8fafc;
}

.address-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
  margin-bottom: 40px;
}

.address-card {
  background: white;
  border-radius: 12px;
  padding: 32px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.address-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.address-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.company-name {
  font-size: 16px;
  font-weight: 500;
  color: #667eea;
  margin-bottom: 8px;
}

.address-text {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 16px;
}

.contact-info .label {
  font-weight: 500;
  color: #1a202c;
}

.map-section {
  text-align: center;
}

.map-image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .hero-image img {
    width: 150px;
    height: 150px;
  }

  .contact-methods-grid {
    grid-template-columns: 1fr;
  }

  .group-discount-content {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .discount-image img {
    width: 250px;
    height: 150px;
  }

  .author-centered-content {
    grid-template-columns: 1fr;
  }

  .address-grid {
    grid-template-columns: 1fr;
  }
}
</style>
