import {
  TranslationOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  SafetyCertificateOutlined,
  EditOutlined,
  TeamOutlined,
} from '@ant-design/icons-vue';

// 权威认证logos
export const authorityLogos = [
  {
    key: 'nature',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/68ddb796-b40c-424e-9bf3-0f3d553492ae_nature-logo.png?auto=compress,format&w=60',
  },
  {
    key: 'springer',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/1fe603a7-c964-4548-b1f6-a560d0b17de9_springer-logo.png?auto=compress,format&w=60',
  },
  {
    key: 'cambridge',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/e6bfe80e-a362-49b8-a232-b70beb07f4e4_cambridge-logo.png?auto=compress,format&w=60',
  },
  {
    key: 'ieee',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/b30d61ac-7ae0-477a-8ef7-f259cf7dab46_ieee.png?auto=compress,format&w=60',
  },
  {
    key: 'agu',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2dPIqRLdaBsbs_AGU-2.png?auto=format,compress&w=60',
  },
  {
    key: 'cope',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/767dd4b2-ccfd-43a1-bd2c-19a743198264_ethics-cope.png?auto=compress,format&w=60',
  },
  {
    key: 'royal',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/aBB2gPIqRLdaBsb2_%E8%8B%B1%E5%9B%BD%E7%9A%87%E5%AE%B6%E5%AD%A6%E4%BC%9A-2.png?auto=format,compress&w=60',
  },
  {
    key: 'seismological',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/aBB4EvIqRLdaBsck_%E5%9C%B0%E9%9C%87%E5%8D%8F%E4%BC%9A-4.png?auto=format,compress&w=60',
  },
  {
    key: 'aacr',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/aBB2f_IqRLdaBsb1_%E7%BE%8E%E5%9B%BD%E7%99%8C%E7%97%87%E7%A0%94%E7%A9%B6%E5%8D%8F%E4%BC%9A-2.png?auto=format,compress&w=60',
  },
  {
    key: 'pnas',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2fPIqRLdaBsby_PNAS-2.png?auto=format,compress&w=60',
  },
  {
    key: 'asme',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2evIqRLdaBsbx_ASME-2.png?auto=format,compress&w=60',
  },
  {
    key: 'asm',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2efIqRLdaBsbw_ASM-2.png?auto=format,compress&w=60',
  },
  {
    key: 'aps',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2ePIqRLdaBsbu_APS-2.png?auto=format,compress&w=60',
  },
  {
    key: 'ams',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2d_IqRLdaBsbt_AMS-2.png?auto=format,compress&w=60',
  },
  {
    key: 'acs',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2cvIqRLdaBsbr_ACS-2.png?auto=format,compress&w=60',
  },
  {
    key: 'aace',
    image: 'https://www.aje.cn/externalimages/aje-cms-production/aBB2cfIqRLdaBsbq_AACE-2.png?auto=format,compress&w=60',
  },
  {
    key: 'jpgu',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/aBB2fvIqRLdaBsb0_%E6%97%A5%E6%9C%AC%E5%9C%B0%E7%90%83%E7%A7%91%E5%AD%A6-2.png?auto=format,compress&w=60',
  },
];

// 服务特点
export const serviceFeatures = [
  { key: 'professional', icon: TranslationOutlined },
  { key: 'noEditing', icon: EditOutlined },
  { key: 'freeReEdit', icon: SafetyOutlined },
];

// 流程步骤
export const processSteps = ['finalDraft', 'expertMatch', 'delivery'];

// 质量保证特点
export const qualityFeatures = [
  { key: 'certificate', icon: SafetyCertificateOutlined },
  { key: 'unlimitedReEdit', icon: EditOutlined },
  { key: 'qualityGuarantee', icon: CheckCircleOutlined },
];

// 客户评价
export const testimonials = [
  {
    key: 'guan',
    name: 'Dr. Guan Gui',
    avatar: null,
  },
  {
    key: 'zhao',
    name: 'Dr. Xiaofan Zhao',
    avatar: null,
  },
  {
    key: 'li',
    name: 'x. li',
    avatar: 'https://www.aje.cn/externalimages/aje-cms-production/Z_TTGXdAxsiBwbY1_25.png?auto=format,compress&rect=0,0,200,200&w=60',
  },
  {
    key: 'xin',
    name: 'P. Xin',
    avatar: 'https://www.aje.cn/externalimages/aje-cms-production/Z_OFEHdAxsiBwYWU_17.png?auto=format,compress&rect=0,0,200,200&w=60',
  },
  {
    key: 'tang',
    name: 'Y. Tang',
    avatar: 'https://www.aje.cn/externalimages/aje-cms-production/Z_TTHndAxsiBwbY6_29.png?auto=format,compress&rect=0,0,200,200&w=60',
  },
  {
    key: 'zhang',
    name: 'T. zhang',
    avatar: 'https://www.aje.cn/externalimages/aje-cms-production/Z_OFBXdAxsiBwYWI_7.png?auto=format,compress&rect=0,0,200,200&w=60',
  },
];

// 相关服务
export const relatedServices = [
  {
    key: 'editing',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/866c7b29-68db-43a3-8ce5-a0bc0be98570_english-editing-64.png?auto=compress,format&w=60',
    link: 'https://www.aje.cn/services/editing',
  },
  {
    key: 'scientific',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/2ff95af9-af02-46a1-bffa-2b446df899b8_scientific-editing-128.png?auto=format,compress&w=60',
    link: 'https://www.aje.cn/services/scientific-editing',
  },
  {
    key: 'presubmission',
    image:
      'https://www.aje.cn/externalimages/aje-cms-production/fc927461-7932-461b-9983-cf1441cc454e_presubmission+review+illustration.png?auto=compress,format&w=60',
    link: 'https://www.aje.cn/services/presubmission-review',
  },
];

// FAQ数据
export const faqData = [{ key: 'sameField' }, { key: 'whyReferences' }, { key: 'difference' }, { key: 'reEditTime' }, { key: 'contact' }];
