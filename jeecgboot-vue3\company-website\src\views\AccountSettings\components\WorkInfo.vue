<template>
  <div class="work-info">
    <div class="section-header">
      <h3>{{ t('accountSettings.workInfo.title') }}</h3>
      <p>{{ t('accountSettings.workInfo.subtitle') }}</p>
    </div>

    <a-form
      ref="formRef"
      :model="formData"
      layout="vertical"
      @finish="handleSubmit"
    >
      <a-row :gutter="24">
        <!-- 所属机构 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="organization" :label="t('accountSettings.workInfo.form.organization.label')">
            <a-input
              v-model:value="formData.organization"
              :placeholder="t('accountSettings.workInfo.form.organization.placeholder')"
            />
          </a-form-item>
        </a-col>

        <!-- 部门/院系 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="department" :label="t('accountSettings.workInfo.form.department.label')">
            <a-input
              v-model:value="formData.department"
              :placeholder="t('accountSettings.workInfo.form.department.placeholder')"
            />
          </a-form-item>
        </a-col>

        <!-- 职位/头衔 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="title" :label="t('accountSettings.workInfo.form.title.label')">
            <a-input
              v-model:value="formData.title"
              :placeholder="t('accountSettings.workInfo.form.title.placeholder')"
            />
          </a-form-item>
        </a-col>

        <!-- 学术级别 -->
        <a-col :xs="24" :sm="12">
          <a-form-item name="academicLevel" :label="t('accountSettings.workInfo.form.academicLevel.label')">
            <a-select
              v-model:value="formData.academicLevel"
              :placeholder="t('accountSettings.workInfo.form.academicLevel.placeholder')"
            >
              <a-select-option value="undergraduate">
                {{ t('accountSettings.workInfo.form.academicLevel.options.undergraduate') }}
              </a-select-option>
              <a-select-option value="graduate">
                {{ t('accountSettings.workInfo.form.academicLevel.options.graduate') }}
              </a-select-option>
              <a-select-option value="phd">
                {{ t('accountSettings.workInfo.form.academicLevel.options.phd') }}
              </a-select-option>
              <a-select-option value="postdoc">
                {{ t('accountSettings.workInfo.form.academicLevel.options.postdoc') }}
              </a-select-option>
              <a-select-option value="professor">
                {{ t('accountSettings.workInfo.form.academicLevel.options.professor') }}
              </a-select-option>
              <a-select-option value="researcher">
                {{ t('accountSettings.workInfo.form.academicLevel.options.researcher') }}
              </a-select-option>
              <a-select-option value="other">
                {{ t('accountSettings.workInfo.form.academicLevel.options.other') }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 研究领域 -->
        <a-col :span="24">
          <a-form-item name="researchField" :label="t('accountSettings.workInfo.form.researchField.label')">
            <a-textarea
              v-model:value="formData.researchField"
              :placeholder="t('accountSettings.workInfo.form.researchField.placeholder')"
              :rows="3"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <a-button type="primary" html-type="submit" :loading="saving">
          {{ t('accountSettings.workInfo.buttons.save') }}
        </a-button>
        <a-button @click="resetForm">
          {{ t('accountSettings.workInfo.buttons.cancel') }}
        </a-button>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from '@/locales/useI18n';
import { useUserStore } from '@/store/modules/user';
import type { UserInfo } from '@/store/modules/user';

const { t } = useI18n();
const userStore = useUserStore();

// 响应式数据
const formRef = ref();
const saving = ref(false);

// 表单数据
const formData = reactive({
  organization: '',
  department: '',
  title: '',
  academicLevel: '',
  researchField: '',
});

// 方法
const loadWorkData = () => {
  const userInfo = userStore.getUserInfo;
  if (userInfo) {
    Object.assign(formData, {
      organization: userInfo.organization || '',
      department: userInfo.department || '',
      title: userInfo.title || '',
      academicLevel: userInfo.academicLevel || '',
      researchField: userInfo.researchField || '',
    });
  }
};

const handleSubmit = async () => {
  try {
    saving.value = true;
    
    // 更新用户工作信息
    userStore.updateUserInfo(formData as Partial<UserInfo>);
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success(t('accountSettings.workInfo.messages.saveSuccess'));
  } catch (error) {
    message.error(t('accountSettings.workInfo.messages.saveError'));
  } finally {
    saving.value = false;
  }
};

const resetForm = () => {
  loadWorkData();
  formRef.value?.clearValidate();
};

// 生命周期
onMounted(() => {
  loadWorkData();
});
</script>

<style lang="less" scoped>
@import '@/styles/variables.less';

.work-info {
  .section-header {
    margin-bottom: @padding-xl;
    
    h3 {
      margin: 0 0 @padding-xs;
      font-size: 20px;
      font-weight: 600;
      color: @gray-10;
    }
    
    p {
      margin: 0;
      color: @gray-7;
    }
  }
  
  .form-actions {
    display: flex;
    gap: @padding-md;
    margin-top: @padding-xl;
    padding-top: @padding-lg;
    border-top: 1px solid @border-color-base;
  }
}

@media (max-width: @screen-sm) {
  .work-info {
    .form-actions {
      flex-direction: column;
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}
</style>
