export default {
  login: {
    brand: {
      title: 'Welcome to AJE',
      description: "The world's leading academic editing and publishing services platform, helping your research achieve successful publication",
      features: {
        professional: '2000+ Professional Editors',
        trusted: 'Trusted by 3000+ Journals',
        global: 'Serving 192 Countries Worldwide',
      },
    },
    form: {
      title: 'Sign in to your account',
      subtitle: 'Please enter your login information to access your account',
      username: {
        label: 'Username/Email',
        placeholder: 'Enter your username or email address',
      },
      password: {
        label: 'Password',
        placeholder: 'Enter your password',
      },
      remember: 'Remember me',
      forgotPassword: 'Forgot password?',
      loginButton: 'Sign In',
      or: 'Or',
    },
    social: {
      wechat: 'Sign in with WeChat',
    },
    register: {
      title: 'Register',
      text: "Don't have an account?",
      link: 'Sign up now',
      button: 'Agree and Register',
      firstName: 'First Name',
      lastName: 'Last Name',
      firstNamePlaceholder: 'Enter first name',
      lastNamePlaceholder: 'Enter last name',
      terms: {
        text: 'By registering, you agree to our',
        service: 'Terms and Conditions',
        and: 'and',
        privacy: 'Privacy Policy',
        dot: '.',
      },
    },
    reset: {
      title: 'Password Reset',
      subtitle: 'Enter your email address and we will send you instructions to reset your password.',
      button: 'Request Password Reset Link',
    },
    switch: {
      toRegister: "I don't have an account",
      toLogin: 'I already have an account',
      backToLogin: 'Back to Login',
    },
    help: {
      support: 'Help Center',
      contact: 'Contact Us',
    },
    validation: {
      username: {
        required: 'Please enter username or email',
        min: 'Username must be at least 3 characters',
      },
      password: {
        required: 'Please enter password',
        min: 'Password must be at least 6 characters',
      },
      email: {
        required: 'Please enter email address',
        format: 'Please enter a valid email address',
      },
      firstName: {
        required: 'Please enter first name',
      },
      lastName: {
        required: 'Please enter last name',
      },
    },
    messages: {
      success: 'Login successful!',
      error: 'Login failed, please check your username and password',
      validationError: 'Please check your input information',
      registerSuccess: 'Registration successful! Please login to your account',
      registerError: 'Registration failed, please try again later',
      resetSuccess: 'Password reset link has been sent to your email',
      resetError: 'Password reset failed, please try again later',
    },
    legal: {
      google: 'This site is protected by Google reCAPTCHA and the Google Privacy Policy applies.',
    },
  },
};
