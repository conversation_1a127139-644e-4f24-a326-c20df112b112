export default {
  components: {
    nav: {
      scientificEditing: 'Scientific Editing',
      languageEditing: 'Language Editing',
      resources: 'Resources',
      pricing: 'Pricing',
      about: 'About Us',
      contact: 'Contact Us',
      admin: 'Admin Panel',
      account: 'Account',
      login: 'Login',
      register: 'Register',
      loginRegister: 'Login / Register',
      placeOrder: 'Place Order',

      // Account related
      myAccount: 'My Account',
      myOrders: 'My Orders',
      myBills: 'My Bills',
      creditsAndPoints: 'Credits and Points',
      referrals: 'Referrals',
      settings: 'Settings',
      help: 'Help',
      logout: 'Logout',
      logoutSuccess: 'Logout successful',

      // Scientific Editing
      scientificReview: 'Scientific Review',
      paperScientificEditing: 'Paper Scientific Editing',
      presubmissionReview: 'Presubmission Review',
      journalSelection: 'Journal Selection',

      // Language Editing
      standardEditing: 'Standard Editing',
      premiumEditing: 'Premium Editing',
      translation: 'Translation',
      formatting: 'Formatting',

      // Resources
      authorResources: 'Author Resources',
      quickQuote: 'Quick Quote',
      academicEthics: 'Academic Ethics',
      qualityAssurance: 'Quality Assurance',
      membership: 'Membership',

      // About Us
      aboutUs: 'About Us',
      ourTeam: 'Our Team',
      testimonials: 'Testimonials',
      areasOfStudy: 'Areas of Study',
    },
    header: {
      about: 'About Us',
      products: 'Products & Services',
      contact: 'Contact Us',
      language: 'Language',
      admin: 'Admin Panel',
      hotline: 'Hotline',
      workTime: 'Mon-Fri 9:00-18:00',
      contactMessage: 'Thank you for your interest! We will contact you soon.',
      loginMessage: 'Please login to your account',
      registerMessage: 'Welcome to register as our user!',
      placeOrderMessage: 'Redirecting to order page...',
    },
    footer: {
      professionalServices: {
        title: 'Professional Services',
        editing: 'English Editing',
        scientificReview: 'Scientific Review Editing',
        scientificEditing: 'Scientific Editing',
        translation: 'Academic Translation',
        rubriq: 'Rubriq (formerly Curie)',
      },
      valueAddedServices: {
        title: 'Value-Added Services',
        presubmissionReview: 'Presubmission Review',
        journalRecommendation: 'Journal Recommendation',
        formatting: 'Manuscript Formatting',
        figures: 'Figure Processing',
        researchPromotion: 'Research Promotion',
      },
      resourceCenter: {
        title: 'Resource Center',
        authorResources: 'Author Resources',
        quickQuote: 'Quick Quote',
        academicEthics: 'Academic Ethics',
        qualityGuarantee: 'Quality Guarantee',
        memberCenter: 'Member Center',
      },
      aboutUs: {
        title: 'About Us',
        contactUs: 'Contact Us',
        aboutAJE: 'About AJE',
        ajeTeam: 'AJE Team',
        customerReviews: 'Customer Reviews',
        areasOfStudy: 'Areas of Study',
      },
      copyright: {
        allRightsReserved: 'All Rights Reserved',
        privacyPolicy: 'Privacy Policy',
        termsOfService: 'Terms of Service',
      },
      filing: {
        icp: 'ICP Filing: 20014092',
        publicSecurity: 'Public Security Filing: 11010802031576',
      },
    },
    navigation: {
      back: 'Back',
      next: 'Next',
      previous: 'Previous',
      more: 'More',
      viewAll: 'View All',
      collapse: 'Collapse',
      expand: 'Expand',
    },
    form: {
      required: 'Required',
      optional: 'Optional',
      placeholder: 'Please enter',
      select: 'Please select',
      upload: 'Upload File',
      clear: 'Clear',
      reset: 'Reset',
      submit: 'Submit',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save',
      edit: 'Edit',
      delete: 'Delete',
      search: 'Search',
      filter: 'Filter',
      sort: 'Sort',
    },
    loading: {
      text: 'Loading...',
      error: 'Loading failed',
      retry: 'Retry',
      noData: 'No data available',
      networkError: 'Network error',
    },
    pagination: {
      total: 'Total {total} items',
      page: 'Page {current}',
      pageSize: '{size} items per page',
      prev: 'Previous',
      next: 'Next',
      first: 'First',
      last: 'Last',
      goto: 'Go to',
    },
    modal: {
      title: 'Notice',
      confirm: 'Confirm',
      cancel: 'Cancel',
      close: 'Close',
      ok: 'OK',
      yes: 'Yes',
      no: 'No',
    },
    message: {
      success: 'Operation successful',
      error: 'Operation failed',
      warning: 'Warning',
      info: 'Information',
      loading: 'Processing...',
      copied: 'Copied to clipboard',
    },
    lazyImage: {
      loading: 'Image loading...',
      error: 'Image loading failed',
      retry: 'Reload',
    },
    contactForm: {
      title: 'Contact Form',
      subtitle: 'Please fill in your contact information',
      success: 'Submitted successfully',
      error: 'Submission failed',
    },
  },
};
