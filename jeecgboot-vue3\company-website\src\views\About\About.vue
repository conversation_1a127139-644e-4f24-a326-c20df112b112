<template>
  <div class="about-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('about.hero.title') }}</h1>
          <p class="hero-description">{{ t('about.hero.description') }}</p>
        </div>
        <div class="hero-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/43214b20-1eed-49b5-8fe3-c1fe34964e14_group-photo-2.jpg?auto=compress,format&w=60"
            :alt="t('about.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 发展历程 -->
    <section class="history-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('about.history.title') }}</h2>
        </div>
        <div class="history-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/ZtF6dUaF0TcGJkAj_AJE-history-6-zip-2500x1000-.png?auto=format,compress&w=60"
            :alt="t('about.history.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 润色流程 -->
    <section class="process-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('about.process.title') }}</h2>
          <p>{{ t('about.process.description') }}</p>
        </div>
        <div class="process-content">
          <div class="process-steps">
            <div class="step-item" v-for="(step, index) in processSteps" :key="step">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h4>{{ t(`about.process.steps.${step}.title`) }}</h4>
                <p>{{ t(`about.process.steps.${step}.description`) }}</p>
              </div>
            </div>
          </div>
          <div class="process-image">
            <img
              src="https://www.aje.cn/externalimages/aje-cms-production/46c78511-5a79-43a2-a8ba-4cd132094365_2018_02_15_aje_event-7977.jpg?auto=compress,format&rect=614,0,2800,2800&w=60"
              :alt="t('about.process.imageAlt')" />
          </div>
        </div>
      </div>
    </section>

    <!-- 道德规范 -->
    <section class="ethics-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('about.ethics.title') }}</h2>
          <p>{{ t('about.ethics.description') }}</p>
        </div>
        <div class="ethics-content">
          <div class="ethics-info">
            <div class="ethics-icon">
              <SafetyCertificateOutlined />
            </div>
            <div class="ethics-text">
              <h4>{{ t('about.ethics.copeTitle') }}</h4>
              <p>{{ t('about.ethics.copeDescription') }}</p>
              <a-button type="link" @click="handleEthicsClick">
                {{ t('about.ethics.learnMore') }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 合作伙伴 -->
    <section class="partners-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('about.partners.title') }}</h2>
        </div>
        <div class="partners-logos">
          <div class="logo-grid">
            <div class="logo-item" v-for="partner in partners" :key="partner.key">
              <img :src="partner.image" :alt="partner.key" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="statistics-section">
      <div class="services-content">
        <div class="statistics-grid">
          <div class="stat-item" v-for="stat in statistics" :key="stat.key">
            <div class="stat-icon">
              <component :is="stat.icon" />
            </div>
            <div class="stat-content">
              <h3>{{ t(`about.statistics.${stat.key}.number`) }}</h3>
              <p>{{ t(`about.statistics.${stat.key}.label`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 -->
    <section class="contact-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('about.contact.title') }}</h2>
          <p>{{ t('about.contact.description') }}</p>
        </div>
        <div class="contact-actions">
          <a-button type="primary" size="large" @click="handleAllServices">
            {{ t('about.contact.allServices') }}
          </a-button>
          <a-button size="large" @click="handleQuickQuote">
            {{ t('about.contact.quickQuote') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { SafetyCertificateOutlined, FileTextOutlined, TeamOutlined, GlobalOutlined, TrophyOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  processSteps,
  partners,
  statistics
} from './about.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 方法
const handleEthicsClick = () => {
  window.open('https://www.aje.cn/about/ethics/', '_blank')
}

const handleAllServices = () => {
  window.open('https://www.aje.cn/services/', '_blank')
}

const handleQuickQuote = () => {
  router.push('/pricing')
}

// 生命周期
onMounted(() => {
  console.log('About 页面已加载')
})
</script>

<style scoped>
.about-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  opacity: 0.9;
}

.hero-image {
  flex-shrink: 0;
}

.hero-image img {
  width: 300px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 通用区域样式 */
.history-section,
.process-section,
.ethics-section,
.partners-section,
.statistics-section,
.contact-section {
  padding: 80px 0;
}

.history-section {
  background: white;
}

.process-section {
  background: #f8fafc;
}

.ethics-section {
  background: white;
}

.partners-section {
  background: #f8fafc;
}

.statistics-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.contact-section {
  background: white;
}

/* 区域标题 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.statistics-section .section-header h2 {
  color: white;
}

.section-header p {
  font-size: 18px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 发展历程区域 */
.history-image {
  text-align: center;
}

.history-image img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 润色流程区域 */
.process-content {
  display: flex;
  gap: 60px;
  align-items: flex-start;
}

.process-steps {
  flex: 1;
}

.step-item {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
}

.step-number {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
}

.step-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.step-content p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
}

.process-image {
  flex-shrink: 0;
}

.process-image img {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  object-fit: cover;
}

/* 道德规范区域 */
.ethics-content {
  max-width: 800px;
  margin: 0 auto;
}

.ethics-info {
  display: flex;
  gap: 24px;
  align-items: flex-start;
  padding: 32px;
  background: #f7fafc;
  border-radius: 12px;
}

.ethics-icon {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.ethics-text h4 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.ethics-text p {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 16px;
}

/* 合作伙伴区域 */
.partners-logos {
  max-width: 1000px;
  margin: 0 auto;
}

.logo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 32px;
  align-items: center;
}

.logo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: white;
  border-radius: 12px;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.logo-item:hover {
  transform: translateY(-4px);
}

.logo-item img {
  max-width: 120px;
  max-height: 60px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.logo-item:hover img {
  filter: grayscale(0%);
}

/* 统计数据区域 */
.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.stat-item {
  text-align: center;
  padding: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-4px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  font-size: 24px;
  color: white;
}

.stat-content h3 {
  font-size: 36px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.stat-content p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 联系我们区域 */
.contact-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .hero-image img {
    width: 250px;
    height: 150px;
  }

  .process-content {
    flex-direction: column;
    gap: 40px;
  }

  .process-image img {
    width: 150px;
    height: 150px;
    margin: 0 auto;
  }

  .ethics-info {
    flex-direction: column;
    text-align: center;
  }

  .logo-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
  }

  .statistics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
  }

  .contact-actions {
    flex-direction: column;
    align-items: center;
  }

  .contact-actions .ant-btn {
    width: 200px;
  }
}
</style>
