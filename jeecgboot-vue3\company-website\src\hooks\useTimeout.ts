import { ref, watch } from 'vue';
import type { Ref } from 'vue';

export interface UseTimeoutFnOptions {
  /**
   * Start the timer immediate after calling this function
   *
   * @default true
   */
  immediate?: boolean;
}

/**
 * Wrapper for `setTimeout` with controls.
 */
export function useTimeoutFn(
  cb: (...args: unknown[]) => any,
  interval: number,
  options: UseTimeoutFnOptions = {}
) {
  const { immediate = true } = options;

  const isPending = ref(false);

  let timer: ReturnType<typeof setTimeout> | null = null;

  function clear() {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  }

  function stop() {
    isPending.value = false;
    clear();
  }

  function start(...args: unknown[]) {
    clear();
    isPending.value = true;
    timer = setTimeout(() => {
      isPending.value = false;
      timer = null;
      cb(...args);
    }, interval);
  }

  if (immediate) {
    isPending.value = true;
    start();
  }

  return {
    isPending: isPending as Readonly<Ref<boolean>>,
    start,
    stop,
  };
}
