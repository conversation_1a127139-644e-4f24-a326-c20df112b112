import { defHttp } from '@/utils/request';

enum Api {
  // 首页相关接口
  bannerList = '/home/<USER>/list',
  companyStats = '/home/<USER>',
  featuredProducts = '/home/<USER>/featured',
  latestNews = '/home/<USER>/latest',
  testimonials = '/home/<USER>',
  partners = '/home/<USER>',
}

/**
 * 获取轮播图列表
 */
export const getBannerList = () => {
  return defHttp.get({ url: Api.bannerList });
};

/**
 * 获取公司统计数据
 */
export const getCompanyStats = () => {
  return defHttp.get({ url: Api.companyStats });
};

/**
 * 获取精选产品
 * @param params 查询参数
 */
export const getFeaturedProducts = (params?: { limit?: number }) => {
  return defHttp.get({ url: Api.featuredProducts, params });
};

/**
 * 获取最新资讯
 * @param params 查询参数
 */
export const getLatestNews = (params?: { limit?: number }) => {
  return defHttp.get({ url: Api.latestNews, params });
};

/**
 * 获取客户评价
 */
export const getTestimonials = () => {
  return defHttp.get({ url: Api.testimonials });
};

/**
 * 获取合作伙伴
 */
export const getPartners = () => {
  return defHttp.get({ url: Api.partners });
};
