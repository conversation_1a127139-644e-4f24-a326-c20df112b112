<template>
  <a-affix :offset-top="0">
    <a-layout-header class="app-header">
      <div class="header-content">
        <!-- Logo 区域 -->
        <div class="logo-section">
          <router-link to="/" class="logo-link">
            <a-avatar :size="48" style="background-color: #1890ff">
              <template #icon>
                <HomeOutlined />
              </template>
            </a-avatar>
            <span class="logo-text">{{ companyInfo?.name || '公司名称' }}</span>
          </router-link>
        </div>

        <!-- 主导航菜单 -->
        <div class="nav-section">
          <a-menu mode="horizontal" :selected-keys="[currentRoute]" class="nav-menu" theme="light">
            <!-- 科学编辑下拉菜单 -->
            <a-sub-menu key="scientific-editing" :title="t('components.nav.scientificEditing')">
              <template #icon>
                <AppstoreOutlined />
              </template>
              <a-menu-item key="scientific-review">
                <router-link to="/services/vip-editing">
                  {{ t('components.nav.scientificReview') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="paper-scientific-editing">
                <router-link to="/services/scientific-editing">
                  {{ t('components.nav.paperScientificEditing') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="presubmission-review">
                <router-link to="/services/presubmission-review">
                  {{ t('components.nav.presubmissionReview') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="journal-selection">
                <router-link to="/services/journal-recommendation">
                  {{ t('components.nav.journalSelection') }}
                </router-link>
              </a-menu-item>
            </a-sub-menu>

            <!-- 语言润色下拉菜单 -->
            <a-sub-menu key="language-editing" :title="t('components.nav.languageEditing')">
              <template #icon>
                <BulbOutlined />
              </template>
              <a-menu-item key="standard-editing">
                <router-link to="/services/editing">
                  {{ t('components.nav.standardEditing') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="premium-editing">
                <router-link to="/services/editing">
                  {{ t('components.nav.premiumEditing') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="translation">
                <router-link to="/services/translation">
                  {{ t('components.nav.translation') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="formatting">
                <router-link to="/services/formatting">
                  {{ t('components.nav.formatting') }}
                </router-link>
              </a-menu-item>
            </a-sub-menu>

            <!-- 资源中心下拉菜单 -->
            <!-- <a-sub-menu key="resources" :title="t('components.nav.resources')">
              <template #icon>
                <BulbOutlined />
              </template>
              <a-menu-item key="author-resources">
                <router-link to="/resources/author-resources">
                  {{ t('components.nav.authorResources') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="quick-quote">
                <router-link to="/resources/quick-quote">
                  {{ t('components.nav.quickQuote') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="academic-ethics">
                <router-link to="/resources/academic-ethics">
                  {{ t('components.nav.academicEthics') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="quality-assurance">
                <router-link to="/resources/quality-assurance">
                  {{ t('components.nav.qualityAssurance') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="membership">
                <router-link to="/resources/membership">
                  {{ t('components.nav.membership') }}
                </router-link>
              </a-menu-item>
            </a-sub-menu> -->

            <!-- 价格 -->
            <a-menu-item key="pricing">
              <router-link to="/pricing">
                <DollarOutlined />
                {{ t('components.nav.pricing') }}
              </router-link>
            </a-menu-item>

            <!-- 关于我们下拉菜单 -->
            <a-sub-menu key="about" :title="t('components.nav.about')">
              <template #icon>
                <TeamOutlined />
              </template>
              <a-menu-item key="about-us">
                <router-link to="/about">
                  {{ t('components.nav.aboutUs') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="our-team">
                <router-link to="/about/our-team">
                  {{ t('components.nav.ourTeam') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="testimonials">
                <router-link to="/testimonials">
                  {{ t('components.nav.testimonials') }}
                </router-link>
              </a-menu-item>
              <a-menu-item key="areas-of-study">
                <router-link to="/about/areas-of-study">
                  {{ t('components.nav.areasOfStudy') }}
                </router-link>
              </a-menu-item>
            </a-sub-menu>
          </a-menu>
        </div>

        <!-- 联系信息区域 -->
        <div class="contact-info">
          <PhoneOutlined />
          <div class="contact-details">
            <div class="phone-number">{{ t('components.header.hotline') }}: ************</div>
            <div class="work-time">{{ t('components.header.workTime') }}</div>
          </div>
        </div>

        <!-- 右侧操作区 -->
        <div class="header-actions">
          <!-- 联系我们 -->
          <a-button type="default" @click="handleContact">
            {{ t('components.nav.contact') }}
          </a-button>

          <!-- 登录/注册 或 我的账户 -->
          <div v-if="!isLoggedIn">
            <a-button type="default" @click="handleLoginRegister" class="login-register-btn">
              {{ t('components.nav.loginRegister') }}
            </a-button>
          </div>
          <div v-else>
            <a-dropdown :trigger="['click']" placement="bottomRight">
              <a-button type="default" class="user-account-btn">
                <UserOutlined />
                {{ t('components.nav.myAccount') }}
                <DownOutlined />
              </a-button>
              <template #overlay>
                <a-menu @click="handleAccountMenuClick">
                  <a-menu-item key="orders">
                    <UserOutlined />
                    {{ t('components.nav.myOrders') }}
                  </a-menu-item>
                  <!-- <a-menu-item key="bills">
                    <FileTextOutlined />
                    {{ t('components.nav.myBills') }}
                  </a-menu-item>
                  <a-menu-item key="membership">
                    <CrownOutlined />
                    {{ t('components.nav.membership') }}
                  </a-menu-item>
                  <a-menu-item key="credits">
                    <DollarOutlined />
                    {{ t('components.nav.creditsAndPoints') }}
                  </a-menu-item>
                  <a-menu-item key="referrals">
                    <TeamOutlined />
                    {{ t('components.nav.referrals') }}
                  </a-menu-item>
                  <a-menu-divider /> -->
                  <a-menu-item key="settings">
                    <SettingOutlined />
                    {{ t('components.nav.settings') }}
                  </a-menu-item>
                  <!-- <a-menu-item key="help">
                    <QuestionCircleOutlined />
                    {{ t('components.nav.help') }}
                  </a-menu-item>
                  <a-menu-divider /> -->
                  <a-menu-item key="logout">
                    <LogoutOutlined />
                    {{ t('components.nav.logout') }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>

          <!-- 立即下单（主要CTA按钮） -->
          <a-button type="primary" size="large" @click="handlePlaceOrder">
            {{ t('components.nav.placeOrder') }}
          </a-button>
        </div>

        <!-- 语言切换 -->
        <div class="locale-picker-wrapper">
          <LocalePicker />
        </div>
      </div>
    </a-layout-header>
  </a-affix>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  AppstoreOutlined,
  BulbOutlined,
  DollarOutlined,
  PhoneOutlined,
  TeamOutlined,
  UserOutlined,
  DownOutlined,
  FileTextOutlined,
  CrownOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  LogoutOutlined,
} from '@ant-design/icons-vue'
import LocalePicker from '@/components/LocalePicker.vue'
import { useI18n } from '@/locales/useI18n'

import { getEnvConfig } from '@/utils/env'
import { useUserStore } from '@/store/modules/user'

const { t } = useI18n()
const { companyInfo } = getEnvConfig()
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 登录状态管理
const isLoggedIn = computed(() => userStore.getIsLoggedIn)

// 检查登录状态
const checkLoginStatus = () => {
  // 这里可以检查localStorage、sessionStorage或调用API
  const token = localStorage.getItem('userToken')
  isLoggedIn.value = !!token
}

// 初始化时检查登录状态
checkLoginStatus()

// 当前路由
const currentRoute = computed(() => {
  const routeName = route.name as string
  if (routeName === 'Home') return 'home'
  return 'home'
})

// 处理联系我们点击
const handleContact = () => {
  router.push('/contact-us')
}

// 处理登录/注册点击
const handleLoginRegister = () => {
  router.push('/login')
}

// 处理立即下单点击
const handlePlaceOrder = () => {
  // 这里可以跳转到下单页面或打开下单流程
  message.info(t('components.header.placeOrderMessage'))
}

// 处理账户菜单点击
const handleAccountMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'orders':
      router.push('/researcher')
      break
    case 'bills':
      message.info(t('components.nav.myBills') + ' - 功能开发中')
      break
    case 'membership':
      message.info(t('components.nav.membership') + ' - 功能开发中')
      break
    case 'credits':
      message.info(t('components.nav.creditsAndPoints') + ' - 功能开发中')
      break
    case 'referrals':
      message.info(t('components.nav.referrals') + ' - 功能开发中')
      break
    case 'settings':
      router.push('/account-settings')
      break
    case 'help':
      message.info(t('components.nav.help') + ' - 功能开发中')
      break
    case 'logout':
      handleLogout()
      break
    default:
      break
  }
}

// 处理退出登录
const handleLogout = () => {
  localStorage.removeItem('userToken')
  isLoggedIn.value = false
  message.success(t('components.nav.logoutSuccess'))
  router.push('/')
}
</script>

<style lang="less" scoped>
.app-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: auto;
  line-height: normal;
  position: relative;

  .header-content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    gap: 16px;
  }

  .logo-section {
    flex-shrink: 0;

    .logo-link {
      display: flex;
      align-items: center;
      gap: 12px;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
      }

      .logo-text {
        font-size: 20px;
        font-weight: 700;
        color: #1890ff;
        white-space: nowrap;
      }
    }
  }

  .nav-section {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 500px;
    min-width: 400px;

    .nav-menu {
      border-bottom: none;
      background: transparent;

      :deep(.ant-menu-item),
      :deep(.ant-menu-submenu) {
        margin: 0 8px;
        padding: 0 16px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f0f8ff;
        }

        a {
          display: flex;
          align-items: center;
          gap: 6px;
          color: #333;
          text-decoration: none;
          font-weight: 500;
          transition: color 0.3s ease;

          &:hover {
            color: #1890ff;
          }
        }

        &.ant-menu-item-selected,
        &.ant-menu-submenu-selected {
          background-color: #e6f7ff;

          a {
            color: #1890ff;
          }
        }
      }

      :deep(.ant-menu-submenu-title) {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #333;
        font-weight: 500;
        transition: color 0.3s ease;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .contact-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 13px;
    flex-shrink: 0;
    min-width: 160px;

    .contact-details {
      .phone-number {
        font-weight: 600;
        color: #333;
        white-space: nowrap;
      }

      .work-time {
        font-size: 12px;
        color: #999;
        white-space: nowrap;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    min-width: 280px;

    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;
      white-space: nowrap;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
      }
    }

    /* 登录注册按钮特殊样式 */
    .login-register-btn {
      border: 1px solid #667eea;
      color: #667eea;
      background: white;

      &:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
    }
  }

  .locale-picker-wrapper {
    flex-shrink: 0;
    min-width: 120px;
    display: flex;
    justify-content: flex-end;
  }

  .time-display {
    position: absolute;
    top: 100%;
    right: 24px;
    background: #fff;
    padding: 4px 12px;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;
    color: #666;
    z-index: 10;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .app-header {
    .header-content {
      padding: 0 16px;
      gap: 16px;
    }

    .contact-info {
      display: none;
    }

    .time-display {
      right: 16px;
    }
  }
}

@media (max-width: 992px) {
  .app-header {
    .header-content {
      gap: 12px;
    }

    .nav-section {
      max-width: 400px;

      .nav-menu {

        :deep(.ant-menu-item),
        :deep(.ant-menu-submenu) {
          margin: 0 4px;
          padding: 0 12px;
        }
      }
    }

    .header-actions {
      gap: 8px;

      .ant-btn {
        font-size: 13px;
        padding: 4px 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .app-header {
    .header-content {
      height: 64px;
      gap: 8px;
    }

    .logo-section .logo-link .logo-text {
      font-size: 16px;
    }

    .nav-section {
      display: none; // 在移动端隐藏导航菜单，可以后续添加移动端菜单
    }

    .header-actions {
      .ant-btn {
        font-size: 12px;
        padding: 4px 8px;

        &[size="large"] {
          font-size: 13px;
          padding: 6px 12px;
        }
      }
    }

    .time-display {
      display: none;
    }
  }
}

@media (max-width: 576px) {
  .app-header {
    .header-content {
      padding: 0 12px;
      height: 56px;
    }

    .logo-section .logo-link {
      gap: 8px;

      .logo-text {
        font-size: 14px;
      }
    }

    .header-actions {
      gap: 6px;

      .ant-btn {
        font-size: 11px;
        padding: 3px 6px;
        min-width: auto;

        &[size="large"] {
          font-size: 12px;
          padding: 4px 8px;
        }

        :deep(.anticon) {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
