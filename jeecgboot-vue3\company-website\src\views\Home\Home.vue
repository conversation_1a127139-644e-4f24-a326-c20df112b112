<template>
    <div class="home">
        <!-- 页头组件 -->
        <AppHeader />

        <!-- 横幅区域 -->
        <section v-if="!bannerClosed" class="banner-section">
            <div class="banner-content">
                <div class="banner-text">
                    <h3>{{ t('home.banner.title') }}</h3>
                    <p>{{ t('home.banner.description') }}</p>
                </div>
                <div class="banner-actions">
                    <a-button type="primary" @click="handleBannerClick">
                        {{ t('home.banner.buttonText') }}
                    </a-button>
                    <a-button type="text" @click="closeBanner" class="close-btn">
                        <CloseOutlined />
                    </a-button>
                </div>
            </div>
        </section>

        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>{{ t('home.hero.title') }}</h1>
                    <p class="hero-description">{{ t('home.hero.description') }}</p>
                    <div class="hero-highlight">
                        <ExclamationCircleOutlined />
                        <span>{{ t('home.hero.highlight') }}</span>
                    </div>
                    <div class="hero-actions">
                        <a-button type="primary" size="large" @click="handlePrimaryAction">
                            {{ t('home.hero.primaryButton') }}
                        </a-button>
                        <a-button size="large" @click="handleSecondaryAction">
                            {{ t('home.hero.secondaryButton') }}
                        </a-button>
                        <!-- 临时测试按钮 -->
                        <a-button type="default" size="large" @click="testAccountSettings">
                            测试账户设置
                        </a-button>
                    </div>
                </div>
                <div class="hero-image">
                    <img :src="heroData.image" :alt="t('home.hero.imageAlt')" @load="handleImageLoad"
                        @error="handleImageError" />
                </div>
            </div>
        </section>

        <!-- 权威认证区域 -->
        <section class="authority-section">
            <div class="authority-content">
                <h2>{{ t('home.authority.title') }}</h2>
                <div class="authority-logos">
                    <div v-for="authority in authorityData" :key="authority.id" class="authority-item">
                        <img :src="authority.logo" :alt="authority.alt" />
                    </div>
                </div>
            </div>
        </section>

        <!-- 统计数据区域 -->
        <section class="stats-section">
            <div class="stats-content">
                <div class="stat-item">
                    <div class="stat-value">3000+</div>
                    <div class="stat-label">{{ t('home.stats.journals') }}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">2000+</div>
                    <div class="stat-label">{{ t('home.stats.subjects') }}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">100万+</div>
                    <div class="stat-label">{{ t('home.stats.manuscripts') }}</div>
                </div>
            </div>
        </section>

        <!-- 核心服务展示 -->
        <section class="core-services-section">
            <div class="services-content">
                <div class="section-header">
                    <h2>{{ t('home.coreServices.title') }}</h2>
                    <p>{{ t('home.coreServices.description') }}</p>
                </div>
                <div class="services-grid">
                    <div v-for="service in coreServicesData" :key="service.id" class="service-card">
                        <div class="service-image">
                            <img :src="service.image" :alt="service.title" />
                        </div>
                        <div class="service-content">
                            <h3>{{ getServiceTitle(service.id, 'core') }}</h3>
                            <p>{{ getServiceDescription(service.id, 'core') }}</p>
                            <div class="service-price">{{ getServicePrice(service.id, 'core') }}</div>
                            <a-button type="primary" @click="handleServiceClick(service)">
                                {{ getServiceButtonText(service.id, 'core') }}
                            </a-button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 增值服务区域 -->
        <section class="value-services-section">
            <div class="services-content">
                <div class="section-header">
                    <h2>{{ t('home.valueServices.title') }}</h2>
                    <p>{{ t('home.valueServices.description') }}</p>
                </div>
                <div class="services-grid">
                    <div v-for="service in valueServicesData" :key="service.id" class="service-card">
                        <div class="service-image">
                            <img :src="service.image" :alt="service.title" />
                        </div>
                        <div class="service-content">
                            <h3>{{ getServiceTitle(service.id, 'value') }}</h3>
                            <p>{{ getServiceDescription(service.id, 'value') }}</p>
                            <div class="service-price">{{ getServicePrice(service.id, 'value') }}</div>
                            <a-button type="primary" @click="handleServiceClick(service)">
                                {{ getServiceButtonText(service.id, 'value') }}
                            </a-button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 客户感言区域 -->
        <section class="testimonials-section">
            <div class="testimonials-content">
                <div class="section-header">
                    <h2>{{ t('home.testimonials.title') }}</h2>
                    <p>{{ t('home.testimonials.description') }}</p>
                </div>
                <div class="testimonials-grid">
                    <div v-for="testimonial in localizedTestimonials" :key="testimonial.id" class="testimonial-card">
                        <div class="testimonial-content">
                            <p>{{ testimonial.content }}</p>
                        </div>
                        <div class="testimonial-author">
                            <img :src="testimonial.avatar" :alt="testimonial.name" />
                            <div class="author-info">
                                <h4>{{ testimonial.name }} {{ testimonial.title }}</h4>
                                <p>{{ testimonial.institution }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 编辑团队介绍 -->
        <section class="editor-team-section">
            <div class="team-content">
                <div class="section-header">
                    <h2>{{ t('home.editorTeam.title') }}</h2>
                    <p>{{ t('home.editorTeam.description') }}</p>
                    <div class="team-note">{{ t('home.editorTeam.note') }}</div>
                </div>
                <div class="editor-fields">
                    <div v-for="field in editorFieldsData" :key="field.id" class="field-tab"
                        :class="{ active: activeField === field.id }" @click="setActiveField(field.id)">
                        {{ getFieldName(field.id) }}
                    </div>
                </div>
                <div class="field-content">
                    <div v-if="activeField" class="field-info">
                        <h3>{{ getFieldContentTitle(activeField) }}</h3>
                        <p>{{ getFieldContentDescription(activeField) }}</p>
                        <a-button type="primary" @click="handleFieldClick">
                            {{ getFieldContentButtonText(activeField) }}
                        </a-button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系信息区域 -->
        <section class="contact-section">
            <div class="contact-content">
                <h2>{{ t('home.contact.title') }}</h2>
                <p>{{ t('home.contact.description') }}</p>
                <div class="contact-actions">
                    <a-button type="primary" size="large" @click="handleContactPrimary">
                        {{ t('home.contact.primaryButton') }}
                    </a-button>
                    <a-button size="large" @click="handleContactSecondary">
                        {{ t('home.contact.secondaryButton') }}
                    </a-button>
                </div>
            </div>
        </section>

        <!-- 关于公司区域 -->
        <section class="about-section">
            <div class="about-content">
                <h2>{{ t('home.about.title') }}</h2>
                <p>{{ t('home.about.description') }}</p>
            </div>
        </section>

        <!-- 页脚组件 -->
        <AppFooter />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { CloseOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
    bannerData,
    heroData,
    authorityData,
    statsData,
    coreServicesData,
    valueServicesData,
    testimonialsData,
    editorFieldsData,
    editorTeamData,
    contactData,
    aboutData,
    type CoreServiceItem,
    type ValueServiceItem
} from './home.data'

// 多语言支持
const { t } = useI18n()

// 响应式数据
const bannerClosed = ref(false)
const activeField = ref('clinical-medicine')

// 计算属性已移除，现在使用多语言函数直接获取内容

// 辅助函数
const getServiceTitle = (serviceId: string, type: 'core' | 'value') => {
    if (type === 'core') {
        const serviceMap: Record<string, string> = {
            '1': 'home.coreServices.services.standardEditing.title',
            '2': 'home.coreServices.services.premiumEditing.title',
            '3': 'home.coreServices.services.scientificReview.title',
            '4': 'home.coreServices.services.scientificEditing.title',
            '5': 'home.coreServices.services.translation.title'
        }
        return t(serviceMap[serviceId] || '')
    } else {
        const serviceMap: Record<string, string> = {
            '1': 'home.valueServices.services.presubmissionReview.title',
            '2': 'home.valueServices.services.journalSelection.title',
            '3': 'home.valueServices.services.formatting.title',
            '4': 'home.valueServices.services.videoAbstract.title',
            '5': 'home.valueServices.services.graphicalAbstract.title',
            '6': 'home.valueServices.services.tableFormatting.title'
        }
        return t(serviceMap[serviceId] || '')
    }
}

const getServiceDescription = (serviceId: string, type: 'core' | 'value') => {
    if (type === 'core') {
        const serviceMap: Record<string, string> = {
            '1': 'home.coreServices.services.standardEditing.description',
            '2': 'home.coreServices.services.premiumEditing.description',
            '3': 'home.coreServices.services.scientificReview.description',
            '4': 'home.coreServices.services.scientificEditing.description',
            '5': 'home.coreServices.services.translation.description'
        }
        return t(serviceMap[serviceId] || '')
    } else {
        const serviceMap: Record<string, string> = {
            '1': 'home.valueServices.services.presubmissionReview.description',
            '2': 'home.valueServices.services.journalSelection.description',
            '3': 'home.valueServices.services.formatting.description',
            '4': 'home.valueServices.services.videoAbstract.description',
            '5': 'home.valueServices.services.graphicalAbstract.description',
            '6': 'home.valueServices.services.tableFormatting.description'
        }
        return t(serviceMap[serviceId] || '')
    }
}

const getServiceButtonText = (serviceId: string, type: 'core' | 'value') => {
    if (type === 'core') {
        const serviceMap: Record<string, string> = {
            '1': 'home.coreServices.services.standardEditing.buttonText',
            '2': 'home.coreServices.services.premiumEditing.buttonText',
            '3': 'home.coreServices.services.scientificReview.buttonText',
            '4': 'home.coreServices.services.scientificEditing.buttonText',
            '5': 'home.coreServices.services.translation.buttonText'
        }
        return t(serviceMap[serviceId] || '')
    } else {
        const serviceMap: Record<string, string> = {
            '1': 'home.valueServices.services.presubmissionReview.buttonText',
            '2': 'home.valueServices.services.journalSelection.buttonText',
            '3': 'home.valueServices.services.formatting.buttonText',
            '4': 'home.valueServices.services.videoAbstract.buttonText',
            '5': 'home.valueServices.services.graphicalAbstract.buttonText',
            '6': 'home.valueServices.services.tableFormatting.buttonText'
        }
        return t(serviceMap[serviceId] || '')
    }
}

const getServicePrice = (serviceId: string, type: 'core' | 'value') => {
    if (type === 'core') {
        const serviceMap: Record<string, string> = {
            '1': 'home.coreServices.services.standardEditing.price',
            '2': 'home.coreServices.services.premiumEditing.price',
            '3': 'home.coreServices.services.scientificReview.price',
            '4': 'home.coreServices.services.scientificEditing.price',
            '5': 'home.coreServices.services.translation.price'
        }
        return t(serviceMap[serviceId] || '')
    } else {
        const serviceMap: Record<string, string> = {
            '1': 'home.valueServices.services.presubmissionReview.price',
            '2': 'home.valueServices.services.journalSelection.price',
            '3': 'home.valueServices.services.formatting.price',
            '4': 'home.valueServices.services.videoAbstract.price',
            '5': 'home.valueServices.services.graphicalAbstract.price',
            '6': 'home.valueServices.services.tableFormatting.price'
        }
        return t(serviceMap[serviceId] || '')
    }
}

const getFieldName = (fieldId: string) => {
    const fieldMap: Record<string, string> = {
        'clinical-medicine': 'home.editorTeam.fields.clinicalMedicine',
        'life-sciences': 'home.editorTeam.fields.lifeSciences',
        'physical-sciences': 'home.editorTeam.fields.physicalSciences',
        'engineering-materials': 'home.editorTeam.fields.engineeringMaterials',
        'business-law': 'home.editorTeam.fields.businessLaw',
        'humanities-social': 'home.editorTeam.fields.humanitiesSocial',
        'mathematics-computer': 'home.editorTeam.fields.mathematicsComputer'
    }
    return t(fieldMap[fieldId] || '')
}

const getFieldContentTitle = (fieldId: string) => {
    const fieldMap: Record<string, string> = {
        'clinical-medicine': 'home.editorTeam.fieldContent.clinicalMedicine.title',
        'life-sciences': 'home.editorTeam.fieldContent.lifeSciences.title',
        'physical-sciences': 'home.editorTeam.fieldContent.physicalSciences.title',
        'engineering-materials': 'home.editorTeam.fieldContent.engineeringMaterials.title',
        'business-law': 'home.editorTeam.fieldContent.businessLaw.title',
        'humanities-social': 'home.editorTeam.fieldContent.humanitiesSocial.title',
        'mathematics-computer': 'home.editorTeam.fieldContent.mathematicsComputer.title'
    }
    return t(fieldMap[fieldId] || '')
}

const getFieldContentDescription = (fieldId: string) => {
    const fieldMap: Record<string, string> = {
        'clinical-medicine': 'home.editorTeam.fieldContent.clinicalMedicine.description',
        'life-sciences': 'home.editorTeam.fieldContent.lifeSciences.description',
        'physical-sciences': 'home.editorTeam.fieldContent.physicalSciences.description',
        'engineering-materials': 'home.editorTeam.fieldContent.engineeringMaterials.description',
        'business-law': 'home.editorTeam.fieldContent.businessLaw.description',
        'humanities-social': 'home.editorTeam.fieldContent.humanitiesSocial.description',
        'mathematics-computer': 'home.editorTeam.fieldContent.mathematicsComputer.description'
    }
    return t(fieldMap[fieldId] || '')
}

const getFieldContentButtonText = (fieldId: string) => {
    const fieldMap: Record<string, string> = {
        'clinical-medicine': 'home.editorTeam.fieldContent.clinicalMedicine.buttonText',
        'life-sciences': 'home.editorTeam.fieldContent.lifeSciences.buttonText',
        'physical-sciences': 'home.editorTeam.fieldContent.physicalSciences.buttonText',
        'engineering-materials': 'home.editorTeam.fieldContent.engineeringMaterials.buttonText',
        'business-law': 'home.editorTeam.fieldContent.businessLaw.buttonText',
        'humanities-social': 'home.editorTeam.fieldContent.humanitiesSocial.buttonText',
        'mathematics-computer': 'home.editorTeam.fieldContent.mathematicsComputer.buttonText'
    }
    return t(fieldMap[fieldId] || '')
}

// 计算属性 - 客户感言数据
const localizedTestimonials = computed(() => {
    return testimonialsData.map((testimonial, index) => {
        const testimonialKey = `testimonial${index + 1}`;
        return {
            ...testimonial,
            name: t(`home.testimonials.items.${testimonialKey}.name`),
            title: t(`home.testimonials.items.${testimonialKey}.title`),
            institution: t(`home.testimonials.items.${testimonialKey}.institution`),
            content: t(`home.testimonials.items.${testimonialKey}.content`)
        }
    })
})

// 方法
const closeBanner = () => {
    bannerClosed.value = true
}

const handleBannerClick = () => {
    window.open(bannerData.buttonLink, '_blank')
}

const handlePrimaryAction = () => {
    window.open(heroData.buttons.primary.link, '_blank')
}

const handleSecondaryAction = () => {
    window.open(heroData.buttons.secondary.link, '_blank')
}

const handleServiceClick = (service: CoreServiceItem | ValueServiceItem) => {
    window.open(service.buttonLink, '_blank')
}

const setActiveField = (fieldId: string) => {
    activeField.value = fieldId
}

const handleFieldClick = () => {
    // 这里可以跳转到具体的研究领域页面
    const fieldUrls: Record<string, string> = {
        'clinical-medicine': '/about/areas-of-study/clinical-medicine',
        'life-sciences': '/about/areas-of-study/life-sciences',
        'physical-sciences': '/about/areas-of-study/physical-sciences',
        'engineering-materials': '/about/areas-of-study/engineering-materials',
        'business-law': '/about/areas-of-study/business-law',
        'humanities-social': '/about/areas-of-study/humanities-social',
        'mathematics-computer': '/about/areas-of-study/mathematics-computer'
    }
    const url = fieldUrls[activeField.value]
    if (url) {
        window.open(url, '_blank')
    }
}

const handleContactPrimary = () => {
    window.open(contactData.buttons.primary.link, '_blank')
}

const handleContactSecondary = () => {
    window.open(contactData.buttons.secondary.link, '_blank')
}

const handleImageLoad = () => {
    console.log('图片加载成功')
}

const handleImageError = () => {
    console.log('图片加载失败')
}

// 临时测试方法
const testAccountSettings = () => {
    // 模拟登录状态
    localStorage.setItem('userToken', 'test-token-' + Date.now())
    // 跳转到账户设置页面
    window.location.href = '/account-settings'
}

onMounted(() => {
    console.log('AJE Home 页面已加载')
})
</script>

<style scoped>
.aje-home {
    min-height: 100vh;
}

/* 横幅区域 */
.banner-section {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    padding: 16px 0;
}

.banner-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 24px;
}

.banner-text h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
}

.banner-text p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.5;
}

.banner-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.close-btn {
    color: white !important;
}

/* 英雄区域 */
.hero-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text h1 {
    font-size: 48px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 24px;
    line-height: 1.2;
}

.hero-description {
    font-size: 18px;
    color: #4a5568;
    margin-bottom: 24px;
    line-height: 1.6;
}

.hero-highlight {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    margin-bottom: 32px;
}

.hero-highlight span {
    color: #856404;
    font-weight: 500;
    line-height: 1.5;
}

.hero-actions {
    display: flex;
    gap: 16px;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 权威认证区域 */
.authority-section {
    padding: 60px 0;
    background: white;
}

.authority-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    text-align: center;
}

.authority-content h2 {
    font-size: 32px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 40px;
}

.authority-logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 32px;
    align-items: center;
}

.authority-item img {
    height: 100px;
    width: auto;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.authority-item:hover img {
    opacity: 1;
}

/* 统计数据区域 */
.stats-section {
    padding: 60px 0;
    background: #f7fafc;
}

.stats-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 48px;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 16px;
    color: #4a5568;
    font-weight: 500;
}

/* 服务区域通用样式 */
.core-services-section,
.value-services-section {
    padding: 80px 0;
}

.core-services-section {
    background: white;
}

.value-services-section {
    background: #f7fafc;
}

.services-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 36px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 16px;
}

.section-header p {
    font-size: 18px;
    color: #4a5568;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
}

.service-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.service-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.service-content {
    padding: 24px;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.service-content h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 12px;
    flex-shrink: 0;
}

.service-content p {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 16px;
    flex: 1;
}

.service-price {
    font-size: 18px;
    font-weight: 600;
    color: #ff6b35;
    margin-bottom: 16px;
    flex-shrink: 0;
}

.service-content .ant-btn {
    margin-top: auto;
    flex-shrink: 0;
}

/* 客户感言区域 */
.testimonials-section {
    padding: 80px 0;
    background: white;
}

.testimonials-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 32px;
}

.testimonial-card {
    background: #f7fafc;
    border-radius: 12px;
    padding: 32px;
    border-left: 4px solid #ff6b35;
}

.testimonial-content p {
    font-size: 16px;
    line-height: 1.6;
    color: #4a5568;
    margin-bottom: 24px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 16px;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin: 0 0 4px 0;
}

.author-info p {
    font-size: 14px;
    color: #718096;
    margin: 0;
}

/* 编辑团队区域 */
.editor-team-section {
    padding: 80px 0;
    background: #f7fafc;
}

.team-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.team-note {
    font-size: 16px;
    color: #718096;
    margin-top: 16px;
    font-style: italic;
}

.editor-fields {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 40px 0;
    justify-content: center;
}

.field-tab {
    padding: 12px 24px;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #4a5568;
}

.field-tab:hover {
    border-color: #ff6b35;
    color: #ff6b35;
}

.field-tab.active {
    background: #ff6b35;
    border-color: #ff6b35;
    color: white;
}

.field-content {
    background: white;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.field-info h3 {
    font-size: 24px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 16px;
}

.field-info p {
    font-size: 16px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 24px;
}

/* 联系信息区域 */
.contact-section {
    padding: 80px 0;
    background: white;
    text-align: center;
}

.contact-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 24px;
}

.contact-content h2 {
    font-size: 32px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 16px;
}

.contact-content p {
    font-size: 18px;
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 32px;
}

.contact-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

/* 关于公司区域 */
.about-section {
    padding: 80px 0 0 0;
    background: #1a202c;
    color: white;
    text-align: center;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 24px 80px 24px;
}

.about-content h2 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 24px;
}

.about-content p {
    font-size: 16px;
    line-height: 1.6;
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 36px;
    }

    .banner-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .stats-content {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .contact-actions {
        flex-direction: column;
        align-items: center;
    }

    .editor-fields {
        justify-content: flex-start;
    }

    .field-tab {
        font-size: 14px;
        padding: 10px 16px;
    }
}
</style>
