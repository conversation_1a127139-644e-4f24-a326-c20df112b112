<template>
  <div class="areas-of-study-page">
    <!-- 页头组件 -->
    <AppHeader />

    <!-- 横幅区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1>{{ t('areasOfStudy.hero.title') }}</h1>
          <p class="hero-description">{{ t('areasOfStudy.hero.description') }}</p>
        </div>
        <div class="hero-image">
          <img
            src="https://www.aje.cn/externalimages/aje-cms-production/8e74bbc6-fb0c-4f44-ada2-c5e0cf50a353_stackable+journal+cover+images+square.png?auto=compress,format&w=60"
            :alt="t('areasOfStudy.hero.imageAlt')" />
        </div>
      </div>
    </section>

    <!-- 学科分类展示 -->
    <section class="disciplines-section">
      <div class="services-content">
        <div class="disciplines-grid">
          <div class="discipline-card" v-for="discipline in disciplines" :key="discipline.key"
            @click="scrollToSection(discipline.key)">
            <div class="discipline-icon">
              <component :is="discipline.icon" />
            </div>
            <h3>{{ t(`areasOfStudy.disciplines.${discipline.key}.title`) }}</h3>
            <p>{{ t(`areasOfStudy.disciplines.${discipline.key}.description`) }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 详细学科内容 -->
    <section class="detailed-sections">
      <div class="services-content">
        <div v-for="discipline in disciplines" :key="discipline.key" :id="discipline.key"
          class="discipline-detail-section">
          <div class="discipline-header">
            <div class="discipline-title">
              <component :is="discipline.icon" class="title-icon" />
              <h2>{{ t(`areasOfStudy.disciplines.${discipline.key}.title`) }}</h2>
            </div>
            <p class="discipline-intro">{{ t(`areasOfStudy.disciplines.${discipline.key}.intro`) }}</p>
          </div>

          <div class="subspecialties-grid">
            <div v-for="subspecialty in discipline.subspecialties" :key="subspecialty" class="subspecialty-section">
              <h4>{{ t(`areasOfStudy.subspecialties.${subspecialty}.title`) }}</h4>
              <ul class="subspecialty-list">
                <li v-for="item in getSubspecialtyItems(subspecialty)" :key="item">
                  {{ t(`areasOfStudy.subspecialties.${subspecialty}.items.${item}`) }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 服务推荐 -->
    <section class="services-recommendation-section">
      <div class="services-content">
        <div class="section-header">
          <h2>{{ t('areasOfStudy.servicesRecommendation.title') }}</h2>
        </div>
        <div class="services-grid">
          <div class="service-card" v-for="service in recommendedServices" :key="service.key">
            <div class="service-image">
              <img :src="service.image" :alt="service.key" />
            </div>
            <div class="service-content">
              <h4>{{ t(`areasOfStudy.servicesRecommendation.services.${service.key}.title`) }}</h4>
              <p>{{ t(`areasOfStudy.servicesRecommendation.services.${service.key}.description`) }}</p>
              <div class="service-price">{{ t(`areasOfStudy.servicesRecommendation.services.${service.key}.price`) }}
              </div>
              <a-button type="primary" @click="handleServiceClick(service)">
                {{ t(`areasOfStudy.servicesRecommendation.services.${service.key}.buttonText`) }}
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="cta-content">
        <h2>{{ t('areasOfStudy.cta.title') }}</h2>
        <p>{{ t('areasOfStudy.cta.description') }}</p>
        <div class="cta-actions">
          <a-button type="primary" size="large" @click="handleOrderNow">
            {{ t('areasOfStudy.cta.orderNow') }}
          </a-button>
          <a-button size="large" @click="handleQuickQuote">
            {{ t('areasOfStudy.cta.quickQuote') }}
          </a-button>
        </div>
      </div>
    </section>

    <!-- 页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import {
  disciplines,
  recommendedServices,
  subspecialtyItems
} from './areasOfStudy.data'

// 多语言支持
const { t } = useI18n()
const router = useRouter()

// 方法
const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const getSubspecialtyItems = (subspecialty: string) => {
  return subspecialtyItems[subspecialty] || []
}

const handleServiceClick = (service: any) => {
  window.open(service.link, '_blank')
}

const handleOrderNow = () => {
  window.open('https://china.aje.com/cn/researcher/submit/get-started', '_blank')
}

const handleQuickQuote = () => {
  router.push('/pricing')
}

// 生命周期
onMounted(() => {
  console.log('AreasOfStudy 页面已加载')
})
</script>

<style scoped>
.areas-of-study-page {
  min-height: 100vh;
  background: #f8fafc;
}

/* 横幅区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 60px;
}

.hero-content {
  flex: 1;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  opacity: 0.9;
}

.hero-image {
  flex-shrink: 0;
}

.hero-image img {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: contain;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
}

/* 通用内容容器 */
.services-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 学科分类展示 */
.disciplines-section {
  padding: 80px 0;
  background: white;
}

.disciplines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.discipline-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 32px 24px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid transparent;
}

.discipline-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.discipline-icon {
  font-size: 48px;
  color: #667eea;
  margin-bottom: 16px;
}

.discipline-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.discipline-card p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

/* 详细学科内容 */
.detailed-sections {
  background: #f8fafc;
  padding: 80px 0;
}

.discipline-detail-section {
  margin-bottom: 80px;
  scroll-margin-top: 100px;
}

.discipline-detail-section:last-child {
  margin-bottom: 0;
}

.discipline-header {
  text-align: center;
  margin-bottom: 48px;
}

.discipline-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.title-icon {
  font-size: 36px;
  color: #667eea;
}

.discipline-header h2 {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.discipline-intro {
  font-size: 16px;
  color: #4a5568;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.subspecialties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.subspecialty-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.subspecialty-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.subspecialty-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.subspecialty-list li {
  padding: 8px 0;
  color: #4a5568;
  font-size: 14px;
  line-height: 1.4;
  border-bottom: 1px solid #f1f5f9;
}

.subspecialty-list li:last-child {
  border-bottom: none;
}

/* 服务推荐区域 */
.services-recommendation-section {
  padding: 80px 0;
  background: white;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.service-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.service-image {
  text-align: center;
  margin-bottom: 16px;
}

.service-image img {
  width: 264px;
  height: 264px;
  object-fit: contain;
}

.service-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 12px;
}

.service-content p {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
  margin-bottom: 16px;
  flex: 1;
}

.service-price {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 16px;
}

/* CTA区域 */
.cta-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.cta-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.cta-content h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
}

.cta-content p {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-container {
    flex-direction: column;
    text-align: center;
    gap: 40px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .hero-image img {
    width: 150px;
    height: 150px;
  }

  .disciplines-grid {
    grid-template-columns: 1fr;
  }

  .subspecialties-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .discipline-title {
    flex-direction: column;
    gap: 8px;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-actions .ant-btn {
    width: 200px;
  }
}
</style>
