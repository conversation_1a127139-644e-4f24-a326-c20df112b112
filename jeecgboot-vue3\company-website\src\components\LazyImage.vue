<template>
  <LazyContainer 
    :class="containerClass"
    :threshold="threshold"
    :direction="direction"
    @init="handleInit"
  >
    <div class="lazy-image-wrapper">
      <img 
        v-if="loaded"
        :src="src" 
        :alt="alt" 
        :class="imageClass"
        @load="handleImageLoad"
        @error="handleImageError"
      />
    </div>
    
    <template #skeleton>
      <div :class="skeletonClass">
        <div class="skeleton-image">
          <div class="skeleton-shimmer"></div>
          <span class="skeleton-text">{{ loadingText }}</span>
        </div>
      </div>
    </template>
  </LazyContainer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import LazyContainer from './LazyContainer.vue';

interface Props {
  src: string;
  alt?: string;
  threshold?: string;
  direction?: 'vertical' | 'horizontal';
  containerClass?: string;
  imageClass?: string;
  skeletonClass?: string;
  loadingText?: string;
}

interface Emits {
  (e: 'load', event: Event): void;
  (e: 'error', event: Event): void;
  (e: 'init'): void;
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  threshold: '50px',
  direction: 'vertical',
  containerClass: '',
  imageClass: '',
  skeletonClass: '',
  loadingText: '加载中...',
});

const emit = defineEmits<Emits>();

const loaded = ref(false);

const handleInit = () => {
  loaded.value = true;
  emit('init');
};

const handleImageLoad = (event: Event) => {
  emit('load', event);
};

const handleImageError = (event: Event) => {
  emit('error', event);
};
</script>

<style lang="less" scoped>
.lazy-image-wrapper {
  width: 100%;
  height: 100%;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
  }
}

.skeleton-image {
  width: 100%;
  height: 200px;
  background: #f5f5f5;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .skeleton-shimmer {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.6),
      transparent
    );
    animation: shimmer 1.5s infinite;
  }
  
  .skeleton-text {
    color: #999;
    font-size: 14px;
    z-index: 1;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>
